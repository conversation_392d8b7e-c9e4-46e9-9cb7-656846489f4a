import 'dart:io';

void main() async {
  final path =
      '/home/<USER>/Documents/Mstr/bidtrakr/lib/features/spare_parts/presentation/widgets/current_part_statistics_widget.dart';

  final files = <File>[];
  final fileOrDir = FileSystemEntity.isDirectorySync(path)
      ? Directory(path)
      : File(path);

  if (fileOrDir is File) {
    if (fileOrDir.path.endsWith('.dart')) {
      files.add(fileOrDir);
    }
  } else if (fileOrDir is Directory) {
    files.addAll(
      fileOrDir
          .listSync(recursive: true)
          .whereType<File>()
          .where((f) => f.path.endsWith('.dart')),
    );
  }

  final regex = RegExp(r'\.withOpacity\(([\d.]+)\)');

  for (var file in files) {
    try {
      final content = file.readAsStringSync();
      final newContent = content.replaceAllMapped(regex, (match) {
        final opacity = double.parse(match.group(1)!);
        final alpha = (opacity * 255).round();
        return '.withAlpha($alpha)';
      });

      if (content != newContent) {
        print('\n📄 File: ${file.path}');
        print('--- Before ---');
        print(_extractChanges(content, regex));
        print('--- After ---');
        print(_extractChanges(newContent, RegExp(r'\.withAlpha\(\d+\)')));

        stdout.write('➡️  Save changes? (y/n): ');
        final input = stdin.readLineSync();
        if (input?.toLowerCase() == 'y') {
          file.writeAsStringSync(newContent);
          print('✅ Changes saved.');
        } else {
          print('❌ Changes canceled.');
        }
      } else {
        print('📁 No changes needed: ${file.path}');
      }
    } catch (e) {
      print('❌ Error processing ${file.path}: $e');
    }
  }
}

String _extractChanges(String content, RegExp pattern) {
  final lines = content.split('\n');
  final result = <String>[];

  for (var i = 0; i < lines.length; i++) {
    if (pattern.hasMatch(lines[i])) {
      result.add('Line ${i + 1}: ${lines[i].trim()}');
    }
  }

  return result.join('\n');
}

//dart run tools/replace_opacity.dart

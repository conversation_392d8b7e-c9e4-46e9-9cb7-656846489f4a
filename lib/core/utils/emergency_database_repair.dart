import 'package:flutter/foundation.dart';

import '../datasources/app_database.dart';

/// Emergency database repair utility for immediate UUID conflict resolution
class EmergencyDatabaseRepair {
  /// Run emergency repair on app startup if needed
  /// Call this from your main app initialization
  static Future<void> runEmergencyRepairIfNeeded(AppDatabase database) async {
    try {
      debugPrint('Checking if emergency database repair is needed...');
      
      // Check for UUID conflicts
      final issues = await database.validateDatabaseIntegrity();
      
      if (issues.isNotEmpty) {
        debugPrint('UUID conflicts detected. Running emergency repair...');
        debugPrint('Issues found: $issues');
        
        // Run the repair
        await database.repairDatabaseUuids();
        
        // Verify repair was successful
        final issuesAfterRepair = await database.validateDatabaseIntegrity();
        if (issuesAfterRepair.isEmpty) {
          debugPrint('Emergency database repair completed successfully');
        } else {
          debugPrint('Some issues remain after emergency repair: $issuesAfterRepair');
        }
      } else {
        debugPrint('No UUID conflicts found. Emergency repair not needed.');
      }
    } catch (e) {
      debugPrint('Error during emergency database repair: $e');
    }
  }
}

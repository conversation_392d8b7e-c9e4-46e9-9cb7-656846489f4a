import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';

import '../datasources/app_database.dart';

/// Helper class for debugging sync issues
class SyncDebugHelper {
  final AppDatabase _database;

  SyncDebugHelper(this._database);

  /// Print detailed information about the current database state
  Future<void> printDatabaseState() async {
    try {
      debugPrint('=== DATABASE STATE DEBUG ===');

      // Check integrity
      final issues = await _database.validateDatabaseIntegrity();
      debugPrint('Database integrity issues: $issues');

      // Check unsynced records
      final unsyncedIncome = await _database.getUnsyncedIncome();
      debugPrint('Unsynced income records: ${unsyncedIncome.length}');

      for (final record in unsyncedIncome) {
        debugPrint(
          '  - UUID: ${record.uuid}, Date: ${record.date}, Status: ${record.syncStatus}',
        );
      }

      // Check for duplicate UUIDs in local database
      final duplicateUuids = await _database.customSelect('''
        SELECT uuid, COUNT(*) as count 
        FROM income 
        WHERE uuid IS NOT NULL 
        GROUP BY uuid 
        HAVING COUNT(*) > 1
      ''').get();

      if (duplicateUuids.isNotEmpty) {
        debugPrint('Duplicate UUIDs found in local database:');
        for (final row in duplicateUuids) {
          debugPrint(
            '  - UUID: ${row.data['uuid']}, Count: ${row.data['count']}',
          );
        }
      } else {
        debugPrint('No duplicate UUIDs found in local database');
      }

      debugPrint('=== END DATABASE STATE DEBUG ===');
    } catch (e) {
      debugPrint('Error during database state debug: $e');
    }
  }

  /// Check if a specific UUID exists in the local database
  Future<void> checkUuidInLocalDatabase(String uuid) async {
    try {
      final records = await _database
          .customSelect(
            'SELECT * FROM income WHERE uuid = ?',
            variables: [Variable(uuid)],
          )
          .get();

      debugPrint('UUID $uuid in local database:');
      if (records.isEmpty) {
        debugPrint('  - Not found');
      } else {
        debugPrint('  - Found ${records.length} record(s)');
        for (final record in records) {
          debugPrint(
            '    ID: ${record.data['id']}, Date: ${record.data['date']}, Status: ${record.data['sync_status']}',
          );
        }
      }
    } catch (e) {
      debugPrint('Error checking UUID $uuid: $e');
    }
  }

  /// Get summary of all tables
  Future<void> printTableSummary() async {
    try {
      debugPrint('=== TABLE SUMMARY ===');

      final tables = [
        'income',
        'orders',
        'performance',
        'spare_parts',
        'spare_parts_history',
      ];

      for (final table in tables) {
        try {
          final totalCount = await _database
              .customSelect('SELECT COUNT(*) as count FROM $table')
              .getSingle();

          final unsyncedCount = await _database
              .customSelect(
                'SELECT COUNT(*) as count FROM $table WHERE sync_status = ?',
                variables: [Variable('pendingUpload')],
              )
              .getSingle();

          final duplicateCount = await _database.customSelect('''
            SELECT COUNT(*) as count FROM (
              SELECT uuid FROM $table 
              WHERE uuid IS NOT NULL 
              GROUP BY uuid 
              HAVING COUNT(*) > 1
            )
          ''').getSingle();

          debugPrint(
            '$table: Total=${totalCount.data['count']}, Unsynced=${unsyncedCount.data['count']}, Duplicates=${duplicateCount.data['count']}',
          );
        } catch (e) {
          debugPrint('$table: Error getting summary - $e');
        }
      }

      debugPrint('=== END TABLE SUMMARY ===');
    } catch (e) {
      debugPrint('Error during table summary: $e');
    }
  }
}

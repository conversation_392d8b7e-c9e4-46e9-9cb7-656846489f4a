import 'package:flutter/foundation.dart';

import '../datasources/app_database.dart';

/// Utility class for database repair operations
class DatabaseRepairUtility {
  final AppDatabase _database;

  /// Constructor
  DatabaseRepairUtility(this._database);

  /// Check if the database needs repair
  /// Returns true if UUID conflicts are detected
  Future<bool> needsRepair() async {
    try {
      final issues = await _database.validateDatabaseIntegrity();
      return issues.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking if database needs repair: $e');
      return false;
    }
  }

  /// Get detailed information about database issues
  /// Returns a human-readable description of problems found
  Future<String> getDatabaseIssuesReport() async {
    try {
      final issues = await _database.validateDatabaseIntegrity();
      
      if (issues.isEmpty) {
        return 'Database integrity check passed. No issues found.';
      }

      final buffer = StringBuffer();
      buffer.writeln('Database integrity issues found:');
      buffer.writeln();

      for (final entry in issues.entries) {
        final tableName = entry.key;
        final tableIssues = entry.value;
        
        buffer.writeln('Table: $tableName');
        for (final issue in tableIssues) {
          buffer.writeln('  - $issue');
        }
        buffer.writeln();
      }

      buffer.writeln('Recommendation: Run database repair to fix these issues.');
      return buffer.toString();
    } catch (e) {
      return 'Error generating database issues report: $e';
    }
  }

  /// Repair the database by fixing UUID conflicts
  /// Returns true if repair was successful
  Future<bool> repairDatabase() async {
    try {
      debugPrint('Starting database repair...');
      
      // First, validate what issues exist
      final issuesBefore = await _database.validateDatabaseIntegrity();
      if (issuesBefore.isEmpty) {
        debugPrint('No issues found, repair not needed');
        return true;
      }

      debugPrint('Issues found before repair: $issuesBefore');

      // Run the repair
      await _database.repairDatabaseUuids();

      // Validate that issues are fixed
      final issuesAfter = await _database.validateDatabaseIntegrity();
      if (issuesAfter.isEmpty) {
        debugPrint('Database repair completed successfully');
        return true;
      } else {
        debugPrint('Some issues remain after repair: $issuesAfter');
        return false;
      }
    } catch (e) {
      debugPrint('Error during database repair: $e');
      return false;
    }
  }

  /// Check if sync errors are likely due to UUID conflicts
  /// This can be called when sync fails to determine if repair is needed
  static bool isSyncErrorDueToUuidConflict(dynamic error) {
    if (error == null) return false;
    
    final errorString = error.toString().toLowerCase();
    return errorString.contains('duplicate key value violates unique constraint') &&
           errorString.contains('uuid');
  }

  /// Get a user-friendly error message for UUID conflicts
  static String getUuidConflictErrorMessage() {
    return '''
Sync failed due to UUID conflicts in your local database.

This can happen if your app was updated from an older version that had a database migration issue.

To fix this:
1. Go to Settings > Database
2. Tap "Repair Database"
3. Try syncing again

Your data will not be lost during the repair process.
''';
  }
}

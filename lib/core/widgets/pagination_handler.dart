import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../models/paginated_result.dart';

/// Generic widget that handles pagination logic and UI for any entity type
///
/// This widget provides a standardized way to display paginated lists with
/// loading indicators, error states, and empty states. It can be used with
/// any entity type and provider that returns `PaginatedResult<T>`.
///
/// Type Parameters:
/// - [T]: The entity type (e.g., Income, Order, Performance)
///
/// Usage:
/// ```dart
/// PaginationHandler<Order>(
///   paginatedProvider: paginatedOrderListProvider,
///   entityName: 'Order',
///   entityIcon: Icons.assignment,
///   buildListItem: (order) => OrderListItem(order: order),
///   onLoadNextPage: () => ref.read(paginatedOrderListProvider.notifier).loadNextPage(),
/// )
/// ```
class PaginationHandler<T> extends ConsumerWidget {
  /// The provider that supplies the paginated data
  final ProviderListenable<AsyncValue<PaginatedResult<T>>> paginatedProvider;

  /// Human-readable name for the entity type (e.g., 'Order', 'Income')
  final String entityName;

  /// Icon representing the entity type
  final IconData entityIcon;

  /// Function to build a list item widget for each entity
  final Widget Function(T) buildListItem;

  /// Callback to load the next page of data
  final VoidCallback onLoadNextPage;

  const PaginationHandler({
    super.key,
    required this.paginatedProvider,
    required this.entityName,
    required this.entityIcon,
    required this.buildListItem,
    required this.onLoadNextPage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Optimize: Only rebuild when the actual data changes, not on loading state changes
    final paginatedDataAsync = ref.watch(
      paginatedProvider.select((asyncValue) {
        return asyncValue.whenOrNull(data: (data) => data);
      }),
    );

    // Handle the optimized data
    if (paginatedDataAsync != null) {
      return _buildPaginatedList(context, paginatedDataAsync);
    }

    // Handle error and loading states when data is null
    final fullAsyncValue = ref.watch(paginatedProvider);

    return fullAsyncValue.maybeWhen(
      error: (error, stack) => SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[400]),
              const SizedBox(height: 16),
              Text(
                'Error loading data',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.red[600]),
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
      orElse: () => const SliverToBoxAdapter(child: SizedBox.shrink()),
    );
  }

  /// Build paginated list with loading indicator
  Widget _buildPaginatedList(
    BuildContext context,
    PaginatedResult<T> paginatedResult,
  ) {
    final items = paginatedResult.items;
    final hasMorePages = paginatedResult.hasMorePages;

    if (items.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(entityIcon, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No ${entityName.toLowerCase()}s found',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                'Tap the + button to add your first ${entityName.toLowerCase()}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // If we're at the last item and there are more pages, show a loading indicator
          if (index == items.length) {
            return hasMorePages
                ? const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(child: CircularProgressIndicator()),
                  )
                : const SizedBox(
                    height: 40,
                  ); // Extra space at the end of the list
          }

          final item = items[index];
          return buildListItem(item);
        },
        // Add +1 for the loading indicator or extra space
        childCount: items.length + 1,
      ),
    );
  }
}

/// Generic mixin to handle pagination scroll logic for any entity type
///
/// This mixin provides standardized scroll handling for pagination. It automatically
/// triggers loading of the next page when the user scrolls near the bottom of the list.
///
/// Type Parameters:
/// - [T]: The widget type that extends ConsumerStatefulWidget
///
/// Usage:
/// ```dart
/// class OrdersScreenState extends BaseCrudScreenState<Order, OrderSummary, OrdersScreen>
///     with PaginationMixin<OrdersScreen> {
///
///   @override
///   void onLoadNextPage() {
///     ref.read(paginatedOrderListProvider.notifier).loadNextPage();
///   }
/// }
/// ```
mixin PaginationMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  late ScrollController scrollController;

  /// Callback to load the next page - must be implemented by concrete classes
  ///
  /// This method should trigger the loading of the next page of data.
  /// It will be called automatically when the user scrolls near the bottom.
  ///
  /// Example implementation:
  /// ```dart
  /// @override
  /// void onLoadNextPage() {
  ///   ref.read(paginatedOrderListProvider.notifier).loadNextPage();
  /// }
  /// ```
  void onLoadNextPage();

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.dispose();
  }

  /// Called when the user scrolls - handles pagination
  ///
  /// This method checks if the user has scrolled near the bottom of the list
  /// and triggers loading of the next page if so. The threshold is set to
  /// 200 pixels from the bottom to provide a smooth user experience.
  void _onScroll() {
    // Check if we're at the bottom of the list
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      // Load more data when we're near the end
      onLoadNextPage();
    }
  }
}

/// Standardized error container component with specialized variants
///
/// This component provides consistent error display across all screens
/// with support for different error types and customization options.
///
/// Usage:
/// ```dart
/// // Basic error container
/// AppErrorContainer(
///   error: error,
///   onRetry: () => _retryOperation(),
/// )
///
/// // Network error variant
/// NetworkErrorContainer(
///   error: error,
///   onRetry: () => _retryNetworkOperation(),
/// )
///
/// // Validation error variant
/// ValidationErrorContainer(
///   error: error,
///   customMessage: 'Please check your input',
/// )
/// ```
library;

import 'package:flutter/material.dart';

import '../theme/app_colors.dart';

/// A standardized error container component
///
/// This widget provides consistent error display across the application
/// with support for retry functionality, custom messages, and different
/// display modes.
class AppErrorContainer extends StatelessWidget {
  /// The error object to display
  final Object error;

  /// Callback for retry action
  final VoidCallback? onRetry;

  /// Custom error message to display instead of error.toString()
  final String? customMessage;

  /// Whether to use compact layout
  final bool compact;

  /// Icon to display with the error
  final IconData icon;

  /// Color for the error icon and text
  final Color? errorColor;

  /// Background color for the container
  final Color? backgroundColor;

  /// Border color for the container
  final Color? borderColor;

  /// Text for the retry button
  final String retryButtonText;

  /// Whether to show the retry button
  final bool showRetryButton;

  /// Padding for the container content
  final EdgeInsets? padding;

  /// Border radius for the container
  final double borderRadius;

  /// Creates an error container with full customization
  const AppErrorContainer({
    super.key,
    required this.error,
    this.onRetry,
    this.customMessage,
    this.compact = false,
    this.icon = Icons.error_outline,
    this.errorColor,
    this.backgroundColor,
    this.borderColor,
    this.retryButtonText = 'Retry',
    this.showRetryButton = true,
    this.padding,
    this.borderRadius = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveErrorColor = errorColor ?? AppColors.error;
    final effectiveBackgroundColor = backgroundColor ?? effectiveErrorColor.withAlpha(30);
    final effectiveBorderColor = borderColor ?? effectiveErrorColor.withAlpha(76);

    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(color: effectiveBorderColor),
      ),
      child: compact ? _buildCompactContent(context, theme, effectiveErrorColor) 
                    : _buildStandardContent(context, theme, effectiveErrorColor),
    );
  }

  Widget _buildCompactContent(BuildContext context, ThemeData theme, Color effectiveErrorColor) {
    return Row(
      children: [
        Icon(icon, color: effectiveErrorColor, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            customMessage ?? 'Error: $error',
            style: theme.textTheme.bodyMedium?.copyWith(color: effectiveErrorColor),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (showRetryButton && onRetry != null) ...[
          const SizedBox(width: 8),
          _buildRetryButton(context, effectiveErrorColor, compact: true),
        ],
      ],
    );
  }

  Widget _buildStandardContent(BuildContext context, ThemeData theme, Color effectiveErrorColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: effectiveErrorColor, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                customMessage ?? 'Error: $error',
                style: theme.textTheme.bodyMedium?.copyWith(color: effectiveErrorColor),
              ),
            ),
          ],
        ),
        if (showRetryButton && onRetry != null) ...[
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.centerRight,
            child: _buildRetryButton(context, effectiveErrorColor),
          ),
        ],
      ],
    );
  }

  Widget _buildRetryButton(BuildContext context, Color effectiveErrorColor, {bool compact = false}) {
    if (compact) {
      return IconButton(
        onPressed: onRetry,
        icon: const Icon(Icons.refresh),
        iconSize: 16,
        color: effectiveErrorColor,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
        tooltip: retryButtonText,
      );
    }

    return TextButton.icon(
      onPressed: onRetry,
      icon: const Icon(Icons.refresh, size: 16),
      label: Text(retryButtonText),
      style: TextButton.styleFrom(
        foregroundColor: effectiveErrorColor,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      ),
    );
  }
}

/// Specialized error container for network-related errors
class NetworkErrorContainer extends StatelessWidget {
  /// The error object to display
  final Object error;

  /// Callback for retry action
  final VoidCallback? onRetry;

  /// Custom error message to display
  final String? customMessage;

  /// Whether to use compact layout
  final bool compact;

  /// Creates a network error container
  const NetworkErrorContainer({
    super.key,
    required this.error,
    this.onRetry,
    this.customMessage,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppErrorContainer(
      error: error,
      onRetry: onRetry,
      customMessage: customMessage ?? 'Network error occurred. Please check your connection.',
      compact: compact,
      icon: Icons.wifi_off,
      errorColor: AppColors.warning,
      retryButtonText: 'Retry Connection',
    );
  }
}

/// Specialized error container for validation-related errors
class ValidationErrorContainer extends StatelessWidget {
  /// The error object to display
  final Object error;

  /// Custom error message to display
  final String? customMessage;

  /// Whether to use compact layout
  final bool compact;

  /// Callback for clearing the error
  final VoidCallback? onClear;

  /// Creates a validation error container
  const ValidationErrorContainer({
    super.key,
    required this.error,
    this.customMessage,
    this.compact = false,
    this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return AppErrorContainer(
      error: error,
      onRetry: onClear,
      customMessage: customMessage ?? 'Please check your input and try again.',
      compact: compact,
      icon: Icons.warning_amber,
      errorColor: AppColors.warning,
      retryButtonText: 'Clear',
      showRetryButton: onClear != null,
    );
  }
}

/// Specialized error container for general application errors
class GeneralErrorContainer extends StatelessWidget {
  /// The error object to display
  final Object error;

  /// Callback for retry action
  final VoidCallback? onRetry;

  /// Custom error message to display
  final String? customMessage;

  /// Whether to use compact layout
  final bool compact;

  /// Creates a general error container
  const GeneralErrorContainer({
    super.key,
    required this.error,
    this.onRetry,
    this.customMessage,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppErrorContainer(
      error: error,
      onRetry: onRetry,
      customMessage: customMessage ?? 'An unexpected error occurred.',
      compact: compact,
      icon: Icons.error_outline,
      errorColor: AppColors.error,
      retryButtonText: 'Try Again',
    );
  }
}

/// Standardized delete confirmation dialog component
///
/// This component provides consistent delete confirmation dialogs across
/// all screens with customizable content and Material Design styling.
///
/// Usage:
/// ```dart
/// // Basic delete confirmation
/// final confirmed = await AppDeleteConfirmationDialog.show(
///   context: context,
///   title: 'Delete Item',
///   message: 'Are you sure you want to delete this item?',
/// );
///
/// if (confirmed == true) {
///   // Perform delete operation
/// }
///
/// // Custom button text
/// final confirmed = await AppDeleteConfirmationDialog.show(
///   context: context,
///   title: 'Remove Record',
///   message: 'This action cannot be undone.',
///   confirmButtonText: 'Remove',
///   cancelButtonText: 'Keep',
/// );
/// ```
library;

import 'package:flutter/material.dart';

import '../theme/app_colors.dart';

/// A standardized delete confirmation dialog
///
/// This widget provides consistent delete confirmation dialogs across
/// the application following Material Design guidelines. It returns:
/// - `true` if the user confirms the deletion
/// - `false` if the user cancels the deletion
/// - `null` if the dialog is dismissed without action
class AppDeleteConfirmationDialog extends StatelessWidget {
  /// The title of the dialog
  final String title;

  /// The message content of the dialog
  final String message;

  /// Text for the confirm button
  final String confirmButtonText;

  /// Text for the cancel button
  final String cancelButtonText;

  /// Icon to display in the dialog
  final IconData? icon;

  /// Whether to show a warning icon
  final bool showWarningIcon;

  /// Color for the confirm button
  final Color? confirmButtonColor;

  /// Whether to show additional warning text
  final bool showWarningText;

  /// Additional warning text to display
  final String? warningText;

  /// Creates a delete confirmation dialog
  const AppDeleteConfirmationDialog._({
    required this.title,
    required this.message,
    this.confirmButtonText = 'Delete',
    this.cancelButtonText = 'Cancel',
    this.icon,
    this.showWarningIcon = true,
    this.confirmButtonColor,
    this.showWarningText = false,
    this.warningText,
  });

  /// Shows a delete confirmation dialog
  ///
  /// Returns:
  /// - `true` if the user confirms the deletion
  /// - `false` if the user cancels the deletion
  /// - `null` if the dialog is dismissed without action
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    String confirmButtonText = 'Delete',
    String cancelButtonText = 'Cancel',
    IconData? icon,
    bool showWarningIcon = true,
    Color? confirmButtonColor,
    bool showWarningText = false,
    String? warningText,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (context) => AppDeleteConfirmationDialog._(
        title: title,
        message: message,
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        icon: icon,
        showWarningIcon: showWarningIcon,
        confirmButtonColor: confirmButtonColor,
        showWarningText: showWarningText,
        warningText: warningText,
      ),
    );
  }

  /// Shows a standard delete confirmation dialog for records
  ///
  /// This is a convenience method for common delete operations.
  static Future<bool?> showForRecord({
    required BuildContext context,
    required String recordType,
    required String recordIdentifier,
    String? customMessage,
  }) {
    final message =
        customMessage ??
        'Are you sure you want to delete the $recordType for $recordIdentifier?';

    return show(
      context: context,
      title: 'Delete $recordType',
      message: message,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
    );
  }

  /// Shows a destructive action confirmation dialog
  ///
  /// This is for actions that cannot be undone.
  static Future<bool?> showDestructiveAction({
    required BuildContext context,
    required String title,
    required String message,
    String confirmButtonText = 'Delete',
    String cancelButtonText = 'Cancel',
  }) {
    return show(
      context: context,
      title: title,
      message: message,
      confirmButtonText: confirmButtonText,
      cancelButtonText: cancelButtonText,
      showWarningText: true,
      warningText: 'This action cannot be undone.',
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveConfirmButtonColor = confirmButtonColor ?? AppColors.error;

    return AlertDialog(
      title: _buildTitle(context, theme),
      content: _buildContent(context, theme),
      actions: _buildActions(context, theme, effectiveConfirmButtonColor),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
      contentPadding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
      actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
    );
  }

  Widget _buildTitle(BuildContext context, ThemeData theme) {
    if (showWarningIcon && icon == null) {
      return Row(
        children: [
          Icon(Icons.warning_amber, color: AppColors.warning, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      );
    } else if (icon != null) {
      return Row(
        children: [
          Icon(icon, color: AppColors.error, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      );
    } else {
      return Text(
        title,
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      );
    }
  }

  Widget _buildContent(BuildContext context, ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(message, style: theme.textTheme.bodyMedium),
        if (showWarningText && warningText != null) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.warning.withAlpha(30),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.warning.withAlpha(76),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: AppColors.warning, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    warningText!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  List<Widget> _buildActions(
    BuildContext context,
    ThemeData theme,
    Color confirmButtonColor,
  ) {
    return [
      TextButton(
        onPressed: () => Navigator.of(context).pop(false),
        child: Text(
          cancelButtonText,
          style: TextStyle(
            color: theme.colorScheme.onSurface.withAlpha(153), // 60% opacity
          ),
        ),
      ),
      TextButton(
        onPressed: () => Navigator.of(context).pop(true),
        style: TextButton.styleFrom(foregroundColor: confirmButtonColor),
        child: Text(
          confirmButtonText,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: confirmButtonColor,
          ),
        ),
      ),
    ];
  }
}

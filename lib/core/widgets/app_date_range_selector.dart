/// Standardized date range selector component with factory constructors
///
/// This component provides a consistent date range selection interface
/// across all screens with specialized factory constructors for common
/// use cases like app bars and forms.
///
/// Usage:
/// ```dart
/// // For app bar usage
/// AppDateRangeSelector.forAppBar(
///   dateRange: dateRange,
///   onDateRangeSelected: (newRange) => ref
///       .read(globalDateRangeProvider.notifier)
///       .setDateRange(newRange),
/// )
///
/// // For form usage
/// AppDateRangeSelector.forForm(
///   dateRange: dateRange,
///   onDateRangeSelected: onDateRangeSelected,
///   label: 'Select Date Range',
/// )
/// ```
library;

import 'package:flutter/material.dart';

import '../theme/app_colors.dart';
import '../utils/date_helper.dart';

/// A standardized date range selector component
///
/// This widget provides a consistent interface for date range selection
/// across the application. It includes factory constructors for common
/// use cases and follows the app's design system.
class AppDateRangeSelector extends StatefulWidget {
  /// The currently selected date range
  final DateTimeRange dateRange;

  /// Callback when a new date range is selected
  final ValueChanged<DateTimeRange> onDateRangeSelected;

  /// Whether the selector is enabled
  final bool enabled;

  /// Label text to display
  final String? label;

  /// Whether to show the label
  final bool showLabel;

  /// Background color override
  final Color? backgroundColor;

  /// Text color override
  final Color? textColor;

  /// Border radius
  final double borderRadius;

  /// Padding around the content
  final EdgeInsets? padding;

  /// Whether to use compact layout
  final bool compact;

  /// Icon to display
  final IconData? icon;

  /// Whether to show the icon
  final bool showIcon;

  /// Border color override
  final Color? borderColor;

  /// The earliest date that can be selected
  final DateTime? firstDate;

  /// The latest date that can be selected
  final DateTime? lastDate;

  /// Creates a date range selector with full customization
  const AppDateRangeSelector({
    super.key,
    required this.dateRange,
    required this.onDateRangeSelected,
    this.enabled = true,
    this.label,
    this.showLabel = false,
    this.backgroundColor,
    this.textColor,
    this.borderRadius = 8.0,
    this.padding,
    this.compact = false,
    this.icon,
    this.showIcon = true,
    this.borderColor,
    this.firstDate,
    this.lastDate,
  });

  /// Factory constructor for app bar usage
  ///
  /// Creates a date range selector optimized for use in app bars
  /// with appropriate styling and compact layout.
  factory AppDateRangeSelector.forAppBar({
    required DateTimeRange dateRange,
    required ValueChanged<DateTimeRange> onDateRangeSelected,
    bool enabled = true,
    DateTime? firstDate,
    DateTime? lastDate,
  }) {
    return AppDateRangeSelector(
      dateRange: dateRange,
      onDateRangeSelected: onDateRangeSelected,
      enabled: enabled,
      backgroundColor: Colors.white.withAlpha(30),
      textColor: Colors.white,
      borderColor: Colors.white.withAlpha(77),
      compact: true,
      icon: Icons.calendar_month,
      showIcon: true,
      showLabel: false,
      borderRadius: 8.0,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      firstDate: firstDate,
      lastDate: lastDate,
    );
  }

  /// Factory constructor for form usage
  ///
  /// Creates a date range selector optimized for use in forms
  /// with label support and standard form styling.
  factory AppDateRangeSelector.forForm({
    required DateTimeRange dateRange,
    required ValueChanged<DateTimeRange> onDateRangeSelected,
    String? label,
    bool enabled = true,
    DateTime? firstDate,
    DateTime? lastDate,
  }) {
    return AppDateRangeSelector(
      dateRange: dateRange,
      onDateRangeSelected: onDateRangeSelected,
      enabled: enabled,
      label: label,
      showLabel: label != null,
      icon: Icons.calendar_month,
      showIcon: true,
      compact: false,
      borderRadius: 8.0,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      firstDate: firstDate,
      lastDate: lastDate,
    );
  }

  @override
  State<AppDateRangeSelector> createState() => _AppDateRangeSelectorState();
}

class _AppDateRangeSelectorState extends State<AppDateRangeSelector> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.enabled ? () => _selectDateRange(context) : null,
      borderRadius: BorderRadius.circular(widget.borderRadius),
      child: Container(
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: Border.all(
            color: widget.borderColor ?? 
                   AppColors.primary.withAlpha(77), // 0.3 * 255 = ~77
          ),
        ),
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (widget.compact) {
      return _buildCompactContent(context);
    } else {
      return _buildStandardContent(context);
    }
  }

  Widget _buildCompactContent(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            DateHelper.formatDateRange(widget.dateRange.start, widget.dateRange.end),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (widget.showIcon) ...[
          const SizedBox(width: 8),
          Icon(
            widget.icon ?? Icons.calendar_month,
            color: widget.textColor,
            size: 18,
          ),
        ],
      ],
    );
  }

  Widget _buildStandardContent(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (widget.showLabel && widget.label != null) ...[
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: widget.textColor,
            ),
          ),
          const SizedBox(width: 8),
        ],
        Expanded(
          child: Text(
            DateHelper.formatDateRange(widget.dateRange.start, widget.dateRange.end),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: widget.textColor,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (widget.showIcon) ...[
          const SizedBox(width: 8),
          Icon(
            widget.icon ?? Icons.calendar_month,
            color: widget.textColor,
          ),
        ],
      ],
    );
  }

  Future<void> _selectDateRange(BuildContext context) async {
    // Ensure we're using local dates for the picker
    final localStart = DateHelper.ensureLocal(widget.dateRange.start);
    final localEnd = DateHelper.ensureLocal(widget.dateRange.end);
    final localDateRange = DateTimeRange(start: localStart, end: localEnd);

    final newDateRange = await showDateRangePicker(
      context: context,
      firstDate: widget.firstDate ?? DateTime(2020),
      lastDate: widget.lastDate ?? DateTime(2030),
      initialDateRange: localDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      // Preserve the exact dates selected by the user without timezone shifts
      // We'll normalize these in the parent widget to ensure consistent behavior
      widget.onDateRangeSelected(newDateRange);
    }
  }
}

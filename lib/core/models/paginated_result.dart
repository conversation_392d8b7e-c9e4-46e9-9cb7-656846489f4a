/// Generic class to hold paginated results for any entity type
///
/// This class provides a standardized way to handle paginated data across
/// all features in the application. It supports accumulating items across
/// multiple pages and provides methods for appending new pages and creating
/// fresh results.
///
/// Type Parameters:
/// - [T]: The entity type (e.g., Income, Order, Performance)
class PaginatedResult<T> {
  /// Accumulated list of all loaded items across pages
  final List<T> items;
  
  /// Current page number (1-based)
  final int currentPage;
  
  /// Whether there are more pages available to load
  final bool hasMorePages;

  const PaginatedResult({
    required this.items,
    required this.currentPage,
    required this.hasMorePages,
  });

  /// Creates a new result by appending new items to the existing list
  ///
  /// This method is used when loading additional pages to accumulate
  /// items from multiple pages into a single list.
  ///
  /// Parameters:
  /// - [newItems]: The items from the new page to append
  /// - [newCurrentPage]: The new current page number
  /// - [newHasMorePages]: Whether there are more pages after this one
  ///
  /// Returns:
  /// A new [PaginatedResult] with the combined items
  PaginatedResult<T> appendPage({
    required List<T> newItems,
    required int newCurrentPage,
    required bool newHasMorePages,
  }) {
    return PaginatedResult<T>(
      items: [...items, ...newItems],
      currentPage: newCurrentPage,
      hasMorePages: newHasMorePages,
    );
  }

  /// Creates a new result with fresh data (used for refresh)
  ///
  /// This static method is used when refreshing data or loading the first page.
  /// It creates a completely new result without accumulating previous data.
  ///
  /// Parameters:
  /// - [items]: The items for this result
  /// - [currentPage]: The current page number
  /// - [hasMorePages]: Whether there are more pages available
  ///
  /// Returns:
  /// A new [PaginatedResult] with the provided data
  static PaginatedResult<T> fresh<T>({
    required List<T> items,
    required int currentPage,
    required bool hasMorePages,
  }) {
    return PaginatedResult<T>(
      items: items,
      currentPage: currentPage,
      hasMorePages: hasMorePages,
    );
  }

  /// Creates an empty result
  ///
  /// This factory constructor creates an empty paginated result,
  /// useful for initial states or error conditions.
  ///
  /// Returns:
  /// An empty [PaginatedResult] with no items and no more pages
  static PaginatedResult<T> empty<T>() {
    return PaginatedResult<T>(
      items: [],
      currentPage: 1,
      hasMorePages: false,
    );
  }

  /// Returns the total number of items currently loaded
  int get totalItems => items.length;

  /// Returns whether the result is empty (no items loaded)
  bool get isEmpty => items.isEmpty;

  /// Returns whether the result has items
  bool get isNotEmpty => items.isNotEmpty;

  @override
  String toString() {
    return 'PaginatedResult<$T>(items: ${items.length}, currentPage: $currentPage, hasMorePages: $hasMorePages)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is PaginatedResult<T> &&
        other.items.length == items.length &&
        other.currentPage == currentPage &&
        other.hasMorePages == hasMorePages;
  }

  @override
  int get hashCode {
    return items.length.hashCode ^
        currentPage.hashCode ^
        hasMorePages.hashCode;
  }
}

import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

import '../repositories/app_settings_repository.dart';
import '../repositories/income_repository.dart';
import '../repositories/level_settings_repository.dart';
import '../repositories/orders_repository.dart';
import '../repositories/performance_repository.dart';
import '../repositories/spare_parts_history_repository.dart';
import '../repositories/spare_parts_repository.dart';
import '../version/database_version_manager.dart';
import 'tables/tables.dart';
import 'triggers.dart';

part 'app_database.g.dart';

@DriftDatabase(
  tables: [
    Income,
    Orders,
    Performance,
    LevelSettings,
    SpareParts,
    SparePartsHistory,
    AppSettings,
  ],
)
class AppDatabase extends _$AppDatabase {
  // Repository instances
  late final incomeRepo = IncomeRepository(this);
  late final ordersRepo = OrdersRepository(this);
  late final performanceRepo = PerformanceRepository(this);
  late final sparePartsRepo = SparePartsRepository(this);
  late final sparePartsHistoryRepo = SparePartsHistoryRepository(this);
  late final appSettingsRepo = AppSettingsRepository(this);
  late final levelSettingsRepo = LevelSettingsRepository(this);

  AppDatabase(String path) : super(_openConnection(path));

  @override
  int get schemaVersion => DatabaseVersionManager.getCurrentSchemaVersion();

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        // Create tables
        await m.createAll();

        // Create triggers
        await Triggers.createTriggers(executor);
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from == 1 || from == 2) {
          // For SQLite, we need to use a different approach for adding NOT NULL columns
          // First, add nullable columns
          try {
            await customStatement('ALTER TABLE income ADD COLUMN uuid TEXT');
            await customStatement(
              'ALTER TABLE income ADD COLUMN created_at TEXT',
            );
            await customStatement(
              'ALTER TABLE income ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE income ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE income ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in income table: $e');
          }

          try {
            await customStatement('ALTER TABLE orders ADD COLUMN uuid TEXT');
            await customStatement(
              'ALTER TABLE orders ADD COLUMN created_at TEXT',
            );
            await customStatement(
              'ALTER TABLE orders ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE orders ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE orders ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in orders table: $e');
          }

          try {
            await customStatement(
              'ALTER TABLE performance ADD COLUMN uuid TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN created_at TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE performance ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in performance table: $e');
          }

          try {
            await customStatement(
              'ALTER TABLE spare_parts ADD COLUMN uuid TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint('Columns might already exist in spare_parts table: $e');
          }

          try {
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN uuid TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN updated_at TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN deleted_at TEXT',
            );
            await customStatement(
              'ALTER TABLE spare_parts_history ADD COLUMN sync_status TEXT',
            );
          } catch (e) {
            debugPrint(
              'Columns might already exist in spare_parts_history table: $e',
            );
          }

          try {
            await m.addColumn(appSettings, appSettings.lastSyncTime);
          } catch (e) {
            debugPrint(
              'lastSyncTime column might already exist in appSettings table: $e',
            );
          }

          // Generate unique UUIDs for existing records and repair duplicate UUIDs
          final now = DateTime.now().toUtc().toIso8601String();

          // Fix income records
          await _fixTableUuids('income', [
            'created_at',
            'updated_at',
            'sync_status',
          ], now);

          // Fix orders records
          await _fixTableUuids('orders', [
            'created_at',
            'updated_at',
            'sync_status',
          ], now);

          // Fix performance records
          await _fixTableUuids('performance', [
            'created_at',
            'updated_at',
            'sync_status',
          ], now);

          // Fix spare_parts records (no created_at/updated_at columns)
          await _fixTableUuids('spare_parts', ['sync_status'], now);

          // Fix spare_parts_history records
          await _fixTableUuids('spare_parts_history', [
            'updated_at',
            'sync_status',
          ], now);

          // Create triggers
          await Triggers.createTriggers(executor);
        }
      },
      beforeOpen: (details) async {
        // Initialize with default settings if needed
        if (details.wasCreated) {
          await _initializeDefaultSettings();
        }

        // Check database compatibility
        if (!details.wasCreated &&
            details.hadUpgrade &&
            details.versionBefore != null) {
          await _checkDatabaseCompatibility(
            details.versionBefore!,
            details.versionNow,
          );
        }
      },
    );
  }

  Future<void> _initializeDefaultSettings() async {
    // Initialize default level settings using the repository
    await levelSettingsRepo.initializeDefaultSettings();

    // Initialize default app settings
    final now = DateTime.now().toUtc();
    final firstDayOfMonth = DateTime.utc(now.year, now.month, 1);
    final lastDayOfMonth = DateTime.utc(now.year, now.month + 1, 0);

    await appSettingsRepo.insertAppSettings(
      AppSettingsCompanion.insert(
        dateRangeStart: firstDayOfMonth,
        dateRangeEnd: lastDayOfMonth,
      ),
    );
  }

  /// Checks if the current app version is compatible with the database schema
  ///
  /// This method is called when the database is upgraded to ensure that
  /// the app can work with the new schema version.
  Future<void> _checkDatabaseCompatibility(
    int oldVersion,
    int newVersion,
  ) async {
    debugPrint('Database upgraded from schema $oldVersion to $newVersion');

    // Check if the current app version is compatible with the new schema
    if (!DatabaseVersionManager.isCompatibleWithSchema(newVersion)) {
      final minAppVersion = DatabaseVersionManager.getMinAppVersionForSchema(
        newVersion,
      );
      debugPrint(
        'WARNING: Current app version is not compatible with database schema $newVersion',
      );
      debugPrint('Minimum required app version: $minAppVersion');

      // Log this issue - in a real app, you might want to show a user-friendly message
      // or even prevent the app from continuing if the database is incompatible
    }
  }

  // Repository access methods - use repositories directly for database operations
  // Example: database.incomeRepo.getAll() instead of database.getAllIncome()

  // Essential delegate methods for backward compatibility
  // These methods are used throughout the codebase and delegate to repositories

  // Orders methods
  Future<List<Order>> getAllOrders() => ordersRepo.getAll();
  Future<int> insertOrder(OrdersCompanion entity) => ordersRepo.insert(entity);
  Future<bool> updateOrder(Order entity) => ordersRepo.update(entity);
  Future<bool> softDeleteOrder(int id) => ordersRepo.softDelete(id);

  // App Settings methods
  Future<AppSetting> getOrCreateAppSettings() =>
      appSettingsRepo.getOrCreateAppSettings();
  Future<bool> updateAppSettings(AppSettingsCompanion entity) =>
      appSettingsRepo.updateAppSettings(entity);
  Future<DateTime?> getLastSyncTime() => appSettingsRepo.getLastSyncTime();
  Future<void> updateLastSyncTime(DateTime time) =>
      appSettingsRepo.updateLastSyncTime(time);

  // Sync methods
  Future<List<IncomeData>> getUnsyncedIncome() =>
      incomeRepo.getUnsyncedRecords();
  Future<List<Order>> getUnsyncedOrders() => ordersRepo.getUnsyncedRecords();
  Future<List<PerformanceData>> getUnsyncedPerformance() =>
      performanceRepo.getUnsyncedRecords();
  Future<List<SparePart>> getUnsyncedSpareParts() =>
      sparePartsRepo.getUnsyncedRecords();
  Future<List<SparePartsHistoryData>> getUnsyncedSparePartsHistory() =>
      sparePartsHistoryRepo.getUnsyncedRecords();

  // Mark as synced methods
  Future<void> markIncomeAsSynced(String uuid) => incomeRepo.markAsSynced(uuid);
  Future<void> markOrderAsSynced(String uuid) => ordersRepo.markAsSynced(uuid);
  Future<void> markPerformanceAsSynced(String uuid) =>
      performanceRepo.markAsSynced(uuid);
  Future<void> markSparePartAsSynced(String uuid) =>
      sparePartsRepo.markAsSynced(uuid);
  Future<void> markSparePartHistoryAsSynced(String uuid) =>
      sparePartsHistoryRepo.markAsSynced(uuid);

  // Spare parts methods
  Future<List<SparePart>> getAllSpareParts() => sparePartsRepo.getAll();
  Future<int> insertSparePart(SparePartsCompanion entity) =>
      sparePartsRepo.insert(entity);
  Future<bool> updateSparePart(SparePart entity) =>
      sparePartsRepo.update(entity);
  Future<bool> softDeleteSparePart(int id) => sparePartsRepo.softDelete(id);
  Future<void> updateSparePartsMileage(int latestMileage) =>
      sparePartsRepo.updateSparePartsMileage(latestMileage);

  // Spare parts history methods
  Future<List<SparePartsHistoryData>> getHistoryForSparePart(int sparePartId) =>
      sparePartsHistoryRepo.getHistoryForSparePart(sparePartId);
  Future<int> insertSparePartHistory(SparePartsHistoryCompanion entity) =>
      sparePartsHistoryRepo.insert(entity);
  Future<bool> updateSparePartHistoryWithSync(SparePartsHistoryData entity) =>
      sparePartsHistoryRepo.updateWithSync(entity);
  Future<bool> softDeleteSparePartHistory(int id) =>
      sparePartsHistoryRepo.softDelete(id);

  /// Repair database by fixing UUID conflicts across all tables
  /// This method can be called to fix existing databases that have duplicate UUIDs
  Future<void> repairDatabaseUuids() async {
    debugPrint('Starting database UUID repair process');
    final now = DateTime.now().toUtc().toIso8601String();

    try {
      // Fix all tables that have UUID columns
      await _fixTableUuids('income', [
        'created_at',
        'updated_at',
        'sync_status',
      ], now);
      await _fixTableUuids('orders', [
        'created_at',
        'updated_at',
        'sync_status',
      ], now);
      await _fixTableUuids('performance', [
        'created_at',
        'updated_at',
        'sync_status',
      ], now);
      await _fixTableUuids('spare_parts', ['sync_status'], now);
      await _fixTableUuids('spare_parts_history', [
        'updated_at',
        'sync_status',
      ], now);

      debugPrint('Database UUID repair process completed successfully');
    } catch (e) {
      debugPrint('Error during database UUID repair: $e');
      rethrow;
    }
  }

  /// Validate database integrity by checking for UUID conflicts
  /// Returns a map of table names to lists of issues found
  Future<Map<String, List<String>>> validateDatabaseIntegrity() async {
    final issues = <String, List<String>>{};

    try {
      debugPrint('Starting database integrity validation');

      // Check each table for UUID issues
      final tables = [
        'income',
        'orders',
        'performance',
        'spare_parts',
        'spare_parts_history',
      ];

      for (final tableName in tables) {
        final tableIssues = <String>[];

        // Check for NULL UUIDs
        final nullUuidCount = await customSelect(
          'SELECT COUNT(*) as count FROM $tableName WHERE uuid IS NULL',
        ).getSingle();

        if (nullUuidCount.data['count'] > 0) {
          tableIssues.add(
            '${nullUuidCount.data['count']} records with NULL UUIDs',
          );
        }

        // Check for duplicate UUIDs
        final duplicateUuids = await customSelect('''
          SELECT uuid, COUNT(*) as count
          FROM $tableName
          WHERE uuid IS NOT NULL
          GROUP BY uuid
          HAVING COUNT(*) > 1
        ''').get();

        if (duplicateUuids.isNotEmpty) {
          final totalDuplicates = duplicateUuids.fold<int>(
            0,
            (sum, row) => sum + (row.data['count'] as int) - 1,
          );
          tableIssues.add(
            '${duplicateUuids.length} UUID groups with $totalDuplicates duplicate records',
          );
        }

        if (tableIssues.isNotEmpty) {
          issues[tableName] = tableIssues;
        }
      }

      if (issues.isEmpty) {
        debugPrint('Database integrity validation passed - no issues found');
      } else {
        debugPrint('Database integrity validation found issues: $issues');
      }

      return issues;
    } catch (e) {
      debugPrint('Error during database integrity validation: $e');
      return {
        'validation_error': ['Failed to validate database: $e'],
      };
    }
  }

  /// Fix UUID issues in a table by generating unique UUIDs for records that have NULL or duplicate UUIDs
  Future<void> _fixTableUuids(
    String tableName,
    List<String> additionalColumns,
    String now,
  ) async {
    try {
      debugPrint('Fixing UUIDs for table: $tableName');

      // First, handle records with NULL UUIDs
      final nullUuidCount = await customSelect(
        'SELECT COUNT(*) as count FROM $tableName WHERE uuid IS NULL',
      ).getSingle();

      if (nullUuidCount.data['count'] > 0) {
        debugPrint(
          'Found ${nullUuidCount.data['count']} records with NULL UUIDs in $tableName',
        );

        // Get all records with NULL UUIDs
        final recordsWithNullUuid = await customSelect(
          'SELECT id FROM $tableName WHERE uuid IS NULL',
        ).get();

        // Update each record individually with a unique UUID
        for (final record in recordsWithNullUuid) {
          final newUuid = const Uuid().v4();
          final updateColumns = ['uuid = ?'];
          final updateValues = [newUuid];

          // Add additional columns based on table structure
          for (final column in additionalColumns) {
            if (column == 'created_at' || column == 'updated_at') {
              updateColumns.add('$column = ?');
              updateValues.add(now);
            } else if (column == 'sync_status') {
              updateColumns.add('$column = ?');
              updateValues.add('synced');
            }
          }

          updateValues.add(record.data['id']);

          await customStatement(
            'UPDATE $tableName SET ${updateColumns.join(', ')} WHERE id = ?',
            updateValues,
          );
        }
      }

      // Next, handle duplicate UUIDs by finding groups of records with the same UUID
      final duplicateUuids = await customSelect('''
        SELECT uuid, COUNT(*) as count
        FROM $tableName
        WHERE uuid IS NOT NULL
        GROUP BY uuid
        HAVING COUNT(*) > 1
      ''').get();

      if (duplicateUuids.isNotEmpty) {
        debugPrint(
          'Found ${duplicateUuids.length} duplicate UUID groups in $tableName',
        );

        for (final duplicateGroup in duplicateUuids) {
          final duplicateUuid = duplicateGroup.data['uuid'] as String;
          final count = duplicateGroup.data['count'] as int;

          debugPrint(
            'Fixing $count records with duplicate UUID: $duplicateUuid',
          );

          // Get all records with this duplicate UUID, ordered by ID to keep the first one unchanged
          final duplicateRecords = await customSelect(
            'SELECT id FROM $tableName WHERE uuid = ? ORDER BY id',
            variables: [Variable(duplicateUuid)],
          ).get();

          // Skip the first record (keep its UUID), update the rest with new UUIDs
          for (int i = 1; i < duplicateRecords.length; i++) {
            final record = duplicateRecords[i];
            final newUuid = const Uuid().v4();
            final updateColumns = ['uuid = ?'];
            final updateValues = [newUuid];

            // Add additional columns for sync status update
            for (final column in additionalColumns) {
              if (column == 'updated_at') {
                updateColumns.add('$column = ?');
                updateValues.add(now);
              } else if (column == 'sync_status') {
                updateColumns.add('$column = ?');
                updateValues.add(
                  'pendingUpload',
                ); // Mark as pending since UUID changed
              }
            }

            updateValues.add(record.data['id']);

            await customStatement(
              'UPDATE $tableName SET ${updateColumns.join(', ')} WHERE id = ?',
              updateValues,
            );
          }
        }
      }

      debugPrint('Successfully fixed UUIDs for table: $tableName');
    } catch (e) {
      debugPrint('Error fixing UUIDs for table $tableName: $e');
    }
  }
}

LazyDatabase _openConnection(String path) {
  return LazyDatabase(() async {
    return NativeDatabase(File(path));
  });
}

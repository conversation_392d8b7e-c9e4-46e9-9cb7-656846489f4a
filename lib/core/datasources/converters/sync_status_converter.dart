import 'package:drift/drift.dart';

/// Enum for sync status
/// 
/// Represents the synchronization state of database records:
/// - pendingUpload: Record needs to be uploaded to remote server
/// - synced: Record is synchronized with remote server
/// - conflict: Record has synchronization conflicts that need resolution
enum SyncStatus { pendingUpload, synced, conflict }

/// Converter for SyncStatus enum to string for storage
/// 
/// This converter handles the serialization and deserialization of SyncStatus
/// enum values to/from string format for database storage.
class SyncStatusConverter extends TypeConverter<SyncStatus, String> {
  const SyncStatusConverter();

  @override
  SyncStatus fromSql(String sqlValue) {
    return SyncStatus.values.firstWhere(
      (status) => status.toString().split('.').last == sqlValue,
      orElse: () => SyncStatus.pendingUpload,
    );
  }

  @override
  String toSql(SyncStatus value) {
    // Extract enum value name from enum.toString() which returns "SyncStatus.value"
    return value.toString().split('.').last;
  }
}

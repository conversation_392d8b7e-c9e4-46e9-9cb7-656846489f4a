import 'package:drift/drift.dart';

/// Converter to ensure DateTime values are stored in UTC
/// 
/// This converter automatically converts DateTime values to UTC when storing
/// in the database and ensures they remain in UTC when reading from the database.
class UtcDateTimeConverter extends TypeConverter<DateTime, DateTime> {
  const UtcDateTimeConverter();

  @override
  DateTime fromSql(DateTime dateTime) {
    return dateTime.toUtc();
  }

  @override
  DateTime toSql(DateTime value) {
    return value.toUtc();
  }
}

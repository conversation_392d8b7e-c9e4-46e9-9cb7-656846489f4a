import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../converters/sync_status_converter.dart';
import '../converters/utc_datetime_converter.dart';

/// Income table definition
/// 
/// This table stores driver income records with calculated fields
/// for tracking daily earnings and financial performance.
class Income extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime().map(const UtcDateTimeConverter())();
  IntColumn get initialMileage => integer()();
  IntColumn get finalMileage => integer()();
  RealColumn get initialGopay => real()();
  RealColumn get initialBca => real()();
  RealColumn get initialCash => real()();
  RealColumn get initialOvo => real()();
  RealColumn get initialBri => real()();
  RealColumn get initialRekpon => real()();
  RealColumn get finalGopay => real()();
  RealColumn get finalBca => real()();
  RealColumn get finalCash => real()();
  RealColumn get finalOvo => real()();
  RealColumn get finalBri => real()();
  RealColumn get finalRekpon => real()();
  RealColumn get initialCapital => real().nullable()(); // Computed field
  RealColumn get finalResult => real().nullable()(); // Computed field
  IntColumn get mileage => integer().nullable()(); // Computed field
  RealColumn get netIncome => real().nullable()(); // Computed field

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

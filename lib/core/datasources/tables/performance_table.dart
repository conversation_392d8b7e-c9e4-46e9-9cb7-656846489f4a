import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../converters/sync_status_converter.dart';
import '../converters/utc_datetime_converter.dart';

/// Performance table definition
/// 
/// This table stores driver performance metrics with calculated fields
/// for tracking bid performance, trip performance, and retention rates.
class Performance extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime().map(const UtcDateTimeConverter())();
  RealColumn get bidPerformance => real()();
  RealColumn get tripPerformance => real()();
  IntColumn get activeDays => integer()();
  RealColumn get onlineHours => real()();
  RealColumn get avgCompleted => real().nullable()(); // Computed field
  RealColumn get avgOnline => real().nullable()(); // Computed field
  RealColumn get retention => real().nullable()(); // Computed field

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

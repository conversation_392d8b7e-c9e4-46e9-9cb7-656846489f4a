import 'package:drift/drift.dart';

import '../converters/utc_datetime_converter.dart';

/// App Settings table definition
/// 
/// This table stores application-wide settings including date ranges,
/// backup configurations, and synchronization timestamps.
class AppSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  // Date range settings
  DateTimeColumn get dateRangeStart =>
      dateTime().map(const UtcDateTimeConverter())();
  DateTimeColumn get dateRangeEnd =>
      dateTime().map(const UtcDateTimeConverter())();
  // Backup settings
  TextColumn get backupDirectoryPath => text().nullable()();
  // Add other app settings here as needed
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  // Add new field for last sync time
  DateTimeColumn get lastSyncTime =>
      dateTime().nullable().map(const UtcDateTimeConverter())();
}

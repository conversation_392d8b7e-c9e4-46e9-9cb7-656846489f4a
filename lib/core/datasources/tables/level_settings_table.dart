import 'package:drift/drift.dart';

/// Level Settings table definition
/// 
/// This table stores the requirements for different driver levels
/// (Platinum, Gold, Silver) including points, bid, and trip requirements.
class LevelSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get platinumPointsReq => integer()();
  RealColumn get platinumBidReq => real()();
  RealColumn get platinumTripReq => real()();
  IntColumn get goldPointsReq => integer()();
  RealColumn get goldBidReq => real()();
  RealColumn get goldTripReq => real()();
  IntColumn get silverPointsReq => integer()();
  RealColumn get silverBidReq => real()();
  RealColumn get silverTripReq => real()();
}

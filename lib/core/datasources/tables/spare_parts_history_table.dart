import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../converters/sync_status_converter.dart';
import '../converters/utc_datetime_converter.dart';
import 'spare_parts_table.dart';

/// Spare Parts History table definition
/// 
/// This table stores the history of spare parts replacements including
/// usage statistics, replacement reasons, and maintenance tracking.
class SparePartsHistory extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  TextColumn get partName => text()();
  TextColumn get partType => text()();
  RealColumn get price => real()();
  DateTimeColumn get replacementDate =>
      dateTime().map(const UtcDateTimeConverter())();
  IntColumn get mileageAtReplacement => integer()();
  IntColumn get sparePartId => integer().references(SpareParts, #id)();
  // New columns for better history tracking
  DateTimeColumn get installationDate => dateTime().map(
    const UtcDateTimeConverter(),
  )(); // When the part was installed
  IntColumn get initialMileage =>
      integer()(); // Mileage when the part was installed
  TextColumn get replacementReason =>
      text().withDefault(const Constant('Regular maintenance'))();
  IntColumn get replacedByPartId =>
      integer().nullable()(); // ID of the new part that replaced this one
  IntColumn get replacementCount =>
      integer().withDefault(const Constant(1))(); // Which replacement this was
  IntColumn get usageDays =>
      integer().withDefault(const Constant(0))(); // Days the part was used
  IntColumn get usageMileage => integer().withDefault(
    const Constant(0),
  )(); // Mileage the part was used for
  TextColumn get notes => text().withDefault(const Constant(''))();

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

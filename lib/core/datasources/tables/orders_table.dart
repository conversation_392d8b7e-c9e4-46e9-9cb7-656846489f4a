import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../converters/sync_status_converter.dart';
import '../converters/utc_datetime_converter.dart';

/// Orders table definition
/// 
/// This table stores driver order records with calculated fields
/// for tracking bid acceptance, trip completion, and performance metrics.
class Orders extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime().map(const UtcDateTimeConverter())();
  IntColumn get orderCompleted => integer()();
  IntColumn get orderMissed => integer()();
  IntColumn get orderCanceled => integer()();
  IntColumn get cbsOrder => integer()();
  IntColumn get incomingOrder => integer().nullable()(); // Computed field
  IntColumn get orderReceived => integer().nullable()(); // Computed field
  RealColumn get bidAcceptance => real().nullable()(); // Computed field
  RealColumn get tripCompletion => real().nullable()(); // Computed field
  IntColumn get points => integer()();
  RealColumn get trip => real()();
  RealColumn get bonus => real()();
  RealColumn get tips => real()();
  RealColumn get income => real().nullable()(); // Computed field

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../converters/sync_status_converter.dart';

/// Spare Parts table definition
/// 
/// This table stores spare parts information including installation details,
/// mileage tracking, and replacement history for vehicle maintenance.
class SpareParts extends Table {
  TextColumn get uuid => text().clientDefault(() => const Uuid().v4())();
  IntColumn get id => integer().autoIncrement()();
  TextColumn get partName => text()();
  TextColumn get partType => text()();
  RealColumn get price => real()();
  IntColumn get mileageLimit => integer()();
  IntColumn get initialMileage => integer()();
  DateTimeColumn get installationDate =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  IntColumn get currentMileage => integer().withDefault(const Constant(0))();
  BoolColumn get warningStatus =>
      boolean().withDefault(const Constant(false))();
  // New columns
  IntColumn get replacementCount => integer().withDefault(const Constant(0))();
  TextColumn get notes => text().withDefault(const Constant(''))();

  // Sync fields
  DateTimeColumn get createdAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get updatedAt =>
      dateTime().clientDefault(() => DateTime.now().toUtc())();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus => text()
      .map(const SyncStatusConverter())
      .withDefault(const Constant('pendingUpload'))();
}

/// Template for creating CRUD operation providers with standardized patterns
///
/// This mixin provides a standardized template for creating providers that
/// handle Create, Read, Update, and Delete operations with consistent
/// error handling, loading states, and automatic state refreshing.
///
/// Usage:
/// ```dart
/// @riverpod
/// class FeatureOperations extends _$FeatureOperations
///     with ProviderErrorHandling, CrudProviderTemplate<Feature> {
///   @override
///   Future<void> build() async {}
///
///   @override
///   List<Provider> get invalidateProviders => [featureListProvider];
///
///   Future<bool> addFeature(Feature feature) async {
///     return await executeAdd(() async {
///       final repository = ref.read(featureRepositoryProvider);
///       final result = await repository.save(feature);
///       return handleResult(result, false);
///     });
///   }
/// }
/// ```
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'provider_error_handling.dart';

/// Template for creating operation providers (add, update, delete)
///
/// This mixin standardizes the pattern of creating providers that handle
/// CRUD operations with loading states and error handling. It must be used
/// together with the ProviderErrorHandling mixin to provide consistent
/// error handling across all operations.
///
/// The template provides:
/// - Standardized CRUD operation execution with error handling
/// - Automatic loading state management
/// - Provider invalidation after successful operations
/// - Generic type support for different entity types
///
/// Requirements:
/// - Must be mixed with ProviderErrorHandling
/// - Must implement invalidateProviders getter
/// - Should be used with AsyncNotifier classes
mixin CrudProviderTemplate<T> on ProviderErrorHandling {
  /// Providers to invalidate after successful operations - must be implemented by concrete classes
  ///
  /// This getter should return a list of providers that need to be invalidated
  /// after successful CRUD operations. This ensures that dependent providers
  /// (like list providers) are updated when data changes.
  ///
  /// Example implementation:
  /// ```dart
  /// @override
  /// List<Provider> get invalidateProviders => [
  ///   orderListProvider,
  ///   orderSummaryProvider,
  /// ];
  /// ```
  List<ProviderBase> get invalidateProviders;

  /// Execute an add operation with standardized error handling and state management
  ///
  /// This method provides a standardized way to execute add operations.
  /// It handles loading states, error handling, and provider invalidation
  /// automatically.
  ///
  /// Parameters:
  /// - [operation]: The async function that performs the actual add operation
  /// - [ref]: The WidgetRef for provider invalidation
  ///
  /// Returns:
  /// - true if the operation was successful
  /// - false if the operation failed
  ///
  /// Example usage:
  /// ```dart
  /// Future<bool> addOrder(Order order) async {
  ///   return await executeAdd(() async {
  ///     final repository = ref.read(orderRepositoryProvider);
  ///     final result = await repository.save(order);
  ///     return handleResult(result, false);
  ///   }, ref);
  /// }
  /// ```
  Future<bool> executeAdd(
    Future<bool> Function() operation,
    WidgetRef ref,
  ) async {
    return await _executeOperation(operation, ref);
  }

  /// Execute an update operation with standardized error handling and state management
  ///
  /// This method provides a standardized way to execute update operations.
  /// It handles loading states, error handling, and provider invalidation
  /// automatically.
  ///
  /// Parameters:
  /// - [operation]: The async function that performs the actual update operation
  /// - [ref]: The WidgetRef for provider invalidation
  ///
  /// Returns:
  /// - true if the operation was successful
  /// - false if the operation failed
  ///
  /// Example usage:
  /// ```dart
  /// Future<bool> updateOrder(Order order) async {
  ///   return await executeUpdate(() async {
  ///     final repository = ref.read(orderRepositoryProvider);
  ///     final result = await repository.update(order);
  ///     return handleResult(result, false);
  ///   }, ref);
  /// }
  /// ```
  Future<bool> executeUpdate(
    Future<bool> Function() operation,
    WidgetRef ref,
  ) async {
    return await _executeOperation(operation, ref);
  }

  /// Execute a delete operation with standardized error handling and state management
  ///
  /// This method provides a standardized way to execute delete operations.
  /// It handles loading states, error handling, and provider invalidation
  /// automatically.
  ///
  /// Parameters:
  /// - [operation]: The async function that performs the actual delete operation
  /// - [ref]: The WidgetRef for provider invalidation
  ///
  /// Returns:
  /// - true if the operation was successful
  /// - false if the operation failed
  ///
  /// Example usage:
  /// ```dart
  /// Future<bool> deleteOrder(String orderId) async {
  ///   return await executeDelete(() async {
  ///     final repository = ref.read(orderRepositoryProvider);
  ///     final result = await repository.delete(orderId);
  ///     return handleResult(result, false);
  ///   }, ref);
  /// }
  /// ```
  Future<bool> executeDelete(
    Future<bool> Function() operation,
    WidgetRef ref,
  ) async {
    return await _executeOperation(operation, ref);
  }

  /// Execute a generic operation with standardized error handling and state management
  ///
  /// This method provides a standardized way to execute any operation that
  /// returns a result of type R. It handles loading states, error handling,
  /// and provider invalidation automatically.
  ///
  /// Parameters:
  /// - [operation]: The async function that performs the actual operation
  /// - [ref]: The WidgetRef for provider invalidation
  /// - [defaultValue]: The value to return if the operation fails
  ///
  /// Returns:
  /// - The result of the operation if successful
  /// - The default value if the operation failed
  ///
  /// Example usage:
  /// ```dart
  /// Future<Order?> getOrderById(String id) async {
  ///   return await executeOperation(() async {
  ///     final repository = ref.read(orderRepositoryProvider);
  ///     final result = await repository.getById(id);
  ///     return handleResult(result, null);
  ///   }, ref, null);
  /// }
  /// ```
  Future<R> executeOperation<R>(
    Future<R> Function() operation,
    WidgetRef ref,
    R defaultValue,
  ) async {
    try {
      final result = await operation();

      // Only invalidate providers if the operation was successful
      // For boolean operations, we check if the result is true
      // For other types, we assume success if no exception was thrown
      bool shouldInvalidate = true;
      if (result is bool) {
        shouldInvalidate = result;
      }

      if (shouldInvalidate) {
        _invalidateProviders(ref);
      }

      return result;
    } catch (error) {
      // Error handling is delegated to the operation function
      // which should use handleResult() from ProviderErrorHandling
      return defaultValue;
    }
  }

  /// Private method to execute boolean operations (add, update, delete)
  ///
  /// This is a helper method that standardizes the execution of operations
  /// that return boolean values indicating success or failure.
  Future<bool> _executeOperation(
    Future<bool> Function() operation,
    WidgetRef ref,
  ) async {
    try {
      final result = await operation();

      // Only invalidate providers if the operation was successful
      if (result) {
        _invalidateProviders(ref);
      }

      return result;
    } catch (error) {
      // Error handling is delegated to the operation function
      // which should use handleResult() from ProviderErrorHandling
      return false;
    }
  }

  /// Private method to invalidate related providers after successful operations
  ///
  /// This method invalidates all providers specified in the invalidateProviders
  /// getter to ensure that dependent data is refreshed after successful operations.
  void _invalidateProviders(WidgetRef ref) {
    for (final provider in invalidateProviders) {
      ref.invalidate(provider);
    }
  }

  /// Check if an operation is currently in progress
  ///
  /// This method can be used to check if any operation is currently being
  /// executed. It's useful for showing loading indicators in the UI.
  ///
  /// Note: The actual loading state management is handled by Riverpod's
  /// AsyncValue system. This method is provided for convenience and
  /// future extensibility.
  bool get isOperationInProgress => false; // Default implementation

  /// Get the last operation result
  ///
  /// This method can be used to get the result of the last executed operation.
  /// It's useful for showing success/failure messages in the UI.
  ///
  /// Note: This is a basic implementation. In a more advanced scenario,
  /// you might want to track operation history or specific operation types.
  bool? get lastOperationResult => null; // Default implementation
}

/// Mixin for standardizing error handling in provider classes
///
/// This mixin provides common error handling patterns for AsyncNotifier classes
/// using the Either pattern from dartz for functional error handling.
///
/// This mixin is used by both regular list providers (ListProviderTemplate)
/// and paginated list providers (PaginatedListProviderTemplate) to ensure
/// consistent error handling across all provider types.
///
/// Usage:
/// ```dart
/// @riverpod
/// class FeatureList extends _$FeatureList with ProviderErrorHandling {
///   @override
///   Future<List<Feature>> build() async {
///     return _fetchFeatureList();
///   }
///
///   Future<List<Feature>> _fetchFeatureList() async {
///     final useCase = ref.watch(getFeatureListProvider);
///     final result = await useCase.execute();
///     return handleResult(result, []);
///   }
/// }
/// ```
library;

import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

import '../../errors/failures.dart';

/// Mixin for standardizing error handling in provider classes
///
/// This mixin provides common error handling patterns for AsyncNotifier classes.
/// It standardizes the handling of Either&lt;Failure, T&gt; results and provides
/// consistent error logging across all providers.
mixin ProviderErrorHandling {
  /// Handle Either&lt;Failure, T&gt; results with standardized error handling
  ///
  /// This method takes an Either result and handles both success and failure cases.
  /// On failure, it logs the error and returns the provided default value.
  /// On success, it returns the actual value.
  ///
  /// Parameters:
  /// - [result]: The Either&lt;Failure, T&gt; result to handle
  /// - [defaultValue]: The value to return in case of failure
  /// - [onError]: Optional callback to execute on error (in addition to logging)
  ///
  /// Returns:
  /// - The success value if the result is Right
  /// - The default value if the result is Left (failure)
  ///
  /// Example:
  /// ```dart
  /// final result = await repository.getData();
  /// return handleResult(result, [], onError: (failure) {
  ///   // Custom error handling logic
  ///   showErrorSnackbar(failure.message);
  /// });
  /// ```
  T handleResult<T>(
    Either<Failure, T> result,
    T defaultValue, {
    void Function(Failure)? onError,
  }) {
    return result.fold((failure) {
      _handleFailure(failure);
      onError?.call(failure);
      return defaultValue;
    }, (value) => value);
  }

  /// Handle async Either&lt;Failure, T&gt; results with standardized error handling
  ///
  /// This is a convenience method for handling Future&lt;Either&lt;Failure, T&gt;&gt; results.
  /// It awaits the future and then applies the same error handling logic as handleResult.
  ///
  /// Parameters:
  /// - [futureResult]: The Future&lt;Either&lt;Failure, T&gt;&gt; result to handle
  /// - [defaultValue]: The value to return in case of failure
  /// - [onError]: Optional callback to execute on error (in addition to logging)
  ///
  /// Returns:
  /// - The success value if the result is Right
  /// - The default value if the result is Left (failure)
  ///
  /// Example:
  /// ```dart
  /// final futureResult = repository.getDataAsync();
  /// return await handleAsyncResult(futureResult, []);
  /// ```
  Future<T> handleAsyncResult<T>(
    Future<Either<Failure, T>> futureResult,
    T defaultValue, {
    void Function(Failure)? onError,
  }) async {
    final result = await futureResult;
    return handleResult(result, defaultValue, onError: onError);
  }

  /// Standard failure handling with consistent logging
  ///
  /// This private method handles the logging of failures in a consistent manner
  /// across all providers. It uses debugPrint to ensure logging only occurs
  /// in debug builds and not in production releases.
  ///
  /// The error message includes both the failure type and message for better
  /// debugging and monitoring.
  ///
  /// Parameters:
  /// - [failure]: The Failure object to log
  void _handleFailure(Failure failure) {
    // Use debugPrint for logging to ensure it only logs in debug builds
    // and not in production releases for security and performance reasons
    debugPrint(
      'Provider error: ${failure.runtimeType} - ${failure.toString()}',
    );

    // In a production app, you might also want to:
    // - Send errors to a crash reporting service (e.g., Crashlytics, Sentry)
    // - Log to a centralized logging service
    // - Track error metrics for monitoring
    //
    // Example:
    // crashReportingService.recordError(failure);
    // analyticsService.trackError(failure.runtimeType.toString());
  }
}

# Provider Templates

This directory contains standardized provider templates to eliminate duplication across features and ensure consistent patterns throughout the application.

## Overview

The provider templates address the 70% duplication identified in the refactoring analysis by providing reusable patterns for:

1. **Repository Providers** - Standardized creation of repository providers
2. **Use Case Providers** - Consistent patterns for use case dependency injection
3. **List Providers** - Common patterns for list management with refresh capability
4. **Operation Providers** - Standardized CRUD operation handling
5. **Error Handling** - Consistent error handling across all providers

## Usage Patterns

### 1. Repository Providers

For repositories that extend BaseRepository and need database + sync service:

```dart
// Before (duplicated pattern):
@riverpod
IncomeRepository incomeRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  final syncService = ref.watch(syncServiceProvider);
  return IncomeRepositoryImpl(
    database: database,
    syncService: syncService,
  );
}

// After (using template):
@riverpod
IncomeRepository incomeRepository(Ref ref) {
  return createRepositoryProvider<IncomeRepository, IncomeRepositoryImpl>(
    ref,
    (database, syncService) => IncomeRepositoryImpl(
      database: database,
      syncService: syncService,
    ),
  );
}
```

For repositories with additional dependencies:

```dart
@riverpod
IncomeRepository incomeRepository(Ref ref) {
  return createRepositoryProviderWithDeps<IncomeRepository, IncomeRepositoryImpl>(
    ref,
    (database, syncService) => IncomeRepositoryImpl(
      database: database,
      syncService: syncService,
      calculationService: ref.watch(incomeCalculationServiceProvider),
    ),
  );
}
```

### 2. Use Case Providers

For simple use cases with single repository dependency:

```dart
// Before (duplicated pattern):
@riverpod
GetIncomeHistory getIncomeHistory(Ref ref) {
  final repository = ref.watch(incomeRepositoryProvider);
  return GetIncomeHistory(repository);
}

// After (using template):
@riverpod
GetIncomeHistory getIncomeHistory(Ref ref) {
  return createUseCaseProvider<GetIncomeHistory, IncomeRepository>(
    ref,
    incomeRepositoryProvider,
    (repository) => GetIncomeHistory(repository),
  );
}
```

For use cases with multiple dependencies:

```dart
@riverpod
ComplexUseCase complexUseCase(Ref ref) {
  return createUseCaseProviderMultiDeps<ComplexUseCase>(
    ref,
    () => ComplexUseCase(
      incomeRepository: ref.watch(incomeRepositoryProvider),
      orderRepository: ref.watch(orderRepositoryProvider),
      calculationService: ref.watch(calculationServiceProvider),
    ),
  );
}
```

### 3. List Providers with Error Handling

```dart
@riverpod
class IncomeList extends _$IncomeList with ProviderErrorHandling, ListProviderTemplate<Income> {
  @override
  Future<List<Income>> build() async {
    return fetchList();
  }

  @override
  Future<List<Income>> fetchData() async {
    final useCase = ref.watch(getIncomeHistoryProvider);
    final result = await useCase.getAll();
    return handleResult(result, []);
  }

  @override
  List<Provider> get invalidateProviders => [incomeListProvider];

  // Custom refresh method that uses the template
  Future<void> refreshIncomeList() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => fetchList());
  }
}
```

### 4. Operation Providers

```dart
@riverpod
class IncomeOperations extends _$IncomeOperations 
    with ProviderErrorHandling, OperationProviderTemplate {
  @override
  Future<void> build() async {}

  Future<bool> addIncome(Income income) async {
    return executeOperation(() async {
      final repository = ref.read(incomeRepositoryProvider);
      final result = await repository.save(income);
      return handleResult(result, false);
    }, [incomeListProvider]);
  }

  Future<bool> updateIncome(Income income) async {
    return executeOperation(() async {
      final repository = ref.read(incomeRepositoryProvider);
      final result = await repository.update(income);
      return handleResult(result, false);
    }, [incomeListProvider]);
  }

  Future<bool> deleteIncome(int id) async {
    return executeOperation(() async {
      final repository = ref.read(incomeRepositoryProvider);
      final result = await repository.delete(id);
      return handleResult(result, false);
    }, [incomeListProvider]);
  }
}
```

## Benefits

1. **Reduced Duplication**: Eliminates the 70% duplication in provider patterns
2. **Consistent Error Handling**: Standardized error handling across all features
3. **Type Safety**: Templates maintain full type safety while reducing boilerplate
4. **Maintainability**: Changes to patterns only need to be made in one place
5. **Testing**: Standardized patterns make testing more predictable

## Migration Strategy

1. **Phase 1**: Apply templates to new providers
2. **Phase 2**: Gradually migrate existing providers to use templates
3. **Phase 3**: Remove old duplicated patterns

## Template Files

- `provider_templates.dart` - Core template functions and mixins
- `README.md` - This documentation file

## Best Practices

1. Always use the appropriate template for your use case
2. Prefer composition over inheritance when extending templates
3. Keep error handling consistent using the provided mixins
4. Document any deviations from standard patterns
5. Test templates thoroughly as they affect multiple features

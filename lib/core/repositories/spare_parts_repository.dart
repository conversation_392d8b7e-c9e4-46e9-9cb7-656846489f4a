import 'package:drift/drift.dart';
import '../constants/table_constants.dart';
import '../datasources/app_database.dart';
import '../datasources/converters/sync_status_converter.dart';
import 'database_repository.dart';

/// Repository for SpareParts table operations
class SparePartsRepository
    extends BaseDatabaseRepository<SparePart, SparePartsCompanion> {
  SparePartsRepository(AppDatabase db) : super(db, db.spareParts);

  @override
  Future<List<SparePart>> getAll() {
    return (db.select(db.spareParts)..where((t) => t.deletedAt.isNull())).get();
  }

  @override
  Future<SparePart?> getById(int id) {
    return (db.select(db.spareParts)
          ..where((t) => t.id.equals(id))
          ..where((t) => t.deletedAt.isNull()))
        .getSingleOrNull();
  }

  @override
  Future<bool> update(SparePart entity) {
    return db.update(db.spareParts).replace(entity);
  }

  @override
  Future<int> delete(int id) {
    return (db.delete(db.spareParts)..where((t) => t.id.equals(id))).go();
  }

  @override
  Future<List<SparePart>> getUnsyncedRecords() {
    return (db.select(
      db.spareParts,
    )..where((t) => t.syncStatus.equals('pendingUpload'))).get();
  }

  @override
  Future<bool> updateWithSync(SparePart entity) async {
    // First update the entity
    final bool updated = await update(entity);

    // Then separately update the sync status fields if the entity update succeeded
    if (updated) {
      await db.customUpdate(
        'UPDATE spare_parts SET updated_at = ?, sync_status = ? WHERE id = ?',
        variables: [
          Variable(DateTime.now().toUtc().toIso8601String()),
          const Variable('pendingUpload'),
          Variable(entity.id),
        ],
        updateKind: UpdateKind.update,
      );
    }

    return updated;
  }

  @override
  Future<bool> softDelete(int id) async {
    return (db.update(db.spareParts)..where((t) => t.id.equals(id)))
        .write(
          SparePartsCompanion(
            deletedAt: Value(DateTime.now().toUtc()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        )
        .then((rows) => rows > 0);
  }

  @override
  Future<void> markAsSynced(String uuid) async {
    await (db.update(db.spareParts)..where((t) => t.uuid.equals(uuid))).write(
      const SparePartsCompanion(syncStatus: Value(SyncStatus.synced)),
    );
  }

  /// Update all spare parts' current mileage based on latest income
  Future<void> updateSparePartsMileage(int latestMileage) async {
    // Update current mileage for all parts
    await db.customUpdate(
      'UPDATE ${TableConstants.sparePartsTable} SET ${TableConstants.currentMileage} = ?',
      variables: [Variable(latestMileage)],
      updateKind: UpdateKind.update,
    );

    // Update warning status based on the warning threshold (90% of mileage limit)
    const warningThreshold = 0.9; // 90% warning threshold
    await db.customUpdate(
      'UPDATE ${TableConstants.sparePartsTable} '
      'SET ${TableConstants.warningStatus} = '
      '(${TableConstants.currentMileage} - ${TableConstants.sparePartInitialMileage}) >= '
      '(${TableConstants.mileageLimit} * ?)',
      variables: const [Variable(warningThreshold)],
      updateKind: UpdateKind.update,
    );
  }

  /// Get spare parts that need replacement (warning status is true)
  Future<List<SparePart>> getPartsNeedingReplacement() {
    return (db.select(db.spareParts)
          ..where((t) => t.deletedAt.isNull())
          ..where((t) => t.warningStatus.equals(true)))
        .get();
  }
}

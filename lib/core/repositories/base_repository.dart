import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart';

import '../datasources/app_database.dart';
import '../errors/exceptions.dart';
import '../errors/failures.dart';
import '../services/sync/sync_operations.dart';
import '../services/sync/sync_service.dart';
import 'repository_interfaces.dart';

/// Enhanced base repository that provides common functionality for all repositories
///
/// This class implements the template method pattern to eliminate code duplication
/// across repository implementations while maintaining type safety and flexibility.
abstract class BaseRepository<
  TEntity,
  TData extends DataClass,
  TCompanion extends Insertable<TData>
>
    implements IRepository<TEntity, int>, ISyncableRepository<TEntity, int> {
  final AppDatabase database;
  final SyncService syncService;

  BaseRepository({required this.database, required this.syncService});

  // Abstract methods that must be implemented by concrete repositories

  /// Map domain entity to database companion for inserts/updates
  TCompanion mapToCompanion(TEntity entity);

  /// Map database data to domain entity
  TEntity mapFromData(TData data);

  /// Get the database insert method for this entity type
  Future<int> insertEntity(TCompanion companion);

  /// Get the database update method for this entity type
  Future<bool> updateEntity(TEntity entity);

  /// Get the database delete method for this entity type
  Future<bool> deleteEntity(int id);

  /// Get all database records for this entity type
  Future<List<TData>> getAllData();

  /// Get database record by ID for this entity type
  Future<TData?> getDataById(int id);

  /// Get unsynced database records for this entity type
  Future<List<TData>> getUnsyncedData();

  /// Mark database record as synced by UUID
  Future<void> markDataAsSynced(String uuid);

  // Template method implementations with common error handling

  @override
  Future<Either<Failure, TEntity>> save(TEntity entity) async {
    return executeWithErrorHandling<int, TEntity>(() async {
      final companion = mapToCompanion(entity);
      final id = await insertEntity(companion);
      _triggerSync();
      return id;
    }, (id) => _copyEntityWithId(entity, id));
  }

  @override
  Future<Either<Failure, TEntity>> update(TEntity entity) async {
    return executeWithErrorHandling<bool, TEntity>(() async {
      final success = await updateEntity(entity);
      if (success) {
        _triggerSync();
      }
      return success;
    }, (success) => success ? entity : throw Exception('Update failed'));
  }

  @override
  Future<Either<Failure, bool>> delete(int id) async {
    return executeWithErrorHandling<bool, bool>(() async {
      final success = await deleteEntity(id);
      if (success) {
        _triggerSync();
      }
      return success;
    }, (success) => success);
  }

  @override
  Future<Either<Failure, List<TEntity>>> getAll() async {
    return executeWithErrorHandling<List<TData>, List<TEntity>>(
      () => getAllData(),
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }

  @override
  Future<Either<Failure, TEntity>> getById(int id) async {
    return executeWithErrorHandling<TData?, TEntity>(() => getDataById(id), (
      data,
    ) {
      if (data == null) {
        throw Exception('Entity with id $id not found');
      }
      return mapFromData(data);
    });
  }

  // ISyncableRepository implementation

  @override
  Future<Either<Failure, List<TEntity>>> getUnsyncedEntities() async {
    return executeWithErrorHandling<List<TData>, List<TEntity>>(
      () => getUnsyncedData(),
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }

  @override
  Future<Either<Failure, bool>> markAsSynced(String uuid) async {
    return executeWithErrorHandling<void, bool>(
      () => markDataAsSynced(uuid),
      (_) => true,
    );
  }

  @override
  Future<Either<Failure, bool>> syncEntities() async {
    return executeWithErrorHandling<void, bool>(
      () => syncService.syncNow(SyncOperation.upload),
      (_) => true,
    );
  }

  /// Template method for consistent error handling across all operations
  Future<Either<Failure, R>> executeWithErrorHandling<T, R>(
    Future<T> Function() operation,
    R Function(T) mapper,
  ) async {
    try {
      final result = await operation();
      return Right(mapper(result));
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  /// Helper method to trigger sync with debounce
  void _triggerSync() {
    syncService.syncData(SyncOperation.upload);
  }

  /// Abstract method to copy entity with new ID (must be implemented by concrete repositories)
  TEntity _copyEntityWithId(TEntity entity, int id);
}

/// Enhanced base repository for entities that support date range filtering
///
/// This class extends the base repository with date range functionality
/// commonly needed by income, orders, and performance repositories.
abstract class BaseDateRangeRepository<
  TEntity,
  TData extends DataClass,
  TCompanion extends Insertable<TData>
>
    extends BaseRepository<TEntity, TData, TCompanion>
    implements IDateRangeRepository<TEntity, int> {
  BaseDateRangeRepository({
    required super.database,
    required super.syncService,
  });

  // Abstract methods for date range operations

  /// Get database records for a specific date range
  Future<List<TData>> getDataForDateRange(DateTime start, DateTime end);

  /// Check if a database record exists for a specific date
  Future<bool> checkDataDateExists(DateTime date, {int? excludeId});

  // IDateRangeRepository implementation

  @override
  Future<Either<Failure, List<TEntity>>> getForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    return executeWithErrorHandling<List<TData>, List<TEntity>>(
      () => getDataForDateRange(start, end),
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }

  @override
  Future<Either<Failure, bool>> checkDateExists(
    DateTime date, {
    int? excludeId,
  }) async {
    return executeWithErrorHandling<bool, bool>(
      () => checkDataDateExists(date, excludeId: excludeId),
      (exists) => exists,
    );
  }
}

import 'package:drift/drift.dart';
import '../datasources/app_database.dart';
import '../datasources/converters/sync_status_converter.dart';
import 'database_repository.dart';

/// Repository for Orders table operations
class OrdersRepository extends BaseDatabaseRepository<Order, OrdersCompanion> {
  OrdersRepository(AppDatabase db) : super(db, db.orders);

  @override
  Future<List<Order>> getAll() {
    return (db.select(db.orders)
          ..where((t) => t.deletedAt.isNull())
          ..orderBy([(t) => OrderingTerm.desc(t.date)]))
        .get();
  }

  @override
  Future<Order?> getById(int id) {
    return (db.select(db.orders)
          ..where((t) => t.id.equals(id))
          ..where((t) => t.deletedAt.isNull()))
        .getSingleOrNull();
  }

  @override
  Future<bool> update(Order entity) {
    return db.update(db.orders).replace(entity);
  }

  @override
  Future<int> delete(int id) {
    return (db.delete(db.orders)..where((t) => t.id.equals(id))).go();
  }

  @override
  Future<List<Order>> getUnsyncedRecords() {
    return (db.select(
      db.orders,
    )..where((t) => t.syncStatus.equals('pendingUpload'))).get();
  }

  @override
  Future<bool> updateWithSync(OrdersCompanion entity) async {
    // We need the ID to update a record
    if (!entity.id.present) {
      return false;
    }

    return (db.update(db.orders)..where((t) => t.id.equals(entity.id.value)))
        .write(
          OrdersCompanion(
            updatedAt: Value(DateTime.now().toUtc()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ).copyWith(
            date: entity.date,
            orderCompleted: entity.orderCompleted,
            orderMissed: entity.orderMissed,
            orderCanceled: entity.orderCanceled,
            cbsOrder: entity.cbsOrder,
            points: entity.points,
            trip: entity.trip,
            bonus: entity.bonus,
            tips: entity.tips,
          ),
        )
        .then((rows) => rows > 0);
  }

  @override
  Future<bool> softDelete(int id) async {
    return (db.update(db.orders)..where((t) => t.id.equals(id)))
        .write(
          OrdersCompanion(
            deletedAt: Value(DateTime.now().toUtc()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        )
        .then((rows) => rows > 0);
  }

  @override
  Future<void> markAsSynced(String uuid) async {
    await (db.update(db.orders)..where((t) => t.uuid.equals(uuid))).write(
      const OrdersCompanion(syncStatus: Value(SyncStatus.synced)),
    );
  }

  /// Get orders for a specific date range
  Future<List<Order>> getOrdersForDateRange(DateTime start, DateTime end) {
    return (db.select(db.orders)
          ..where((t) => t.deletedAt.isNull())
          ..where((t) => t.date.isBiggerOrEqualValue(start))
          ..where((t) => t.date.isSmallerOrEqualValue(end))
          ..orderBy([(t) => OrderingTerm.desc(t.date)]))
        .get();
  }

  /// Get orders for performance calculation (last 14 days)
  Future<List<Order>> getOrdersForPerformanceCalculation(DateTime endDate) {
    final startDate = endDate.subtract(const Duration(days: 14));
    return getOrdersForDateRange(startDate, endDate);
  }

  /// Get total completed orders for the last 14 days (excluding current day)
  Future<int> getTotalCompletedOrdersForLast14Days(DateTime endDate) async {
    final startDate = endDate.subtract(const Duration(days: 14));

    final result = await db
        .customSelect(
          'SELECT SUM(order_completed) as total FROM orders '
          'WHERE deleted_at IS NULL AND date >= ? AND date <= ?',
          variables: [
            Variable(startDate.toIso8601String()),
            Variable(endDate.toIso8601String()),
          ],
        )
        .getSingleOrNull();

    return result?.data['total'] as int? ?? 0;
  }

  /// Get total points for a month
  Future<int> getTotalPointsForMonth(int year, int month) async {
    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 0); // Last day of month

    final result = await db
        .customSelect(
          'SELECT SUM(points) as total FROM orders '
          'WHERE deleted_at IS NULL AND date >= ? AND date <= ?',
          variables: [
            Variable(startDate.toIso8601String()),
            Variable(endDate.toIso8601String()),
          ],
        )
        .getSingleOrNull();

    return result?.data['total'] as int? ?? 0;
  }

  /// Check if an order record already exists for a specific date
  Future<bool> checkDateExists(DateTime date, {int? excludeId}) async {
    final query = db.select(db.orders)
      ..where((t) => t.deletedAt.isNull())
      ..where((t) => t.date.equals(date));

    if (excludeId != null) {
      query.where((t) => t.id.isNotValue(excludeId));
    }

    final count = await query.get().then((records) => records.length);
    return count > 0;
  }
}

import 'package:drift/drift.dart';
import '../datasources/app_database.dart';
import '../datasources/converters/sync_status_converter.dart';
import 'database_repository.dart';

/// Repository for SparePartsHistory table operations
class SparePartsHistoryRepository
    extends
        BaseDatabaseRepository<
          SparePartsHistoryData,
          SparePartsHistoryCompanion
        > {
  SparePartsHistoryRepository(AppDatabase db) : super(db, db.sparePartsHistory);

  @override
  Future<List<SparePartsHistoryData>> getAll() {
    return (db.select(db.sparePartsHistory)
          ..where((t) => t.deletedAt.isNull())
          ..orderBy([(t) => OrderingTerm.desc(t.replacementDate)]))
        .get();
  }

  @override
  Future<SparePartsHistoryData?> getById(int id) {
    return (db.select(db.sparePartsHistory)
          ..where((t) => t.id.equals(id))
          ..where((t) => t.deletedAt.isNull()))
        .getSingleOrNull();
  }

  @override
  Future<bool> update(SparePartsHistoryData entity) {
    return db.update(db.sparePartsHistory).replace(entity);
  }

  @override
  Future<int> delete(int id) {
    return (db.delete(
      db.sparePartsHistory,
    )..where((t) => t.id.equals(id))).go();
  }

  @override
  Future<List<SparePartsHistoryData>> getUnsyncedRecords() {
    return (db.select(
      db.sparePartsHistory,
    )..where((t) => t.syncStatus.equals('pendingUpload'))).get();
  }

  @override
  Future<bool> updateWithSync(SparePartsHistoryData entity) async {
    // First update the entity
    final bool updated = await update(entity);

    // Then separately update the sync status fields if the entity update succeeded
    if (updated) {
      await db.customUpdate(
        'UPDATE spare_parts_history SET updated_at = ?, sync_status = ? WHERE id = ?',
        variables: [
          Variable(DateTime.now().toUtc().toIso8601String()),
          const Variable('pendingUpload'),
          Variable(entity.id),
        ],
        updateKind: UpdateKind.update,
      );
    }

    return updated;
  }

  @override
  Future<bool> softDelete(int id) async {
    return (db.update(db.sparePartsHistory)..where((t) => t.id.equals(id)))
        .write(
          SparePartsHistoryCompanion(
            deletedAt: Value(DateTime.now().toUtc()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        )
        .then((rows) => rows > 0);
  }

  @override
  Future<void> markAsSynced(String uuid) async {
    await (db.update(
      db.sparePartsHistory,
    )..where((t) => t.uuid.equals(uuid))).write(
      const SparePartsHistoryCompanion(syncStatus: Value(SyncStatus.synced)),
    );
  }

  /// Get history for a specific spare part
  Future<List<SparePartsHistoryData>> getHistoryForSparePart(int sparePartId) {
    return (db.select(db.sparePartsHistory)
          ..where((t) => t.sparePartId.equals(sparePartId))
          ..where((t) => t.deletedAt.isNull())
          ..orderBy([(t) => OrderingTerm.desc(t.replacementDate)]))
        .get();
  }
}

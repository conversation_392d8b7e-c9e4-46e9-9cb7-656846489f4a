import 'dart:async';
import 'dart:math' show min;
import 'package:supabase_flutter/supabase_flutter.dart';

import 'auth_logger.dart';

/// Types of refresh errors for categorization
enum RefreshErrorType {
  network,
  authentication,
  tokenExpired,
  serverError,
  unknown,
}

/// Represents a refresh error with categorization
class RefreshError {
  final RefreshErrorType type;
  final String message;
  final dynamic originalError;
  final DateTime timestamp;
  final int attemptNumber;

  RefreshError({
    required this.type,
    required this.message,
    required this.originalError,
    required this.attemptNumber,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'RefreshError(type: $type, message: $message, attempt: $attemptNumber, time: $timestamp)';
  }
}

/// Handles session-related errors and recovery strategies
class SessionErrorHandler {
  final AuthLogger _authLogger;

  // Constants for retry logic
  static const Duration _initialRetryDelay = Duration(seconds: 2);
  static const int _maxRefreshRetries = 3;

  SessionErrorHandler(this._authLogger);

  /// Categorize refresh errors for appropriate handling
  RefreshError categorizeRefreshError(dynamic error, int attemptNumber) {
    RefreshErrorType type;
    String message;

    if (error is AuthException) {
      switch (error.statusCode) {
        case '401':
          type = RefreshErrorType.authentication;
          message = 'Authentication failed: ${error.message}';
          break;
        case '403':
          type = RefreshErrorType.tokenExpired;
          message = 'Token expired: ${error.message}';
          break;
        case '500':
        case '502':
        case '503':
        case '504':
          type = RefreshErrorType.serverError;
          message = 'Server error: ${error.message}';
          break;
        default:
          type = RefreshErrorType.authentication;
          message = 'Auth error: ${error.message}';
      }
    } else if (error is TimeoutException) {
      type = RefreshErrorType.network;
      message = 'Network timeout during refresh';
    } else if (error.toString().toLowerCase().contains('network') ||
               error.toString().toLowerCase().contains('connection')) {
      type = RefreshErrorType.network;
      message = 'Network error: $error';
    } else {
      type = RefreshErrorType.unknown;
      message = 'Unknown error: $error';
    }

    return RefreshError(
      type: type,
      message: message,
      originalError: error,
      attemptNumber: attemptNumber,
    );
  }

  /// Determine if an error is retryable
  bool isRetryableError(RefreshError error) {
    switch (error.type) {
      case RefreshErrorType.network:
      case RefreshErrorType.serverError:
        return true;
      case RefreshErrorType.authentication:
      case RefreshErrorType.tokenExpired:
        return false; // These require re-authentication
      case RefreshErrorType.unknown:
        return error.attemptNumber < 2; // Give unknown errors one retry
    }
  }

  /// Calculate retry delay with exponential backoff
  Duration calculateRetryDelay(int attemptNumber) {
    // Exponential backoff: 2s, 4s, 8s, etc.
    final delaySeconds = _initialRetryDelay.inSeconds * (1 << (attemptNumber - 1));
    return Duration(seconds: min(delaySeconds, 30)); // Cap at 30 seconds
  }

  /// Handle session refresh error with appropriate strategy
  Future<bool> handleRefreshError(RefreshError error) async {
    _authLogger.addLogEntry(
      'Handling refresh error: $error',
      importance: AuthLogImportance.critical,
    );

    switch (error.type) {
      case RefreshErrorType.network:
        return await _handleNetworkError(error);
      
      case RefreshErrorType.authentication:
      case RefreshErrorType.tokenExpired:
        return await _handleAuthenticationError(error);
      
      case RefreshErrorType.serverError:
        return await _handleServerError(error);
      
      case RefreshErrorType.unknown:
        return await _handleUnknownError(error);
    }
  }

  /// Handle network-related errors
  Future<bool> _handleNetworkError(RefreshError error) async {
    _authLogger.addLogEntry(
      'Network error during refresh, will retry if attempts remain',
      importance: AuthLogImportance.important,
    );

    if (error.attemptNumber < _maxRefreshRetries) {
      final delay = calculateRetryDelay(error.attemptNumber);
      _authLogger.addLogEntry(
        'Scheduling retry in ${delay.inSeconds} seconds (attempt ${error.attemptNumber + 1}/$_maxRefreshRetries)',
        importance: AuthLogImportance.important,
      );
      return true; // Indicate retry should be attempted
    }

    _authLogger.addLogEntry(
      'Max network retry attempts reached, giving up',
      importance: AuthLogImportance.critical,
    );
    return false;
  }

  /// Handle authentication-related errors
  Future<bool> _handleAuthenticationError(RefreshError error) async {
    _authLogger.addLogEntry(
      'Authentication error during refresh: ${error.message}',
      importance: AuthLogImportance.critical,
    );

    // Authentication errors typically require user intervention
    // Log the error and indicate that refresh should not be retried
    _authLogger.addLogEntry(
      'Authentication error requires user re-login',
      importance: AuthLogImportance.critical,
    );

    return false; // Do not retry authentication errors
  }

  /// Handle server-related errors
  Future<bool> _handleServerError(RefreshError error) async {
    _authLogger.addLogEntry(
      'Server error during refresh: ${error.message}',
      importance: AuthLogImportance.critical,
    );

    if (error.attemptNumber < _maxRefreshRetries) {
      final delay = calculateRetryDelay(error.attemptNumber);
      _authLogger.addLogEntry(
        'Server error, will retry in ${delay.inSeconds} seconds (attempt ${error.attemptNumber + 1}/$_maxRefreshRetries)',
        importance: AuthLogImportance.important,
      );
      return true; // Indicate retry should be attempted
    }

    _authLogger.addLogEntry(
      'Max server error retry attempts reached',
      importance: AuthLogImportance.critical,
    );
    return false;
  }

  /// Handle unknown errors
  Future<bool> _handleUnknownError(RefreshError error) async {
    _authLogger.addLogEntry(
      'Unknown error during refresh: ${error.message}',
      importance: AuthLogImportance.critical,
    );

    // Give unknown errors a limited number of retries
    if (error.attemptNumber < 2) {
      final delay = calculateRetryDelay(error.attemptNumber);
      _authLogger.addLogEntry(
        'Unknown error, will retry once in ${delay.inSeconds} seconds',
        importance: AuthLogImportance.important,
      );
      return true;
    }

    _authLogger.addLogEntry(
      'Unknown error retry limit reached',
      importance: AuthLogImportance.critical,
    );
    return false;
  }

  /// Handle session corruption recovery
  Future<void> handleSessionCorruption(dynamic error) async {
    _authLogger.addLogEntry(
      'Handling session corruption: $error',
      importance: AuthLogImportance.critical,
    );

    try {
      // Log the corruption details
      _authLogger.addLogEntry(
        'Session corruption detected, attempting recovery',
        importance: AuthLogImportance.critical,
      );

      // Additional corruption handling logic can be added here
      // For now, we'll just log the event
      
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during session corruption handling: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Handle session expiration
  Future<void> handleSessionExpiration({required String reason}) async {
    _authLogger.addLogEntry(
      'Handling session expiration: $reason',
      importance: AuthLogImportance.critical,
    );

    try {
      // Log the expiration
      _authLogger.addLogEntry(
        'Session expired, user will need to re-authenticate',
        importance: AuthLogImportance.critical,
      );

      // Additional expiration handling logic can be added here
      
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during session expiration handling: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Check if error indicates session corruption
  bool isSessionCorruptionError(dynamic error) {
    if (error == null) return false;
    
    final errorString = error.toString().toLowerCase();
    return errorString.contains('corrupt') ||
           errorString.contains('invalid format') ||
           errorString.contains('malformed') ||
           errorString.contains('parse error');
  }

  /// Check if error indicates network issues
  bool isNetworkError(dynamic error) {
    if (error == null) return false;
    
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('timeout') ||
           errorString.contains('unreachable');
  }

  /// Get user-friendly error message
  String getUserFriendlyErrorMessage(RefreshError error) {
    switch (error.type) {
      case RefreshErrorType.network:
        return 'Network connection issue. Please check your internet connection and try again.';
      
      case RefreshErrorType.authentication:
      case RefreshErrorType.tokenExpired:
        return 'Your session has expired. Please sign in again.';
      
      case RefreshErrorType.serverError:
        return 'Server is temporarily unavailable. Please try again later.';
      
      case RefreshErrorType.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;
import 'package:supabase_flutter/supabase_flutter.dart';

import 'auth_logger.dart';
import 'auth_logger_provider.dart';
import 'auth_providers.dart';
import 'auth_repository.dart';
import 'session_error_handler.dart';
import 'session_refresh_handler.dart';
import 'session_state_manager.dart';
import 'session_validator.dart';

/// Provider for the session manager
final sessionManagerProvider = rp.Provider<SessionManager>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  final authLogger = ref.watch(authLoggerProvider);
  return SessionManager(repository, authLogger);
});

/// Provider for session status
final sessionStatusProvider = rp.StateProvider<SessionStatus>(
  (ref) => SessionStatus.unknown,
);

/// Main session manager that coordinates all session-related operations
class SessionManager {
  final AuthRepository _repository;
  final AuthLogger _authLogger;

  // Split components
  late final SessionValidator _validator;
  late final SessionErrorHandler _errorHandler;
  late final SessionRefreshHandler _refreshHandler;
  late final SessionStateManager _stateManager;

  // State management
  bool _isInitialized = false;
  Timer? _refreshTimer;
  Timer? _activityTimer;

  SessionManager(this._repository, this._authLogger) {
    _initializeComponents();
  }

  /// Initialize the split components
  void _initializeComponents() {
    _validator = SessionValidator(_authLogger);
    _errorHandler = SessionErrorHandler(_authLogger);
    _refreshHandler = SessionRefreshHandler(
      _repository,
      _authLogger,
      _errorHandler,
    );
    _stateManager = SessionStateManager(_authLogger, _validator);
  }

  /// Initialize the session manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _authLogger.addLogEntry(
        'Initializing SessionManager',
        importance: AuthLogImportance.important,
      );

      // Load persisted session if available
      await _loadPersistedSession();

      // Set up periodic refresh timer
      _setupRefreshTimer();

      // Set up activity tracking
      _setupActivityTracking();

      _isInitialized = true;

      _authLogger.addLogEntry(
        'SessionManager initialized successfully',
        importance: AuthLogImportance.important,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Failed to initialize SessionManager: $e',
        importance: AuthLogImportance.critical,
      );
      rethrow;
    }
  }

  /// Load persisted session data and attempt to restore session
  Future<void> _loadPersistedSession() async {
    try {
      final sessionData = await _stateManager.loadPersistedSession();

      if (sessionData != null) {
        _authLogger.addLogEntry(
          'Found persisted session data, attempting to restore session',
          importance: AuthLogImportance.important,
        );

        // Check if the persisted session is still valid
        final currentSession = _repository.getCurrentSession();
        if (currentSession != null) {
          final isValid = await _stateManager.validateCurrentState(
            currentSession,
          );
          if (isValid) {
            _authLogger.addLogEntry(
              'Persisted session is valid',
              importance: AuthLogImportance.important,
            );
            return;
          }
        }

        // If session is invalid, clear persisted data
        await _stateManager.clearPersistedSession();
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error loading persisted session: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Set up periodic refresh timer
  void _setupRefreshTimer() {
    _refreshTimer?.cancel();

    // Check for refresh every 30 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkAndRefreshSession();
    });
  }

  /// Set up activity tracking
  void _setupActivityTracking() {
    _activityTimer?.cancel();

    // Update activity every 5 minutes
    _activityTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _stateManager.updateLastActiveTime();
    });
  }

  /// Check if session needs refresh and refresh if necessary
  Future<void> _checkAndRefreshSession() async {
    try {
      final currentSession = _repository.getCurrentSession();

      if (currentSession == null) {
        _stateManager.updateStatus(SessionStatus.invalid);
        return;
      }

      // Check if refresh is needed
      if (_refreshHandler.needsRefresh(currentSession)) {
        await refreshSession();
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during periodic session check: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Refresh the current session
  Future<bool> refreshSession() async {
    if (!_isInitialized) {
      await initialize();
    }

    _stateManager.updateStatus(SessionStatus.refreshing);

    try {
      final success = await _refreshHandler.refreshSession();

      if (success) {
        final currentSession = _repository.getCurrentSession();
        if (currentSession != null) {
          await _stateManager.persistSession(currentSession);
          _stateManager.updateStatus(SessionStatus.valid);
        }
      } else {
        _stateManager.updateStatus(SessionStatus.expired);
      }

      return success;
    } catch (e) {
      _authLogger.addLogEntry(
        'Session refresh failed: $e',
        importance: AuthLogImportance.critical,
      );
      _stateManager.updateStatus(SessionStatus.invalid);
      return false;
    }
  }

  /// Validate session for app resume
  Future<bool> validateSessionForResume() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final currentSession = _repository.getCurrentSession();
      final validationResult = await _validator.validateSessionForResume(
        currentSession,
      );

      if (!validationResult.isValid) {
        _authLogger.addLogEntry(
          'Session validation failed: ${validationResult.reason}',
          importance: AuthLogImportance.critical,
        );

        // Handle different failure types
        switch (validationResult.failureType) {
          case ResumeValidationFailureType.sessionExpired:
            _stateManager.updateStatus(SessionStatus.expired);
            break;
          case ResumeValidationFailureType.sessionCorrupted:
            await _stateManager.clearPersistedSession();
            _stateManager.updateStatus(SessionStatus.invalid);
            break;
          case ResumeValidationFailureType.extendedInactivity:
            // Attempt to refresh the session
            return await refreshSession();
          default:
            _stateManager.updateStatus(SessionStatus.unknown);
        }

        return false;
      }

      _stateManager.updateStatus(SessionStatus.valid);
      return true;
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during session validation: $e',
        importance: AuthLogImportance.critical,
      );
      _stateManager.updateStatus(SessionStatus.invalid);
      return false;
    }
  }

  /// Handle session changes (called when user signs in/out)
  Future<void> handleSessionChange(Session? session) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      if (session != null) {
        // New session - persist it
        await _stateManager.persistSession(session);
        _stateManager.updateStatus(SessionStatus.valid);

        _authLogger.addLogEntry(
          'New session established',
          importance: AuthLogImportance.important,
        );
      } else {
        // Session ended - clear persisted data
        await _stateManager.clearPersistedSession();
        _stateManager.updateStatus(SessionStatus.invalid);

        _authLogger.addLogEntry(
          'Session ended',
          importance: AuthLogImportance.important,
        );
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error handling session change: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Get current session status
  SessionStatus get currentStatus => _stateManager.currentStatus;

  /// Stream of session status changes
  Stream<SessionStatus> get statusStream => _stateManager.statusStream;

  /// Check if session manager is initialized
  bool get isInitialized => _isInitialized;

  /// Check if currently refreshing
  bool get isRefreshing => _refreshHandler.isRefreshing;

  /// Get time until next refresh
  Duration? getTimeUntilNextRefresh() {
    final currentSession = _repository.getCurrentSession();
    return _refreshHandler.getTimeUntilNextRefresh(currentSession);
  }

  /// Force refresh session
  Future<bool> forceRefresh() async {
    return await _refreshHandler.forceRefresh();
  }

  /// Handle app resumed lifecycle event
  Future<void> onAppResumed() async {
    if (!_isInitialized) {
      await initialize();
    }

    _authLogger.addLogEntry(
      'App resumed - checking session validity',
      importance: AuthLogImportance.important,
    );

    try {
      // Check if we need to resume session validation
      final success = await validateSessionForResume();
      if (!success) {
        _authLogger.addLogEntry(
          'Session resume failed - session may be expired',
          importance: AuthLogImportance.important,
        );
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error handling app resume: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Handle app paused lifecycle event
  Future<void> onAppPaused() async {
    _authLogger.addLogEntry(
      'App paused - saving session state',
      importance: AuthLogImportance.important,
    );

    try {
      // Update last activity time
      await _stateManager.updateLastActiveTime();
    } catch (e) {
      _authLogger.addLogEntry(
        'Error handling app pause: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Handle app inactive lifecycle event
  Future<void> onAppInactive() async {
    _authLogger.addLogEntry(
      'App inactive - pausing session activities',
      importance: AuthLogImportance.important,
    );

    try {
      // Update last activity time
      await _stateManager.updateLastActiveTime();
    } catch (e) {
      _authLogger.addLogEntry(
        'Error handling app inactive: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Handle app detached lifecycle event
  Future<void> onAppDetached() async {
    _authLogger.addLogEntry(
      'App detached - cleaning up session resources',
      importance: AuthLogImportance.important,
    );

    try {
      // Cancel timers and save state
      _refreshTimer?.cancel();
      _activityTimer?.cancel();

      // Update last activity time before detaching
      await _stateManager.updateLastActiveTime();
    } catch (e) {
      _authLogger.addLogEntry(
        'Error handling app detached: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Handle sign out event
  Future<void> onSignOut() async {
    _authLogger.addLogEntry(
      'Sign out event - clearing session data',
      importance: AuthLogImportance.important,
    );

    try {
      // Clear persisted session data
      await _stateManager.clearPersistedSession();

      // Update status to invalid
      _stateManager.updateStatus(SessionStatus.invalid);

      // Cancel any active timers
      _refreshTimer?.cancel();
      _activityTimer?.cancel();
    } catch (e) {
      _authLogger.addLogEntry(
        'Error handling sign out: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _refreshTimer?.cancel();
    _activityTimer?.cancel();
    _stateManager.dispose();
    _isInitialized = false;

    _authLogger.addLogEntry(
      'SessionManager disposed',
      importance: AuthLogImportance.important,
    );
  }
}

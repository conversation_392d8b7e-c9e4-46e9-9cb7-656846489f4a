import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'auth_logger.dart';
import 'auth_repository.dart';
import 'session_error_handler.dart';

/// Handles session refresh operations
class SessionRefreshHandler {
  final AuthRepository _repository;
  final AuthLogger _authLogger;
  final SessionErrorHandler _errorHandler;

  bool _isRefreshing = false;
  int _refreshAttempts = 0;

  // Constants for token refresh
  static const Duration _minRefreshThreshold = Duration(minutes: 5);
  static const Duration _maxRefreshThreshold = Duration(minutes: 20);
  static const double _refreshThresholdPercentage = 0.25;
  static const Duration _proactiveRefreshBuffer = Duration(minutes: 2);
  static const int _maxRefreshRetries = 3;

  // Keys for storing refresh data
  static const String _lastRefreshTimeKey = 'last_refresh_time';

  SessionRefreshHandler(this._repository, this._authLogger, this._errorHandler);

  /// Check if session needs refresh
  bool needsRefresh(Session? session, {bool isAppActive = true}) {
    if (session == null) return false;

    if (session.expiresAt == null) {
      _authLogger.addLogEntry(
        'Session has no expiry time, assuming it needs refresh',
        importance: AuthLogImportance.important,
      );
      return true;
    }

    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();

    // Check if already expired
    if (now.isAfter(expiresAt)) {
      _authLogger.addLogEntry(
        'Session has expired',
        importance: AuthLogImportance.critical,
      );
      return true;
    }

    // Calculate refresh threshold
    final refreshThreshold = _calculateRefreshThreshold(session, isAppActive);
    final shouldRefresh = now.isAfter(expiresAt.subtract(refreshThreshold));

    if (shouldRefresh) {
      _authLogger.addLogEntry(
        'Session needs refresh (expires in ${expiresAt.difference(now).inMinutes} minutes, threshold: ${refreshThreshold.inMinutes} minutes)',
        importance: AuthLogImportance.important,
      );
    }

    return shouldRefresh;
  }

  /// Calculate dynamic refresh threshold based on token lifetime and app state
  Duration _calculateRefreshThreshold(Session session, bool isAppActive) {
    if (session.expiresAt == null) {
      return _minRefreshThreshold;
    }

    // Calculate token lifetime
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session.expiresAt! * 1000,
    );
    final now = DateTime.now();
    final tokenLifetime = expiresAt.difference(now);

    // Calculate threshold as percentage of remaining lifetime
    final calculatedThreshold = Duration(
      milliseconds: (tokenLifetime.inMilliseconds * _refreshThresholdPercentage)
          .round(),
    );

    // Apply proactive buffer if user is active
    final proactiveThreshold = isAppActive
        ? calculatedThreshold + _proactiveRefreshBuffer
        : calculatedThreshold;

    // Ensure threshold is within bounds
    if (proactiveThreshold < _minRefreshThreshold) {
      return _minRefreshThreshold;
    } else if (proactiveThreshold > _maxRefreshThreshold) {
      return _maxRefreshThreshold;
    }

    return proactiveThreshold;
  }

  /// Refresh the current session
  Future<bool> refreshSession() async {
    if (_isRefreshing) {
      _authLogger.addLogEntry(
        'Session refresh already in progress, skipping',
        importance: AuthLogImportance.verbose,
      );
      return false;
    }

    _isRefreshing = true;
    _refreshAttempts = 0;

    try {
      _authLogger.addLogEntry(
        'Starting session refresh',
        importance: AuthLogImportance.important,
      );

      final success = await _attemptRefreshWithRetry();

      if (success) {
        await _updateLastRefreshTime();
        _authLogger.addLogEntry(
          'Session refresh completed successfully',
          importance: AuthLogImportance.important,
        );
      } else {
        _authLogger.addLogEntry(
          'Session refresh failed after all attempts',
          importance: AuthLogImportance.critical,
        );
      }

      return success;
    } finally {
      _isRefreshing = false;
      _refreshAttempts = 0;
    }
  }

  /// Attempt refresh with retry logic
  Future<bool> _attemptRefreshWithRetry() async {
    while (_refreshAttempts < _maxRefreshRetries) {
      _refreshAttempts++;

      try {
        _authLogger.addLogEntry(
          'Refresh attempt $_refreshAttempts/$_maxRefreshRetries',
          importance: AuthLogImportance.important,
        );

        final session = await _repository.refreshSession();

        if (session != null) {
          _authLogger.addLogEntry(
            'Session refresh successful on attempt $_refreshAttempts',
            importance: AuthLogImportance.important,
          );
          return true;
        } else {
          _authLogger.addLogEntry(
            'Session refresh returned null session on attempt $_refreshAttempts',
            importance: AuthLogImportance.critical,
          );
        }
      } catch (error) {
        final refreshError = _errorHandler.categorizeRefreshError(
          error,
          _refreshAttempts,
        );

        _authLogger.addLogEntry(
          'Refresh attempt $_refreshAttempts failed: ${refreshError.message}',
          importance: AuthLogImportance.critical,
        );

        // Check if we should retry
        final shouldRetry = await _errorHandler.handleRefreshError(
          refreshError,
        );

        if (!shouldRetry) {
          _authLogger.addLogEntry(
            'Error handler indicates no retry should be attempted',
            importance: AuthLogImportance.critical,
          );
          break;
        }

        // If this isn't the last attempt, wait before retrying
        if (_refreshAttempts < _maxRefreshRetries) {
          final delay = _errorHandler.calculateRetryDelay(_refreshAttempts);
          _authLogger.addLogEntry(
            'Waiting ${delay.inSeconds} seconds before retry',
            importance: AuthLogImportance.important,
          );
          await Future.delayed(delay);
        }
      }
    }

    return false;
  }

  /// Update last refresh time
  Future<void> _updateLastRefreshTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _lastRefreshTimeKey,
        DateTime.now().toIso8601String(),
      );

      _authLogger.addLogEntry(
        'Updated last refresh time',
        importance: AuthLogImportance.verbose,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Error updating last refresh time: $e',
        importance: AuthLogImportance.important,
      );
    }
  }

  /// Get last refresh time
  Future<DateTime?> getLastRefreshTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastRefreshTimeString = prefs.getString(_lastRefreshTimeKey);

      if (lastRefreshTimeString != null) {
        return DateTime.tryParse(lastRefreshTimeString);
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error getting last refresh time: $e',
        importance: AuthLogImportance.important,
      );
    }

    return null;
  }

  /// Check if currently refreshing
  bool get isRefreshing => _isRefreshing;

  /// Get current refresh attempt number
  int get currentRefreshAttempt => _refreshAttempts;

  /// Force refresh regardless of timing
  Future<bool> forceRefresh() async {
    _authLogger.addLogEntry(
      'Force refresh requested',
      importance: AuthLogImportance.important,
    );

    return await refreshSession();
  }

  /// Check if refresh was recent (within last 5 minutes)
  Future<bool> wasRecentlyRefreshed() async {
    final lastRefreshTime = await getLastRefreshTime();
    if (lastRefreshTime == null) return false;

    final timeSinceRefresh = DateTime.now().difference(lastRefreshTime);
    return timeSinceRefresh < const Duration(minutes: 5);
  }

  /// Get time until next recommended refresh
  Duration? getTimeUntilNextRefresh(
    Session? session, {
    bool isAppActive = true,
  }) {
    if (session?.expiresAt == null) return null;

    final expiresAt = DateTime.fromMillisecondsSinceEpoch(
      session!.expiresAt! * 1000,
    );
    final refreshThreshold = _calculateRefreshThreshold(session, isAppActive);
    final refreshTime = expiresAt.subtract(refreshThreshold);
    final now = DateTime.now();

    if (now.isAfter(refreshTime)) {
      return Duration.zero; // Should refresh now
    }

    return refreshTime.difference(now);
  }
}

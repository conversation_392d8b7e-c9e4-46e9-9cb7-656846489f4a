import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../sync/sync_logger.dart';

/// Enum for auth log importance
enum AuthLogImportance {
  /// Critical logs that should always be shown in sync logs
  /// Examples: login failures, session expirations, token refresh errors
  critical,

  /// Important logs that provide key information but aren't critical
  /// Examples: successful logins, sign-outs
  important,

  /// Verbose logs that are useful for debugging but not for regular users
  /// Examples: session details, app lifecycle events
  verbose,
}

/// Class representing an auth log entry
class AuthLogEntry {
  final DateTime timestamp;
  final String message;
  final AuthLogImportance importance;

  AuthLogEntry({
    required this.timestamp,
    required this.message,
    required this.importance,
  });

  String get formattedUtcTimestamp =>
      DateFormat('yyyy-MM-dd HH:mm:ss').format(timestamp.toUtc());

  String get formattedLocalTimestamp =>
      DateFormat('yyyy-MM-dd HH:mm:ss').format(timestamp.toLocal());

  String get formattedEntry =>
      '[$formattedLocalTimestamp LOCAL / $formattedUtcTimestamp UTC] $message';

  LogLevel get logLevel => _getLogLevelForImportance(importance);

  static LogLevel _getLogLevelForImportance(AuthLogImportance importance) {
    switch (importance) {
      case AuthLogImportance.critical:
        return LogLevel.error;
      case AuthLogImportance.important:
        return LogLevel.info;
      case AuthLogImportance.verbose:
        return LogLevel.debug;
    }
  }
}

/// Provider for auth logs
final authLogsProvider = rp.StateProvider<List<AuthLogEntry>>((ref) => []);

/// Provider for filtered auth logs based on importance
final filteredAuthLogsProvider = rp.Provider<List<AuthLogEntry>>((ref) {
  final allAuthLogs = ref.watch(authLogsProvider);
  final importanceFilter = ref.watch(authLogImportanceFilterProvider);

  return allAuthLogs.where((log) {
    switch (importanceFilter) {
      case AuthLogImportance.verbose:
        return true; // Show all logs
      case AuthLogImportance.important:
        return log.importance == AuthLogImportance.important ||
            log.importance == AuthLogImportance.critical;
      case AuthLogImportance.critical:
        return log.importance == AuthLogImportance.critical;
    }
  }).toList();
});

/// Provider for the auth log importance filter (moved from app_logs_screen.dart)
final authLogImportanceFilterProvider = rp.StateProvider<AuthLogImportance>(
  (ref) => AuthLogImportance.important,
);

/// Class responsible for logging authentication events
class AuthLogger {
  final rp.Ref _ref;

  /// In-memory log storage
  final List<AuthLogEntry> _authLogs = [];

  /// Maximum number of log entries to keep
  final int _maxLogEntries;

  /// Constructor
  AuthLogger(this._ref, {int maxLogEntries = 200})
    : _maxLogEntries = maxLogEntries;

  /// Add a log entry
  ///
  /// [message] The log message to add
  /// [importance] The importance level of the log (default: verbose)
  void addLogEntry(
    String message, {
    AuthLogImportance importance = AuthLogImportance.verbose,
  }) {
    // Skip verbose logs that don't provide meaningful diagnostic information
    if (importance == AuthLogImportance.verbose &&
        _shouldSkipVerboseLog(message)) {
      return;
    }

    final entry = AuthLogEntry(
      timestamp: DateTime.now(),
      message: message,
      importance: importance,
    );

    // Add to in-memory auth logs
    _authLogs.add(entry);

    // Trim log if it gets too large
    if (_authLogs.length > _maxLogEntries) {
      _authLogs.removeRange(0, _authLogs.length - _maxLogEntries);
    }

    // Update the provider
    _ref.read(authLogsProvider.notifier).state = List.from(_authLogs);

    // Print to console for debugging (all logs)
    debugPrint('AUTH: ${entry.formattedEntry}');
  }

  /// Check if a verbose log should be skipped
  bool _shouldSkipVerboseLog(String message) {
    // List of message patterns to skip for verbose logs
    final skipPatterns = [
      'App lifecycle event:',
      'Session still valid',
      'Token still valid',
      'No refresh needed',
      'Session details:',
      'Session refresh already in progress',
      'Checking session validity',
      'Session check completed',
      'Token validation successful',
      'Routine session check',
      'Background session validation',
      'Periodic auth check',
    ];

    // Skip if the message contains any of the patterns
    return skipPatterns.any((pattern) => message.contains(pattern));
  }

  // Using the static method from AuthLogEntry instead

  /// Log session details
  ///
  /// [session] The session to log details for
  /// [importance] The importance level for the session summary (default: important)
  /// Note: Detailed session information is always logged as verbose
  void logSessionDetails(
    Session? session, {
    AuthLogImportance importance = AuthLogImportance.important,
  }) {
    if (session == null) {
      addLogEntry('Session is null', importance: AuthLogImportance.important);
      return;
    }

    try {
      final expiresAt = session.expiresAt != null
          ? DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)
          : null;

      final now = DateTime.now();
      final timeUntilExpiry = expiresAt?.difference(now);

      // Log a summary with the specified importance
      addLogEntry(
        'Session active for user: ${session.user.email}, expires in: ${timeUntilExpiry != null ? _formatDuration(timeUntilExpiry) : "unknown"}',
        importance: importance,
      );

      // Only log detailed session information if it's not verbose or if it's about to expire
      final isAboutToExpire =
          timeUntilExpiry != null && timeUntilExpiry.inMinutes < 30;

      if (importance != AuthLogImportance.verbose || isAboutToExpire) {
        // Include more details for important logs
        addLogEntry('Session ID: ${session.user.id}', importance: importance);
        addLogEntry(
          'Expires at: ${expiresAt?.toLocal().toString() ?? 'unknown'}',
          importance: importance,
        );

        if (timeUntilExpiry != null) {
          // Add warning if token is about to expire
          if (isAboutToExpire) {
            addLogEntry(
              'WARNING: Token expires soon (${timeUntilExpiry.inMinutes} minutes)',
              importance: AuthLogImportance.critical,
            );
          }

          // Check if refresh token is present
          if (session.refreshToken == null || session.refreshToken!.isEmpty) {
            addLogEntry(
              'WARNING: No refresh token available for session',
              importance: AuthLogImportance.critical,
            );
          }
        }
      }
    } catch (e) {
      addLogEntry(
        'Error logging session details: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Log authentication state change
  void logAuthStateChange(AuthChangeEvent event, Session? session) {
    // Log the event with appropriate importance
    switch (event) {
      case AuthChangeEvent.signedIn:
        addLogEntry(
          'AUTH STATE CHANGE: User signed in',
          importance: AuthLogImportance.important,
        );
        if (session != null) {
          final user = session.user;
          addLogEntry(
            'User ID: ${user.id}, Email: ${user.email}',
            importance: AuthLogImportance.important,
          );
          logSessionDetails(session, importance: AuthLogImportance.important);
        } else {
          addLogEntry(
            'WARNING: Sign-in event with null session',
            importance: AuthLogImportance.critical,
          );
        }
        break;

      case AuthChangeEvent.signedOut:
        addLogEntry(
          'AUTH STATE CHANGE: User signed out',
          importance: AuthLogImportance.critical,
        );
        // Log stack trace to help identify unexpected sign-outs
        addLogEntry(
          'Sign-out stack trace: ${StackTrace.current}',
          importance: AuthLogImportance.critical,
        );
        break;

      case AuthChangeEvent.userUpdated:
        addLogEntry(
          'AUTH STATE CHANGE: User updated',
          importance: AuthLogImportance.important,
        );
        logSessionDetails(session, importance: AuthLogImportance.important);
        break;

      case AuthChangeEvent.passwordRecovery:
        addLogEntry(
          'AUTH STATE CHANGE: Password recovery initiated',
          importance: AuthLogImportance.important,
        );
        break;

      case AuthChangeEvent.tokenRefreshed:
        // Upgrade token refresh to important for better visibility
        addLogEntry(
          'AUTH STATE CHANGE: Token refreshed',
          importance: AuthLogImportance.important,
        );
        logSessionDetails(session, importance: AuthLogImportance.important);
        break;

      case AuthChangeEvent.mfaChallengeVerified:
        addLogEntry(
          'AUTH STATE CHANGE: MFA challenge verified',
          importance: AuthLogImportance.important,
        );
        break;

      case AuthChangeEvent.initialSession:
        addLogEntry(
          'AUTH STATE CHANGE: Initial session loaded',
          importance: AuthLogImportance.important,
        );
        logSessionDetails(session, importance: AuthLogImportance.important);
        break;

      default:
        // Handle deprecated or unknown events
        addLogEntry(
          'AUTH STATE CHANGE: Unknown or deprecated event: $event',
          importance: AuthLogImportance.important,
        );
        break;
    }
  }

  /// Log session refresh attempt
  void logSessionRefreshAttempt() {
    // Upgrade refresh attempts to important for better visibility
    addLogEntry(
      'SESSION REFRESH: Attempting to refresh session',
      importance: AuthLogImportance.important,
    );
  }

  /// Log session refresh result
  void logSessionRefreshResult(Session? session, {String? error}) {
    if (error != null) {
      // Refresh failures are critical
      addLogEntry(
        'SESSION REFRESH FAILED: $error',
        importance: AuthLogImportance.critical,
      );
      // Log stack trace for refresh failures
      addLogEntry(
        'Refresh failure stack trace: ${StackTrace.current}',
        importance: AuthLogImportance.critical,
      );
      return;
    }

    if (session != null) {
      // Successful refreshes are important
      addLogEntry(
        'SESSION REFRESH SUCCEEDED',
        importance: AuthLogImportance.important,
      );

      // Log detailed session information
      final expiresAt = session.expiresAt != null
          ? DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)
          : null;

      if (expiresAt != null) {
        final now = DateTime.now();
        final timeUntilExpiry = expiresAt.difference(now);

        addLogEntry(
          'New session expires at: ${expiresAt.toLocal()} (in ${_formatDuration(timeUntilExpiry)})',
          importance: AuthLogImportance.important,
        );

        // Check if the new session will expire soon
        if (timeUntilExpiry.inMinutes < 60) {
          addLogEntry(
            'WARNING: New session will expire soon (${timeUntilExpiry.inMinutes} minutes)',
            importance: AuthLogImportance.critical,
          );
        }
      }
    } else {
      // Null session is critical
      addLogEntry(
        'SESSION REFRESH FAILED: Returned null session',
        importance: AuthLogImportance.critical,
      );
      addLogEntry(
        'Null session stack trace: ${StackTrace.current}',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Log app lifecycle event that might affect authentication
  void logAppLifecycleEvent(String event) {
    // Only log resume events as important, others remain verbose
    if (event == 'resumed') {
      addLogEntry(
        'APP LIFECYCLE: App resumed',
        importance: AuthLogImportance.important,
      );
    } else {
      addLogEntry(
        'App lifecycle event: $event',
        importance: AuthLogImportance.verbose,
      );
    }
  }

  /// Get all logs
  List<AuthLogEntry> getLogs() {
    return List.unmodifiable(_authLogs);
  }

  /// Get filtered logs by importance
  List<AuthLogEntry> getFilteredLogs(AuthLogImportance minImportance) {
    return _authLogs
        .where(
          (log) =>
              _getImportanceValue(log.importance) >=
              _getImportanceValue(minImportance),
        )
        .toList();
  }

  /// Get importance value for comparison
  int _getImportanceValue(AuthLogImportance importance) {
    switch (importance) {
      case AuthLogImportance.verbose:
        return 0;
      case AuthLogImportance.important:
        return 1;
      case AuthLogImportance.critical:
        return 2;
    }
  }

  /// Clear logs
  void clearLogs() {
    _authLogs.clear();
    _ref.read(authLogsProvider.notifier).state = [];
  }

  /// Format duration in a human-readable format
  String _formatDuration(Duration duration) {
    if (duration.isNegative) {
      return 'expired';
    } else if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours.remainder(24)}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes.remainder(60)}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds.remainder(60)}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }
}

import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'auth_logger.dart';

/// Types of resume validation failures
enum ResumeValidationFailureType {
  noSession,
  sessionExpired,
  sessionCorrupted,
  extendedInactivity,
  networkIssue,
  persistenceFailure,
}

/// Result of resume session validation
class ResumeValidationResult {
  final bool isValid;
  final ResumeValidationFailureType? failureType;
  final String reason;
  final Session? currentSession;
  final Duration? inactivityDuration;

  ResumeValidationResult.valid(this.currentSession)
    : isValid = true,
      failureType = null,
      reason = 'Session validation passed',
      inactivityDuration = null;

  ResumeValidationResult.invalid({
    required this.failureType,
    required this.reason,
    this.currentSession,
    this.inactivityDuration,
  }) : isValid = false;
}

/// Handles session validation logic
class SessionValidator {
  final AuthLogger _authLogger;

  // Keys for storing session data
  static const String _sessionExpiryKey = 'session_expiry_time';
  static const String _sessionRefreshTokenKey = 'session_refresh_token';
  static const String _sessionAccessTokenKey = 'session_access_token';
  static const String _lastAppActiveTimeKey = 'last_app_active_time';
  static const String _sessionIntegrityHashKey = 'session_integrity_hash';
  static const String _sessionVersionKey = 'session_version';

  // Constants for validation
  static const Duration _extendedInactivityThreshold = Duration(minutes: 30);
  static const int _currentSessionVersion = 1;

  SessionValidator(this._authLogger);

  /// Validate and load session data from SharedPreferences
  Future<Map<String, dynamic>?> validateAndLoadSessionData(
    SharedPreferences prefs,
  ) async {
    try {
      // Check if we have the required keys
      if (!prefs.containsKey(_sessionExpiryKey) ||
          !prefs.containsKey(_sessionRefreshTokenKey) ||
          !prefs.containsKey(_sessionAccessTokenKey)) {
        _authLogger.addLogEntry(
          'Missing required session keys in SharedPreferences',
          importance: AuthLogImportance.verbose,
        );
        return null;
      }

      // Get session data
      final expiryTimeString = prefs.getString(_sessionExpiryKey);
      final refreshToken = prefs.getString(_sessionRefreshTokenKey);
      final accessToken = prefs.getString(_sessionAccessTokenKey);

      if (expiryTimeString == null ||
          refreshToken == null ||
          accessToken == null) {
        _authLogger.addLogEntry(
          'Session data contains null values',
          importance: AuthLogImportance.verbose,
        );
        return null;
      }

      // Parse expiry time
      final expiryTime = DateTime.tryParse(expiryTimeString);
      if (expiryTime == null) {
        _authLogger.addLogEntry(
          'Invalid expiry time format: $expiryTimeString',
          importance: AuthLogImportance.important,
        );
        return null;
      }

      // Validate session integrity
      if (!await _validateSessionIntegrity(
        prefs,
        refreshToken,
        accessToken,
        expiryTime,
      )) {
        _authLogger.addLogEntry(
          'Session integrity validation failed',
          importance: AuthLogImportance.critical,
        );
        return null;
      }

      return {
        'expiryTime': expiryTime,
        'refreshToken': refreshToken,
        'accessToken': accessToken,
      };
    } catch (e) {
      _authLogger.addLogEntry(
        'Error validating session data: $e',
        importance: AuthLogImportance.critical,
      );
      return null;
    }
  }

  /// Validate session integrity using hash verification
  Future<bool> _validateSessionIntegrity(
    SharedPreferences prefs,
    String refreshToken,
    String accessToken,
    DateTime expiryTime,
  ) async {
    try {
      final storedHash = prefs.getString(_sessionIntegrityHashKey);
      final storedVersion = prefs.getInt(_sessionVersionKey) ?? 0;

      // Check version compatibility
      if (storedVersion != _currentSessionVersion) {
        _authLogger.addLogEntry(
          'Session version mismatch: stored=$storedVersion, current=$_currentSessionVersion',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      if (storedHash == null) {
        _authLogger.addLogEntry(
          'No integrity hash found for session',
          importance: AuthLogImportance.important,
        );
        return false;
      }

      // Calculate expected hash
      final expectedHash = _calculateSessionHash(
        refreshToken,
        accessToken,
        expiryTime,
      );

      if (storedHash != expectedHash) {
        _authLogger.addLogEntry(
          'Session integrity hash mismatch',
          importance: AuthLogImportance.critical,
        );
        return false;
      }

      return true;
    } catch (e) {
      _authLogger.addLogEntry(
        'Error validating session integrity: $e',
        importance: AuthLogImportance.critical,
      );
      return false;
    }
  }

  /// Calculate session hash for integrity verification
  String _calculateSessionHash(
    String refreshToken,
    String accessToken,
    DateTime expiryTime,
  ) {
    final data =
        '$refreshToken:$accessToken:${expiryTime.millisecondsSinceEpoch}';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Validate session for app resume
  Future<ResumeValidationResult> validateSessionForResume(
    Session? currentSession,
  ) async {
    try {
      // Check if we have a session
      if (currentSession == null) {
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.noSession,
          reason: 'No active session found',
        );
      }

      // Check if session is expired
      if (currentSession.expiresAt != null) {
        final expiresAt = DateTime.fromMillisecondsSinceEpoch(
          currentSession.expiresAt! * 1000,
        );
        if (DateTime.now().isAfter(expiresAt)) {
          return ResumeValidationResult.invalid(
            failureType: ResumeValidationFailureType.sessionExpired,
            reason: 'Session has expired',
            currentSession: currentSession,
          );
        }
      }

      // Check for extended inactivity
      final inactivityResult = await _checkExtendedInactivity();
      if (!inactivityResult.isValid) {
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.extendedInactivity,
          reason: inactivityResult.reason,
          currentSession: currentSession,
          inactivityDuration: inactivityResult.inactivityDuration,
        );
      }

      // Check session corruption
      if (!await _validateCurrentSessionIntegrity(currentSession)) {
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.sessionCorrupted,
          reason: 'Session data appears to be corrupted',
          currentSession: currentSession,
        );
      }

      return ResumeValidationResult.valid(currentSession);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error during session resume validation: $e',
        importance: AuthLogImportance.critical,
      );
      return ResumeValidationResult.invalid(
        failureType: ResumeValidationFailureType.networkIssue,
        reason: 'Validation error: $e',
        currentSession: currentSession,
      );
    }
  }

  /// Check for extended inactivity
  Future<ResumeValidationResult> _checkExtendedInactivity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastActiveTimeString = prefs.getString(_lastAppActiveTimeKey);

      if (lastActiveTimeString == null) {
        // No last active time recorded, assume first run
        return ResumeValidationResult.valid(null);
      }

      final lastActiveTime = DateTime.tryParse(lastActiveTimeString);
      if (lastActiveTime == null) {
        _authLogger.addLogEntry(
          'Invalid last active time format: $lastActiveTimeString',
          importance: AuthLogImportance.important,
        );
        return ResumeValidationResult.valid(null);
      }

      final inactivityDuration = DateTime.now().difference(lastActiveTime);
      if (inactivityDuration > _extendedInactivityThreshold) {
        return ResumeValidationResult.invalid(
          failureType: ResumeValidationFailureType.extendedInactivity,
          reason:
              'Extended inactivity detected: ${inactivityDuration.inMinutes} minutes',
          inactivityDuration: inactivityDuration,
        );
      }

      return ResumeValidationResult.valid(null);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error checking extended inactivity: $e',
        importance: AuthLogImportance.critical,
      );
      return ResumeValidationResult.invalid(
        failureType: ResumeValidationFailureType.persistenceFailure,
        reason: 'Failed to check inactivity: $e',
      );
    }
  }

  /// Validate current session integrity
  Future<bool> _validateCurrentSessionIntegrity(Session session) async {
    try {
      // Basic validation checks
      if (session.accessToken.isEmpty) {
        _authLogger.addLogEntry(
          'Session has empty access token',
          importance: AuthLogImportance.critical,
        );
        return false;
      }

      if (session.refreshToken?.isEmpty ?? true) {
        _authLogger.addLogEntry(
          'Session has empty refresh token',
          importance: AuthLogImportance.critical,
        );
        return false;
      }

      // Additional integrity checks can be added here
      return true;
    } catch (e) {
      _authLogger.addLogEntry(
        'Error validating current session integrity: $e',
        importance: AuthLogImportance.critical,
      );
      return false;
    }
  }

  /// Generate session integrity hash for storage
  Future<String> generateSessionIntegrityHash(Session session) async {
    final expiryTime = session.expiresAt != null
        ? DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)
        : DateTime.now().add(const Duration(hours: 1));

    return _calculateSessionHash(
      session.refreshToken ?? '',
      session.accessToken,
      expiryTime,
    );
  }

  /// Store session integrity data
  Future<void> storeSessionIntegrityData(
    SharedPreferences prefs,
    Session session,
  ) async {
    try {
      final hash = await generateSessionIntegrityHash(session);
      await prefs.setString(_sessionIntegrityHashKey, hash);
      await prefs.setInt(_sessionVersionKey, _currentSessionVersion);

      _authLogger.addLogEntry(
        'Session integrity data stored',
        importance: AuthLogImportance.verbose,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Error storing session integrity data: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }
}

import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'auth_logger.dart';
import 'session_validator.dart';

/// Session status enumeration
enum SessionStatus { unknown, valid, expired, refreshing, invalid }

/// Handles session state management and persistence
class SessionStateManager {
  final AuthLogger _authLogger;
  final SessionValidator _validator;

  SessionStatus _currentStatus = SessionStatus.unknown;
  final StreamController<SessionStatus> _statusController =
      StreamController<SessionStatus>.broadcast();

  // Keys for storing session data
  static const String _sessionExpiryKey = 'session_expiry_time';
  static const String _sessionRefreshTokenKey = 'session_refresh_token';
  static const String _sessionAccessTokenKey = 'session_access_token';
  static const String _lastAppActiveTimeKey = 'last_app_active_time';

  SessionStateManager(this._authLogger, this._validator);

  /// Get current session status
  SessionStatus get currentStatus => _currentStatus;

  /// Stream of session status changes
  Stream<SessionStatus> get statusStream => _statusController.stream;

  /// Update session status and notify listeners
  void updateStatus(SessionStatus status) {
    if (_currentStatus != status) {
      final previousStatus = _currentStatus;
      _currentStatus = status;

      _authLogger.addLogEntry(
        'Session status changed from $previousStatus to $status',
        importance: AuthLogImportance.important,
      );

      _statusController.add(status);
    }
  }

  /// Persist session data to SharedPreferences
  Future<void> persistSession(Session session) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Calculate and store expiry time
      final expiryTime = session.expiresAt != null
          ? DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)
          : DateTime.now().add(const Duration(hours: 1));

      await prefs.setString(_sessionExpiryKey, expiryTime.toIso8601String());
      await prefs.setString(
        _sessionRefreshTokenKey,
        session.refreshToken ?? '',
      );
      await prefs.setString(_sessionAccessTokenKey, session.accessToken);

      // Store session integrity data
      await _validator.storeSessionIntegrityData(prefs, session);

      _authLogger.addLogEntry(
        'Session persisted successfully (expires: ${expiryTime.toIso8601String()})',
        importance: AuthLogImportance.verbose,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Error persisting session: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Load persisted session data
  Future<Map<String, dynamic>?> loadPersistedSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await _validator.validateAndLoadSessionData(prefs);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error loading persisted session: $e',
        importance: AuthLogImportance.critical,
      );
      return null;
    }
  }

  /// Clear persisted session data
  Future<void> clearPersistedSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await Future.wait([
        prefs.remove(_sessionExpiryKey),
        prefs.remove(_sessionRefreshTokenKey),
        prefs.remove(_sessionAccessTokenKey),
        prefs.remove(_lastAppActiveTimeKey),
        prefs.remove('session_integrity_hash'),
        prefs.remove('session_version'),
      ]);

      _authLogger.addLogEntry(
        'Persisted session data cleared',
        importance: AuthLogImportance.important,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Error clearing persisted session: $e',
        importance: AuthLogImportance.critical,
      );
    }
  }

  /// Update last app active time
  Future<void> updateLastActiveTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _lastAppActiveTimeKey,
        DateTime.now().toIso8601String(),
      );

      _authLogger.addLogEntry(
        'Updated last app active time',
        importance: AuthLogImportance.verbose,
      );
    } catch (e) {
      _authLogger.addLogEntry(
        'Error updating last active time: $e',
        importance: AuthLogImportance.important,
      );
    }
  }

  /// Get last app active time
  Future<DateTime?> getLastActiveTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastActiveTimeString = prefs.getString(_lastAppActiveTimeKey);

      if (lastActiveTimeString != null) {
        return DateTime.tryParse(lastActiveTimeString);
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error getting last active time: $e',
        importance: AuthLogImportance.important,
      );
    }

    return null;
  }

  /// Check if session data exists in persistence
  Future<bool> hasPersistedSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_sessionExpiryKey) &&
          prefs.containsKey(_sessionRefreshTokenKey) &&
          prefs.containsKey(_sessionAccessTokenKey);
    } catch (e) {
      _authLogger.addLogEntry(
        'Error checking for persisted session: $e',
        importance: AuthLogImportance.important,
      );
      return false;
    }
  }

  /// Get session expiry time from persistence
  Future<DateTime?> getPersistedExpiryTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryTimeString = prefs.getString(_sessionExpiryKey);

      if (expiryTimeString != null) {
        return DateTime.tryParse(expiryTimeString);
      }
    } catch (e) {
      _authLogger.addLogEntry(
        'Error getting persisted expiry time: $e',
        importance: AuthLogImportance.important,
      );
    }

    return null;
  }

  /// Check if persisted session is expired
  Future<bool> isPersistedSessionExpired() async {
    final expiryTime = await getPersistedExpiryTime();
    if (expiryTime == null) return true;

    return DateTime.now().isAfter(expiryTime);
  }

  /// Get time until persisted session expires
  Future<Duration?> getTimeUntilExpiry() async {
    final expiryTime = await getPersistedExpiryTime();
    if (expiryTime == null) return null;

    final now = DateTime.now();
    if (now.isAfter(expiryTime)) {
      return Duration.zero; // Already expired
    }

    return expiryTime.difference(now);
  }

  /// Validate current session state
  Future<bool> validateCurrentState(Session? session) async {
    if (session == null) {
      updateStatus(SessionStatus.invalid);
      return false;
    }

    // Check if session is expired
    if (session.expiresAt != null) {
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        session.expiresAt! * 1000,
      );
      if (DateTime.now().isAfter(expiresAt)) {
        updateStatus(SessionStatus.expired);
        return false;
      }
    }

    // Validate session integrity
    final validationResult = await _validator.validateSessionForResume(session);
    if (!validationResult.isValid) {
      switch (validationResult.failureType) {
        case ResumeValidationFailureType.sessionExpired:
          updateStatus(SessionStatus.expired);
          break;
        case ResumeValidationFailureType.sessionCorrupted:
        case ResumeValidationFailureType.noSession:
          updateStatus(SessionStatus.invalid);
          break;
        default:
          updateStatus(SessionStatus.unknown);
      }
      return false;
    }

    updateStatus(SessionStatus.valid);
    return true;
  }

  /// Reset session state
  void resetState() {
    updateStatus(SessionStatus.unknown);
    _authLogger.addLogEntry(
      'Session state reset',
      importance: AuthLogImportance.important,
    );
  }

  /// Get session state summary
  Future<Map<String, dynamic>> getStateSummary() async {
    final hasPersistedData = await hasPersistedSession();
    final expiryTime = await getPersistedExpiryTime();
    final lastActiveTime = await getLastActiveTime();
    final isExpired = await isPersistedSessionExpired();

    return {
      'currentStatus': _currentStatus.toString(),
      'hasPersistedData': hasPersistedData,
      'expiryTime': expiryTime?.toIso8601String(),
      'lastActiveTime': lastActiveTime?.toIso8601String(),
      'isExpired': isExpired,
      'timeUntilExpiry': (await getTimeUntilExpiry())?.inMinutes,
    };
  }

  /// Dispose resources
  void dispose() {
    _statusController.close();
  }
}

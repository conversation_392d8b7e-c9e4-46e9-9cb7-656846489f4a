import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;

import 'sync_logger.dart';

/// <PERSON><PERSON> responsible for sync error management and recovery
class SyncErrorHandler {
  final SyncLogger _logger;
  final rp.Ref _ref;

  /// Constructor
  SyncErrorHandler(this._logger, this._ref);

  /// Handle sync errors and determine recovery strategy
  Future<SyncErrorResult> handleSyncError(
    Object error,
    StackTrace stackTrace,
    String operation,
  ) async {
    _logger.addLogEntry('Handling sync error for operation: $operation');
    _logger.addLogEntry('Error: $error');
    
    if (kDebugMode) {
      debugPrint('Sync error in $operation: $error');
      debugPrint('Stack trace: $stackTrace');
    }

    // Categorize the error
    final errorCategory = _categorizeError(error);
    
    // Determine recovery strategy
    final recoveryStrategy = _determineRecoveryStrategy(errorCategory, error);
    
    // Log the categorization and strategy
    _logger.addLogEntry('Error category: ${errorCategory.name}');
    _logger.addLogEntry('Recovery strategy: ${recoveryStrategy.name}');

    return SyncErrorResult(
      originalError: error,
      stackTrace: stackTrace,
      operation: operation,
      category: errorCategory,
      recoveryStrategy: recoveryStrategy,
      errorMessage: _formatErrorMessage(error, errorCategory),
      isRetryable: _isRetryable(errorCategory),
      suggestedDelay: _getSuggestedRetryDelay(errorCategory),
    );
  }

  /// Categorize the error type
  SyncErrorCategory _categorizeError(Object error) {
    final errorString = error.toString().toLowerCase();

    // Network-related errors
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket') ||
        errorString.contains('host lookup failed')) {
      return SyncErrorCategory.network;
    }

    // Authentication errors
    if (errorString.contains('unauthorized') ||
        errorString.contains('authentication') ||
        errorString.contains('invalid token') ||
        errorString.contains('expired token') ||
        errorString.contains('403') ||
        errorString.contains('401')) {
      return SyncErrorCategory.authentication;
    }

    // Database errors
    if (errorString.contains('database') ||
        errorString.contains('sql') ||
        errorString.contains('constraint') ||
        errorString.contains('foreign key') ||
        errorString.contains('unique constraint')) {
      return SyncErrorCategory.database;
    }

    // UUID conflict errors
    if (errorString.contains('uuid conflict') ||
        errorString.contains('duplicate key') ||
        errorString.contains('uuid')) {
      return SyncErrorCategory.uuidConflict;
    }

    // Data validation errors
    if (errorString.contains('validation') ||
        errorString.contains('invalid data') ||
        errorString.contains('format') ||
        errorString.contains('parse')) {
      return SyncErrorCategory.dataValidation;
    }

    // Rate limiting errors
    if (errorString.contains('rate limit') ||
        errorString.contains('too many requests') ||
        errorString.contains('429')) {
      return SyncErrorCategory.rateLimiting;
    }

    // Server errors
    if (errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504') ||
        errorString.contains('server error')) {
      return SyncErrorCategory.serverError;
    }

    // Default to unknown
    return SyncErrorCategory.unknown;
  }

  /// Determine the appropriate recovery strategy
  SyncRecoveryStrategy _determineRecoveryStrategy(
    SyncErrorCategory category,
    Object error,
  ) {
    switch (category) {
      case SyncErrorCategory.network:
        return SyncRecoveryStrategy.retryWithBackoff;
      
      case SyncErrorCategory.authentication:
        return SyncRecoveryStrategy.refreshTokenAndRetry;
      
      case SyncErrorCategory.database:
        return SyncRecoveryStrategy.skipAndContinue;
      
      case SyncErrorCategory.uuidConflict:
        return SyncRecoveryStrategy.repairAndRetry;
      
      case SyncErrorCategory.dataValidation:
        return SyncRecoveryStrategy.skipAndLog;
      
      case SyncErrorCategory.rateLimiting:
        return SyncRecoveryStrategy.retryWithLongDelay;
      
      case SyncErrorCategory.serverError:
        return SyncRecoveryStrategy.retryWithBackoff;
      
      case SyncErrorCategory.unknown:
        return SyncRecoveryStrategy.skipAndLog;
    }
  }

  /// Format error message for user display
  String _formatErrorMessage(Object error, SyncErrorCategory category) {
    switch (category) {
      case SyncErrorCategory.network:
        return 'Network connection issue. Please check your internet connection.';
      
      case SyncErrorCategory.authentication:
        return 'Authentication error. Please sign in again.';
      
      case SyncErrorCategory.database:
        return 'Database error occurred during sync.';
      
      case SyncErrorCategory.uuidConflict:
        return 'Data conflict detected. Attempting automatic resolution.';
      
      case SyncErrorCategory.dataValidation:
        return 'Invalid data format detected. Some records may be skipped.';
      
      case SyncErrorCategory.rateLimiting:
        return 'Too many requests. Sync will retry after a delay.';
      
      case SyncErrorCategory.serverError:
        return 'Server error occurred. Sync will retry automatically.';
      
      case SyncErrorCategory.unknown:
        return 'An unexpected error occurred during sync.';
    }
  }

  /// Check if error is retryable
  bool _isRetryable(SyncErrorCategory category) {
    switch (category) {
      case SyncErrorCategory.network:
      case SyncErrorCategory.authentication:
      case SyncErrorCategory.uuidConflict:
      case SyncErrorCategory.rateLimiting:
      case SyncErrorCategory.serverError:
        return true;
      
      case SyncErrorCategory.database:
      case SyncErrorCategory.dataValidation:
      case SyncErrorCategory.unknown:
        return false;
    }
  }

  /// Get suggested retry delay based on error category
  Duration _getSuggestedRetryDelay(SyncErrorCategory category) {
    switch (category) {
      case SyncErrorCategory.network:
        return const Duration(seconds: 5);
      
      case SyncErrorCategory.authentication:
        return const Duration(seconds: 2);
      
      case SyncErrorCategory.uuidConflict:
        return const Duration(seconds: 1);
      
      case SyncErrorCategory.rateLimiting:
        return const Duration(minutes: 1);
      
      case SyncErrorCategory.serverError:
        return const Duration(seconds: 10);
      
      case SyncErrorCategory.database:
      case SyncErrorCategory.dataValidation:
      case SyncErrorCategory.unknown:
        return Duration.zero;
    }
  }

  /// Update UI state with error information
  void updateUIWithError(String errorMessage, SyncErrorCategory category) {
    // Update sync state provider with error
    _ref.read(syncStateProvider.notifier).state = rp.AsyncValue.error(
      errorMessage,
      StackTrace.current,
    );

    _logger.addLogEntry('UI updated with error: $errorMessage');
  }
}

/// Categories of sync errors
enum SyncErrorCategory {
  network,
  authentication,
  database,
  uuidConflict,
  dataValidation,
  rateLimiting,
  serverError,
  unknown,
}

/// Recovery strategies for sync errors
enum SyncRecoveryStrategy {
  retryWithBackoff,
  refreshTokenAndRetry,
  skipAndContinue,
  repairAndRetry,
  skipAndLog,
  retryWithLongDelay,
}

/// Result of error handling
class SyncErrorResult {
  final Object originalError;
  final StackTrace stackTrace;
  final String operation;
  final SyncErrorCategory category;
  final SyncRecoveryStrategy recoveryStrategy;
  final String errorMessage;
  final bool isRetryable;
  final Duration suggestedDelay;

  SyncErrorResult({
    required this.originalError,
    required this.stackTrace,
    required this.operation,
    required this.category,
    required this.recoveryStrategy,
    required this.errorMessage,
    required this.isRetryable,
    required this.suggestedDelay,
  });
}

/// Provider for tracking sync state (imported from sync_operations.dart)
final syncStateProvider = rp.StateProvider<rp.AsyncValue<String>>((ref) {
  return const rp.AsyncValue.data('synced');
});

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;

import '../../config/supabase_config.dart';
import '../../datasources/app_database.dart';
import '../../providers/app_settings_provider.dart';

import 'connectivity_monitor.dart';

import 'sync_logger.dart';
import 'sync_operations.dart';
import 'sync_queue.dart';

/// Provider for the sync service
final syncServiceProvider = rp.Provider<SyncService>((ref) {
  final db = ref.watch(databaseProvider);
  return SyncService(db, ref);
});

/// Main SyncService class that acts as a facade for the other sync-related classes
class SyncService {
  late final SyncLogger _logger;
  late final SupabaseConfig _supabase;
  late final ConnectivityMonitor _connectivityMonitor;
  // No longer needed as it's created in SyncOperations
  // late final GenericEntitySyncHandler _entitySyncHandler;
  late final SyncOperations _syncOperations;

  /// Constructor
  SyncService(AppDatabase db, rp.Ref ref) {
    // Initialize components
    _supabase = SupabaseConfig();
    _logger = SyncLogger(ref);

    // Initialize connectivity monitor first
    _connectivityMonitor = ConnectivityMonitor(Connectivity(), _logger);

    // Then initialize sync operations
    _syncOperations = SyncOperations(
      db,
      _supabase,
      _logger,
      _connectivityMonitor,
      // Removed unused parameters
      ref.read(syncQueueProvider),
      ref,
    );

    // Connect the sync operations to the connectivity monitor
    _connectivityMonitor.setSyncOperations(_syncOperations);
  }

  /// Sync data with debounce
  Future<void> syncData(
    SyncOperation operation, {
    Duration debounceTime = const Duration(seconds: 5),
  }) async {
    await _syncOperations.syncData(operation, debounceTime: debounceTime);
  }

  /// Sync data immediately without debounce
  Future<void> syncNow(
    SyncOperation operation, {
    bool isManualSync = false,
  }) async {
    await _syncOperations.syncNow(operation, isManualSync: isManualSync);
  }

  /// Get all logs
  List<dynamic> getLogs() {
    return _logger.getLogs();
  }

  /// Clear logs
  void clearLogs() {
    _logger.clearLogs();
  }

  /// Refresh logs - adds a refresh entry without clearing logs
  void refreshLogs() {
    _logger.info('Logs refreshed by user', source: LogSource.system);
  }

  /// Dispose resources
  void dispose() {
    _connectivityMonitor.dispose();
    _syncOperations.dispose();
  }
}

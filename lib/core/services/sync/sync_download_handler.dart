import 'dart:async';

import 'package:flutter/foundation.dart';

import '../../config/supabase_config.dart';
import '../../datasources/app_database.dart';
import '../../models/sync_models.dart';
import 'generic_entity_sync_handler.dart';
import 'sync_logger.dart';

/// Handler responsible for download sync operations
class SyncDownloadHandler {
  final AppDatabase _db;
  final SupabaseConfig _supabase;
  final SyncLogger _logger;
  final GenericEntitySyncHandler _entitySyncHandler;

  /// Constructor
  SyncDownloadHandler(this._db, this._supabase, this._logger)
    : _entitySyncHandler = GenericEntitySyncHandler(_supabase, _logger);

  /// Handle download operation
  Future<bool> handleDownloadOperation() async {
    _logger.addLogEntry('Starting download operation');
    try {
      await downloadRecentChanges();
      return true;
    } catch (e) {
      final errorMessage = 'Download failed: $e';
      _logger.addLogEntry(errorMessage);
      return false;
    }
  }

  /// Download recent changes from Supabase
  Future<void> downloadRecentChanges() async {
    try {
      _logger.addLogEntry('Starting download of recent changes');

      // Get the last sync time
      final lastSyncTime = await _db.getLastSyncTime();

      if (lastSyncTime == null) {
        _logger.addLogEntry('No previous sync found, performing full download');
        // If this is the first sync, fetch all records
        await _fetchAllRecords();
        return;
      }

      // Log the raw lastSyncTime for debugging
      _logger.addLogEntry('Raw lastSyncTime: $lastSyncTime');

      // Add a small buffer (1 second) to avoid precision issues
      // Ensure the time is in UTC and handle potential format issues
      DateTime syncTimeCutoff;
      try {
        syncTimeCutoff = lastSyncTime.toUtc().subtract(
          const Duration(seconds: 1),
        );
      } catch (e) {
        _logger.addLogEntry('Error processing lastSyncTime: $e');
        // Fallback to current time minus 1 hour if there's an issue
        syncTimeCutoff = DateTime.now().toUtc().subtract(
          const Duration(hours: 1),
        );
      }

      _logger.addLogEntry(
        'Downloading records updated after: ${syncTimeCutoff.toIso8601String()}',
      );

      // Download records for each table
      await _downloadIncomeRecords(syncTimeCutoff);
      await _downloadOrderRecords(syncTimeCutoff);
      await _downloadPerformanceRecords(syncTimeCutoff);
      await _downloadSparePartRecords(syncTimeCutoff);
      await _downloadSparePartHistoryRecords(syncTimeCutoff);

      _logger.addLogEntry('Download operation completed successfully');
    } catch (e) {
      _logger.addLogEntry('Error downloading recent changes: $e');
      debugPrint('Error downloading recent changes: $e');
      rethrow;
    }
  }

  /// Fetch all records from Supabase (for first sync)
  Future<void> _fetchAllRecords() async {
    try {
      _logger.addLogEntry('Fetching all records from Supabase (first sync)');

      // Fetch all income records
      _logger.addLogEntry('Fetching income records');
      final incomeResponse = await _supabase.client.from('income').select();
      _logger.addLogEntry('Fetched ${incomeResponse.length} income records');

      _logger.addLogEntry('Fetching order records');
      final orderResponse = await _supabase.client.from('orders').select();
      _logger.addLogEntry('Fetched ${orderResponse.length} order records');

      _logger.addLogEntry('Fetching performance records');
      final performanceResponse = await _supabase.client
          .from('performance')
          .select();
      _logger.addLogEntry(
        'Fetched ${performanceResponse.length} performance records',
      );

      _logger.addLogEntry('Fetching spare part records');
      final sparePartResponse = await _supabase.client
          .from('spare_parts')
          .select();
      _logger.addLogEntry(
        'Fetched ${sparePartResponse.length} spare part records',
      );

      _logger.addLogEntry('Fetching spare part history records');
      final sparePartHistoryResponse = await _supabase.client
          .from('spare_parts_history')
          .select();
      _logger.addLogEntry(
        'Fetched ${sparePartHistoryResponse.length} spare part history records',
      );

      // Process and insert the records
      await _processIncomeRecords(incomeResponse);
      await _processOrderRecords(orderResponse);
      await _processPerformanceRecords(performanceResponse);
      await _processSparePartRecords(sparePartResponse);
      await _processSparePartHistoryRecords(sparePartHistoryResponse);

      _logger.addLogEntry('All records fetched and processed successfully');
    } catch (e) {
      _logger.addLogEntry('Error fetching all records: $e');
      debugPrint('Error fetching all records: $e');
      rethrow;
    }
  }

  /// Download income records updated after the given time
  Future<void> _downloadIncomeRecords(DateTime syncTimeCutoff) async {
    try {
      _logger.addLogEntry('Downloading income records');
      final response = await _supabase.client
          .from('income')
          .select()
          .gte('updated_at', syncTimeCutoff.toIso8601String());

      _logger.addLogEntry('Downloaded ${response.length} income records');
      await _processIncomeRecords(response);
    } catch (e) {
      _logger.addLogEntry('Error downloading income records: $e');
      rethrow;
    }
  }

  /// Download order records updated after the given time
  Future<void> _downloadOrderRecords(DateTime syncTimeCutoff) async {
    try {
      _logger.addLogEntry('Downloading order records');
      final response = await _supabase.client
          .from('orders')
          .select()
          .gte('updated_at', syncTimeCutoff.toIso8601String());

      _logger.addLogEntry('Downloaded ${response.length} order records');
      await _processOrderRecords(response);
    } catch (e) {
      _logger.addLogEntry('Error downloading order records: $e');
      rethrow;
    }
  }

  /// Download performance records updated after the given time
  Future<void> _downloadPerformanceRecords(DateTime syncTimeCutoff) async {
    try {
      _logger.addLogEntry('Downloading performance records');
      final response = await _supabase.client
          .from('performance')
          .select()
          .gte('updated_at', syncTimeCutoff.toIso8601String());

      _logger.addLogEntry('Downloaded ${response.length} performance records');
      await _processPerformanceRecords(response);
    } catch (e) {
      _logger.addLogEntry('Error downloading performance records: $e');
      rethrow;
    }
  }

  /// Download spare part records updated after the given time
  Future<void> _downloadSparePartRecords(DateTime syncTimeCutoff) async {
    try {
      _logger.addLogEntry('Downloading spare part records');
      final response = await _supabase.client
          .from('spare_parts')
          .select()
          .gte('updated_at', syncTimeCutoff.toIso8601String());

      _logger.addLogEntry('Downloaded ${response.length} spare part records');
      await _processSparePartRecords(response);
    } catch (e) {
      _logger.addLogEntry('Error downloading spare part records: $e');
      rethrow;
    }
  }

  /// Download spare part history records updated after the given time
  Future<void> _downloadSparePartHistoryRecords(DateTime syncTimeCutoff) async {
    try {
      _logger.addLogEntry('Downloading spare part history records');
      final response = await _supabase.client
          .from('spare_parts_history')
          .select()
          .gte('updated_at', syncTimeCutoff.toIso8601String());

      _logger.addLogEntry(
        'Downloaded ${response.length} spare part history records',
      );
      await _processSparePartHistoryRecords(response);
    } catch (e) {
      _logger.addLogEntry('Error downloading spare part history records: $e');
      rethrow;
    }
  }

  /// Process income records from Supabase response
  Future<void> _processIncomeRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} income records');
    for (final record in records) {
      try {
        await _entitySyncHandler
            .insertOrUpdateRecord<IncomeData, IncomeCompanion>(
              jsonData: record,
              entityName: 'income',
              uuid: record['uuid'],
              getExistingRecords: (uuid) => (_db.select(
                _db.income,
              )..where((t) => t.uuid.equals(uuid))).get(),
              fromJson: IncomeSyncModel.fromJson,
              insertRecord: (data) => _db.into(_db.income).insert(data),
              updateRecord: (uuid, data) => (_db.update(
                _db.income,
              )..where((t) => t.uuid.equals(uuid))).write(data),
              getUpdatedAt: (record) => record.updatedAt,
              getSyncStatus: (record) =>
                  record.syncStatus.toString().split('.').last,
            );
      } catch (e) {
        _logger.addLogEntry(
          'Error processing income record: ${record['uuid']} - $e',
        );
        // Continue with other records
      }
    }
  }

  /// Process order records from Supabase response
  Future<void> _processOrderRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} order records');
    for (final record in records) {
      try {
        await _entitySyncHandler.insertOrUpdateRecord<Order, OrdersCompanion>(
          jsonData: record,
          entityName: 'orders',
          uuid: record['uuid'],
          getExistingRecords: (uuid) =>
              (_db.select(_db.orders)..where((t) => t.uuid.equals(uuid))).get(),
          fromJson: OrdersSyncModel.fromJson,
          insertRecord: (data) => _db.into(_db.orders).insert(data),
          updateRecord: (uuid, data) => (_db.update(
            _db.orders,
          )..where((t) => t.uuid.equals(uuid))).write(data),
          getUpdatedAt: (record) => record.updatedAt,
          getSyncStatus: (record) =>
              record.syncStatus.toString().split('.').last,
        );
      } catch (e) {
        _logger.addLogEntry(
          'Error processing order record: ${record['uuid']} - $e',
        );
        // Continue with other records
      }
    }
  }

  /// Process performance records from Supabase response
  Future<void> _processPerformanceRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} performance records');
    for (final record in records) {
      try {
        await _entitySyncHandler
            .insertOrUpdateRecord<PerformanceData, PerformanceCompanion>(
              jsonData: record,
              entityName: 'performance',
              uuid: record['uuid'],
              getExistingRecords: (uuid) => (_db.select(
                _db.performance,
              )..where((t) => t.uuid.equals(uuid))).get(),
              fromJson: PerformanceSyncModel.fromJson,
              insertRecord: (data) => _db.into(_db.performance).insert(data),
              updateRecord: (uuid, data) => (_db.update(
                _db.performance,
              )..where((t) => t.uuid.equals(uuid))).write(data),
              getUpdatedAt: (record) => record.updatedAt,
              getSyncStatus: (record) =>
                  record.syncStatus.toString().split('.').last,
            );
      } catch (e) {
        _logger.addLogEntry(
          'Error processing performance record: ${record['uuid']} - $e',
        );
        // Continue with other records
      }
    }
  }

  /// Process spare part records from Supabase response
  Future<void> _processSparePartRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Processing ${records.length} spare part records');
    for (final record in records) {
      try {
        await _entitySyncHandler
            .insertOrUpdateRecord<SparePart, SparePartsCompanion>(
              jsonData: record,
              entityName: 'spare_parts',
              uuid: record['uuid'],
              getExistingRecords: (uuid) => (_db.select(
                _db.spareParts,
              )..where((t) => t.uuid.equals(uuid))).get(),
              fromJson: SparePartsSyncModel.fromJson,
              insertRecord: (data) => _db.into(_db.spareParts).insert(data),
              updateRecord: (uuid, data) => (_db.update(
                _db.spareParts,
              )..where((t) => t.uuid.equals(uuid))).write(data),
              getUpdatedAt: (record) => record.updatedAt,
              getSyncStatus: (record) =>
                  record.syncStatus.toString().split('.').last,
            );
      } catch (e) {
        _logger.addLogEntry(
          'Error processing spare part record: ${record['uuid']} - $e',
        );
        // Continue with other records
      }
    }
  }

  /// Process spare part history records from Supabase response
  Future<void> _processSparePartHistoryRecords(List<dynamic> records) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry(
      'Processing ${records.length} spare part history records',
    );
    for (final record in records) {
      try {
        await _entitySyncHandler.insertOrUpdateRecord<
          SparePartsHistoryData,
          SparePartsHistoryCompanion
        >(
          jsonData: record,
          entityName: 'spare_parts_history',
          uuid: record['uuid'],
          getExistingRecords: (uuid) => (_db.select(
            _db.sparePartsHistory,
          )..where((t) => t.uuid.equals(uuid))).get(),
          fromJson: SparePartsHistorySyncModel.fromJson,
          insertRecord: (data) => _db.into(_db.sparePartsHistory).insert(data),
          updateRecord: (uuid, data) => (_db.update(
            _db.sparePartsHistory,
          )..where((t) => t.uuid.equals(uuid))).write(data),
          getUpdatedAt: (record) => record.updatedAt,
          getSyncStatus: (record) =>
              record.syncStatus.toString().split('.').last,
        );
      } catch (e) {
        _logger.addLogEntry(
          'Error processing spare part history record: ${record['uuid']} - $e',
        );
        // Continue with other records
      }
    }
  }
}

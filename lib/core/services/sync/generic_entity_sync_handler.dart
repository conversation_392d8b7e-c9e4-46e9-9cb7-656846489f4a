import '../../config/supabase_config.dart';
import '../../models/sync_models.dart';
import 'conflict_resolver.dart';
import 'sync_logger.dart';

/// Generic handler for entity sync operations
class GenericEntitySyncHandler {
  final SupabaseConfig _supabase;
  final SyncLogger _logger;
  final ConflictResolver _conflictResolver;

  /// Constructor
  GenericEntitySyncHandler(this._supabase, this._logger)
    : _conflictResolver = ConflictResolver(_logger);

  /// Generic method to upload entity records
  Future<void> uploadEntityRecords<T, M extends SyncModel>({
    required String tableName,
    required List<T> records,
    required M Function(T) toSyncModel,
    required Future<void> Function(String) markAsSynced,
    required String Function(T) getUuid,
    required DateTime? Function(T) getDeletedAt,
  }) async {
    if (records.isEmpty) {
      return;
    }

    _logger.addLogEntry('Uploading ${records.length} $tableName records');

    // Count deleted records for logging
    final deletedCount = records
        .where((record) => getDeletedAt(record) != null)
        .length;
    if (deletedCount > 0) {
      _logger.addLogEntry(
        '$deletedCount of these $tableName records are marked as deleted',
      );
    }

    // Check for duplicate UUIDs in the records to upload
    final uuids = records.map((record) => getUuid(record)).toList();
    final uniqueUuids = uuids.toSet();
    if (uuids.length != uniqueUuids.length) {
      _logger.addLogEntry(
        'WARNING: Found duplicate UUIDs in local $tableName records to upload',
      );
      final duplicates = <String>[];
      for (final uuid in uniqueUuids) {
        if (uuids.where((u) => u == uuid).length > 1) {
          duplicates.add(uuid);
        }
      }
      _logger.addLogEntry('Duplicate UUIDs: ${duplicates.join(', ')}');
      throw Exception(
        'Cannot upload $tableName records with duplicate UUIDs: ${duplicates.join(', ')}. Please run database repair.',
      );
    }

    try {
      // Convert records to JSON
      final jsonData = records
          .map((record) => toSyncModel(record).toJson())
          .toList();

      // Try individual upserts to handle conflicts more gracefully
      await _uploadRecordsIndividually(
        tableName,
        jsonData,
        records,
        markAsSynced,
        getUuid,
      );

      _logger.addLogEntry(
        'Successfully uploaded ${records.length} $tableName records',
      );
    } catch (e) {
      // Enhanced error handling for UUID conflicts
      if (e.toString().contains(
            'duplicate key value violates unique constraint',
          ) &&
          e.toString().contains('uuid')) {
        _logger.addLogEntry(
          'UUID conflict detected during upload. This may indicate database corruption.',
        );
        _logger.addLogEntry('UUIDs being uploaded: ${uuids.join(', ')}');
        throw Exception(
          'UUID conflict in $tableName: $e. Database repair may be required.',
        );
      }
      rethrow;
    }
  }

  /// Upload records individually to handle UUID conflicts more gracefully
  Future<void> _uploadRecordsIndividually<T>(
    String tableName,
    List<Map<String, dynamic>> jsonData,
    List<T> records,
    Future<void> Function(String) markAsSynced,
    String Function(T) getUuid,
  ) async {
    for (int i = 0; i < records.length; i++) {
      final record = records[i];
      final recordJson = jsonData[i];
      final uuid = getUuid(record);

      try {
        _logger.addLogEntry('Uploading individual record with UUID: $uuid');

        // Try upsert first
        await _supabase.client.from(tableName).upsert([
          recordJson,
        ], onConflict: 'uuid');

        // Mark as synced if successful
        await markAsSynced(uuid);
        _logger.addLogEntry('Successfully uploaded record with UUID: $uuid');
      } catch (e) {
        if (e.toString().contains(
              'duplicate key value violates unique constraint',
            ) &&
            e.toString().contains('uuid')) {
          _logger.addLogEntry(
            'UUID conflict for $uuid, attempting manual resolution...',
          );

          try {
            // Check if record exists in Supabase
            final existingRecords = await _supabase.client
                .from(tableName)
                .select()
                .eq('uuid', uuid)
                .limit(1);

            if (existingRecords.isNotEmpty) {
              _logger.addLogEntry(
                'Record with UUID $uuid exists in Supabase, updating...',
              );

              // Update the existing record
              await _supabase.client
                  .from(tableName)
                  .update(recordJson)
                  .eq('uuid', uuid);

              await markAsSynced(uuid);
              _logger.addLogEntry(
                'Successfully updated existing record with UUID: $uuid',
              );
            } else {
              _logger.addLogEntry(
                'Record with UUID $uuid not found in Supabase, inserting...',
              );

              // Insert as new record
              await _supabase.client.from(tableName).insert([recordJson]);

              await markAsSynced(uuid);
              _logger.addLogEntry(
                'Successfully inserted new record with UUID: $uuid',
              );
            }
          } catch (manualError) {
            _logger.addLogEntry(
              'Manual resolution failed for UUID $uuid: $manualError',
            );
            throw Exception(
              'Failed to resolve UUID conflict for $uuid: $manualError',
            );
          }
        } else {
          _logger.addLogEntry('Upload failed for UUID $uuid: $e');
          rethrow;
        }
      }
    }
  }

  /// Generic method to insert or update a record
  Future<void> insertOrUpdateRecord<T, C>({
    required Map<String, dynamic> jsonData,
    required String entityName,
    required String uuid,
    required Future<List<T>> Function(String) getExistingRecords,
    required C Function(Map<String, dynamic>) fromJson,
    required Future<void> Function(C) insertRecord,
    required Future<void> Function(String, C) updateRecord,
    required DateTime Function(T) getUpdatedAt,
    required String Function(T) getSyncStatus,
  }) async {
    try {
      _logger.addLogEntry('Processing $entityName record: $uuid');

      try {
        // Convert JSON to entity companion
        final entityData = fromJson(jsonData);

        // Check if record with this UUID already exists
        final existingRecords = await getExistingRecords(uuid);

        if (existingRecords.isEmpty) {
          // Insert new record
          await insertRecord(entityData);
          _logger.addLogEntry('Inserted new $entityName record: $uuid');
        } else {
          // Update existing record (if server record is newer)
          final existingRecord = existingRecords.first;
          // Parse server updated timestamp for logging
          safeDateTimeParse(jsonData['updated_at'] as String);

          // Convert existing record to a map for conflict resolution
          final localData = _convertToMap(existingRecord);

          // Use the conflict resolver to determine which data to use
          final resolvedData = _conflictResolver.resolveConflict(
            localData: localData,
            remoteData: jsonData,
            entityName: entityName,
            uuid: uuid,
          );

          // If the resolved data is different from the server data, we need to update
          final needsUpdate = _isDifferent(resolvedData, jsonData);

          // If the resolved data is different from the local data, we need to update
          final localNeedsUpdate = _isDifferent(resolvedData, localData);

          if (needsUpdate || localNeedsUpdate) {
            // Convert the resolved data back to an entity
            final resolvedEntity = fromJson(resolvedData);

            // Update the record
            await updateRecord(uuid, resolvedEntity);
            _logger.addLogEntry(
              'Updated $entityName record: $uuid (conflict resolved)',
            );
          } else {
            _logger.addLogEntry(
              'No update needed for $entityName record: $uuid (data already in sync)',
            );
          }
        }
      } catch (e) {
        // Log the error but don't rethrow, so we can continue processing other records
        _logger.addLogEntry('Error processing $entityName record: $uuid - $e');
      }
    } catch (e) {
      _logger.addLogEntry('Error inserting/updating $entityName record: $e');
      // Don't rethrow, so we can continue processing other records
    }
  }

  /// Helper function to safely parse DateTime values from various formats
  DateTime safeDateTimeParse(dynamic dateValue) {
    // If the value is already a DateTime, return it
    if (dateValue is DateTime) {
      return dateValue.toUtc();
    }

    // If the value is null, return current time
    if (dateValue == null) {
      return DateTime.now().toUtc();
    }

    // Convert to string and trim any whitespace
    final dateStr = dateValue.toString().trim();

    // If the string is empty, return current time
    if (dateStr.isEmpty) {
      return DateTime.now().toUtc();
    }

    try {
      // Try standard ISO 8601 parsing first
      return DateTime.parse(dateStr).toUtc();
    } catch (e) {
      try {
        // Try parsing common date formats
        if (RegExp(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}').hasMatch(dateStr)) {
          // Handle format: YYYY-MM-DD HH:MM:SS
          final dateTimeStr = dateStr.replaceFirst(' ', 'T');
          return DateTime.parse('${dateTimeStr}Z').toUtc();
        } else if (dateStr.endsWith('Z') && dateStr.length > 1) {
          // Handle Unix timestamp with 'Z' suffix (seconds since epoch)
          final timestamp = int.tryParse(
            dateStr.substring(0, dateStr.length - 1),
          );
          if (timestamp != null) {
            return DateTime.fromMillisecondsSinceEpoch(
              timestamp * 1000,
              isUtc: true,
            );
          }
        } else {
          // Try parsing as Unix timestamp (seconds since epoch)
          final timestamp = int.tryParse(dateStr);
          if (timestamp != null) {
            return DateTime.fromMillisecondsSinceEpoch(
              timestamp * 1000,
              isUtc: true,
            );
          }
        }

        // If we get here, try one more time with a more lenient parser
        final parts = dateStr.split(RegExp(r'[^0-9]'));
        if (parts.length >= 6) {
          final year = int.tryParse(parts[0]) ?? DateTime.now().year;
          final month = int.tryParse(parts[1]) ?? 1;
          final day = int.tryParse(parts[2]) ?? 1;
          final hour = int.tryParse(parts[3]) ?? 0;
          final minute = int.tryParse(parts[4]) ?? 0;
          final second = int.tryParse(parts[5]) ?? 0;

          return DateTime.utc(year, month, day, hour, minute, second);
        }
      } catch (e2) {
        _logger.addLogEntry('Failed to parse date: $dateValue. Error: $e2');
      }

      // If all parsing fails, log the error and return current time
      _logger.addLogEntry('Could not parse date: $dateValue');
      return DateTime.now().toUtc();
    }
  }

  /// Convert an entity to a Map for conflict resolution
  Map<String, dynamic> _convertToMap<T>(T entity) {
    // This is a generic method that tries to convert an entity to a Map
    // It uses reflection to get the properties of the entity

    final result = <String, dynamic>{};

    try {
      // Try to access common properties that most entities have
      // This is not a complete solution, but it covers the basic properties

      // Use reflection to get the properties
      final mirror = entity.toString();

      // Parse the string representation to extract key-value pairs
      // Format is typically "ClassName(field1: value1, field2: value2, ...)"
      final content = mirror.substring(
        mirror.indexOf('(') + 1,
        mirror.lastIndexOf(')'),
      );

      // Split by commas, but handle commas in values
      final pairs = content.split(RegExp(r',\s*(?![^()]*\))'));

      for (final pair in pairs) {
        final parts = pair.split(':');
        if (parts.length >= 2) {
          final key = parts[0].trim();
          final value = parts.sublist(1).join(':').trim();

          // Convert string value to appropriate type
          result[key] = _parseValue(value);
        }
      }

      // Add uuid if it exists
      if (entity is SyncModel) {
        result['uuid'] = entity.uuid;
        result['updated_at'] = entity.updatedAt.toIso8601String();
        result['deleted_at'] = entity.deletedAt?.toIso8601String();
        result['sync_status'] = entity.syncStatus.toString();
      }
    } catch (e) {
      // If conversion fails, return an empty map
      // This will be handled by the conflict resolver
    }

    return result;
  }

  /// Parse a string value to an appropriate type
  dynamic _parseValue(String value) {
    // Remove quotes if present
    if (value.startsWith("'") && value.endsWith("'")) {
      return value.substring(1, value.length - 1);
    }

    // Try to parse as number
    if (RegExp(r'^\d+$').hasMatch(value)) {
      return int.tryParse(value) ?? value;
    }

    if (RegExp(r'^\d+\.\d+$').hasMatch(value)) {
      return double.tryParse(value) ?? value;
    }

    // Try to parse as boolean
    if (value == 'true') return true;
    if (value == 'false') return false;

    // Try to parse as DateTime
    if (RegExp(r'^\d{4}-\d{2}-\d{2}').hasMatch(value)) {
      try {
        return DateTime.parse(value);
      } catch (_) {
        // Return as string if parsing fails
        return value;
      }
    }

    // Return as string for other cases
    return value;
  }

  /// Check if two maps are different
  bool _isDifferent(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    // Fields to ignore in comparison
    final fieldsToIgnore = ['sync_status', 'device_id'];

    // Check if map1 has fields that map2 doesn't, or if values are different
    for (final key in map1.keys) {
      if (fieldsToIgnore.contains(key)) continue;

      if (!map2.containsKey(key) || map1[key] != map2[key]) {
        return true;
      }
    }

    // Check if map2 has fields that map1 doesn't
    for (final key in map2.keys) {
      if (fieldsToIgnore.contains(key)) continue;

      if (!map1.containsKey(key)) {
        return true;
      }
    }

    return false;
  }
}

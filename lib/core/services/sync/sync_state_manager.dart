import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;

import '../../datasources/app_database.dart';
import 'sync_logger.dart';

/// Manager responsible for sync state tracking and coordination
class SyncStateManager {
  final AppDatabase _db;
  final SyncLogger _logger;
  final rp.Ref _ref;

  /// Debounce timer for sync operations
  Timer? _syncDebounceTimer;

  /// Flag to track if sync is in progress
  bool _isSyncing = false;

  /// Flag to track if database repair is in progress (to prevent infinite loops)
  bool _isRepairInProgress = false;

  /// Threshold for number of pending changes to trigger immediate sync
  int _pendingChangesThreshold = 20;

  /// Constructor
  SyncStateManager(this._db, this._logger, this._ref);

  /// Get current sync status
  bool get isSyncing => _isSyncing;

  /// Get repair status
  bool get isRepairInProgress => _isRepairInProgress;

  /// Get pending changes threshold
  int get pendingChangesThreshold => _pendingChangesThreshold;

  /// Set pending changes threshold
  set pendingChangesThreshold(int threshold) {
    _pendingChangesThreshold = threshold;
    _logger.addLogEntry('Pending changes threshold updated to: $threshold');
  }

  /// Start sync operation
  void startSync(String operation) {
    _isSyncing = true;
    _logger.addLogEntry('Sync started: $operation');
    
    // Update UI state to show syncing
    _ref.read(syncStateProvider.notifier).state = 
        rp.AsyncValue.data('syncing');
  }

  /// Complete sync operation
  Future<void> completeSync(bool successful, String? errorMessage) async {
    _isSyncing = false;
    
    if (successful) {
      _logger.addLogEntry('Sync completed successfully');
      await _updateLastSyncTimeAndState(true, null);
    } else {
      _logger.addLogEntry('Sync completed with errors: $errorMessage');
      await _updateLastSyncTimeAndState(false, errorMessage);
    }
  }

  /// Start database repair
  void startRepair() {
    _isRepairInProgress = true;
    _logger.addLogEntry('Database repair started');
  }

  /// Complete database repair
  void completeRepair() {
    _isRepairInProgress = false;
    _logger.addLogEntry('Database repair completed');
  }

  /// Initialize sync data (last sync time and next scheduled sync)
  Future<void> initializeSyncData() async {
    await _initializeLastSyncTime();
    await _initializeNextScheduledSync();
  }

  /// Initialize the last sync time from database
  Future<void> _initializeLastSyncTime() async {
    try {
      // Get the last sync time from database
      final lastSyncTime = await _db.getLastSyncTime();

      // Update the provider with the retrieved value
      if (lastSyncTime != null) {
        _ref.read(lastSyncTimeProvider.notifier).state = lastSyncTime;
        _logger.addLogEntry(
          'Initialized last sync time: ${lastSyncTime.toIso8601String()}',
        );
      } else {
        _logger.addLogEntry('No previous sync time found in database');
      }
    } catch (e) {
      _logger.addLogEntry('Error initializing last sync time: $e');
    }
  }

  /// Initialize the next scheduled sync time
  Future<void> _initializeNextScheduledSync() async {
    try {
      // Get the next scheduled sync time from database or calculate it
      final nextScheduledSync = await _calculateNextScheduledSync();

      // Update the provider with the calculated value
      if (nextScheduledSync != null) {
        _ref.read(nextScheduledSyncProvider.notifier).state = nextScheduledSync;
        _logger.addLogEntry(
          'Initialized next scheduled sync: ${nextScheduledSync.toIso8601String()}',
        );
      } else {
        _logger.addLogEntry('No next scheduled sync calculated');
      }
    } catch (e) {
      _logger.addLogEntry('Error initializing next scheduled sync: $e');
    }
  }

  /// Calculate the next scheduled sync time
  Future<DateTime?> _calculateNextScheduledSync() async {
    try {
      final lastSyncTime = await _db.getLastSyncTime();
      if (lastSyncTime == null) {
        // If no previous sync, schedule for immediate sync
        return DateTime.now();
      }

      // Calculate next sync based on sync interval (e.g., every 30 minutes)
      const syncInterval = Duration(minutes: 30);
      return lastSyncTime.add(syncInterval);
    } catch (e) {
      _logger.addLogEntry('Error calculating next scheduled sync: $e');
      return null;
    }
  }

  /// Update last sync time and UI state
  Future<void> _updateLastSyncTimeAndState(
    bool syncSuccessful,
    String? errorMessage,
  ) async {
    try {
      if (syncSuccessful) {
        // Update last sync time in database
        final now = DateTime.now().toUtc();
        await _db.updateLastSyncTime(now);

        // Update the provider
        _ref.read(lastSyncTimeProvider.notifier).state = now;

        // Update UI state to show success
        _ref.read(syncStateProvider.notifier).state = 
            const rp.AsyncValue.data('synced');

        _logger.addLogEntry('Last sync time updated: ${now.toIso8601String()}');

        // Calculate and update next scheduled sync
        final nextSync = await _calculateNextScheduledSync();
        if (nextSync != null) {
          _ref.read(nextScheduledSyncProvider.notifier).state = nextSync;
        }
      } else {
        // Update UI state to show error
        _ref.read(syncStateProvider.notifier).state = rp.AsyncValue.error(
          errorMessage ?? 'Sync failed',
          StackTrace.current,
        );
      }
    } catch (e) {
      _logger.addLogEntry('Error updating last sync time and state: $e');
    }
  }

  /// Check if immediate sync is needed based on pending changes
  Future<bool> shouldTriggerImmediateSync() async {
    try {
      final pendingChangesCount = await _getPendingChangesCount();
      final shouldTrigger = pendingChangesCount >= _pendingChangesThreshold;
      
      if (shouldTrigger) {
        _logger.addLogEntry(
          'Immediate sync triggered: $pendingChangesCount pending changes (threshold: $_pendingChangesThreshold)',
        );
      }
      
      return shouldTrigger;
    } catch (e) {
      _logger.addLogEntry('Error checking pending changes count: $e');
      return false;
    }
  }

  /// Get count of pending changes across all tables
  Future<int> _getPendingChangesCount() async {
    try {
      final incomeCount = (await _db.getUnsyncedIncome()).length;
      final orderCount = (await _db.getUnsyncedOrders()).length;
      final performanceCount = (await _db.getUnsyncedPerformance()).length;
      final sparePartCount = (await _db.getUnsyncedSpareParts()).length;
      final sparePartHistoryCount = (await _db.getUnsyncedSparePartsHistory()).length;

      return incomeCount + orderCount + performanceCount + sparePartCount + sparePartHistoryCount;
    } catch (e) {
      _logger.addLogEntry('Error getting pending changes count: $e');
      return 0;
    }
  }

  /// Schedule debounced sync
  void scheduleDebounceSync(Function() syncFunction, {Duration delay = const Duration(seconds: 5)}) {
    // Cancel existing timer
    _syncDebounceTimer?.cancel();

    // Schedule new sync
    _syncDebounceTimer = Timer(delay, () {
      if (!_isSyncing) {
        syncFunction();
      }
    });

    _logger.addLogEntry('Debounced sync scheduled with ${delay.inSeconds}s delay');
  }

  /// Cancel debounced sync
  void cancelDebounceSync() {
    _syncDebounceTimer?.cancel();
    _syncDebounceTimer = null;
    _logger.addLogEntry('Debounced sync cancelled');
  }

  /// Dispose resources
  void dispose() {
    _syncDebounceTimer?.cancel();
    _logger.addLogEntry('SyncStateManager disposed');
  }
}

/// Provider for tracking sync state
final syncStateProvider = rp.StateProvider<rp.AsyncValue<String>>((ref) {
  return const rp.AsyncValue.data('synced');
});

/// Provider for the last sync time
final lastSyncTimeProvider = rp.StateProvider<DateTime?>((ref) => null);

/// Provider for the next scheduled sync time
final nextScheduledSyncProvider = rp.StateProvider<DateTime?>((ref) => null);

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;
import 'package:intl/intl.dart';

/// Enum for log levels
enum LogLevel {
  debug(0, 'DEBUG'),
  info(1, 'INFO'),
  warning(2, 'WARNING'),
  error(3, 'ERROR');

  final int value;
  final String label;

  const LogLevel(this.value, this.label);

  bool operator >=(LogLevel other) => value >= other.value;
}

/// Enum for log sources to categorize log origins
enum LogSource {
  /// Logs from sync operations (upload, download, conflicts, etc.)
  sync('SYNC'),

  /// Logs from authentication operations (login, logout, token refresh, etc.)
  auth('AUTH'),

  /// Logs from system operations (app lifecycle, connectivity, etc.)
  system('SYSTEM'),

  /// Logs from database operations
  database('DATABASE'),

  /// Logs from backup/restore operations
  backup('BACKUP'),

  /// Unknown or legacy log source
  unknown('UNKNOWN');

  final String label;

  const LogSource(this.label);
}

/// Provider for sync logs
final syncLogsProvider = rp.StateProvider<List<LogEntry>>((ref) => []);

/// Provider for minimum log level to display
final logLevelFilterProvider = rp.StateProvider<LogLevel>(
  (ref) => LogLevel.info,
);

/// Provider for pure sync logs (only sync source)
final pureSyncLogsProvider = rp.Provider<List<LogEntry>>((ref) {
  final allLogs = ref.watch(syncLogsProvider);
  return allLogs.where((log) => log.isFromSource(LogSource.sync)).toList();
});

/// Provider for pure auth logs from sync logs (auth logs that ended up in sync logs)
final authLogsFromSyncProvider = rp.Provider<List<LogEntry>>((ref) {
  final allLogs = ref.watch(syncLogsProvider);
  return allLogs.where((log) => log.isFromSource(LogSource.auth)).toList();
});

/// Provider for system logs from sync logs
final systemLogsFromSyncProvider = rp.Provider<List<LogEntry>>((ref) {
  final allLogs = ref.watch(syncLogsProvider);
  return allLogs.where((log) => log.isFromSource(LogSource.system)).toList();
});

/// Provider for filtered pure sync logs based on level
final filteredPureSyncLogsProvider = rp.Provider<List<LogEntry>>((ref) {
  final pureLogs = ref.watch(pureSyncLogsProvider);
  final minLevel = ref.watch(logLevelFilterProvider);
  return pureLogs.where((log) => log.level >= minLevel).toList();
});

/// Class representing a log entry
class LogEntry {
  final DateTime timestamp;
  final String message;
  final LogLevel level;
  final LogSource source;

  LogEntry({
    required this.timestamp,
    required this.message,
    required this.level,
    required this.source,
  });

  String get formattedTimestamp =>
      DateFormat('yyyy-MM-dd HH:mm:ss').format(timestamp.toLocal());

  String get formattedEntry =>
      '[$formattedTimestamp] [${source.label}] [${level.label}] $message';

  @override
  String toString() => formattedEntry;

  /// Check if this log entry matches a specific source
  bool isFromSource(LogSource targetSource) => source == targetSource;

  /// Check if this log entry is from any of the specified sources
  bool isFromAnySources(List<LogSource> targetSources) =>
      targetSources.contains(source);
}

/// Class responsible for managing sync logs
class SyncLogger {
  final rp.Ref _ref;

  /// In-memory log storage
  final List<LogEntry> _syncLogs = [];

  /// Maximum number of log entries to keep
  final int _maxLogEntries;

  /// Constructor
  SyncLogger(this._ref, {int maxLogEntries = 200})
    : _maxLogEntries = maxLogEntries;

  /// Add a log entry with specified level and source
  void log(
    String message, {
    LogLevel level = LogLevel.info,
    LogSource source = LogSource.sync,
  }) {
    // Skip verbose debug logs that don't provide meaningful information
    if (level == LogLevel.debug && _shouldSkipVerboseLog(message)) {
      return;
    }

    final entry = LogEntry(
      timestamp: DateTime.now(),
      message: message,
      level: level,
      source: source,
    );

    // Add to in-memory log
    _syncLogs.add(entry);

    // Trim log if it gets too large
    if (_syncLogs.length > _maxLogEntries) {
      _syncLogs.removeRange(0, _syncLogs.length - _maxLogEntries);
    }

    // Update the provider
    _ref.read(syncLogsProvider.notifier).state = List.from(_syncLogs);

    // Print to console for debugging
    debugPrint('SYNC: ${entry.formattedEntry}');
  }

  /// Add a debug log entry
  void debug(String message, {LogSource source = LogSource.sync}) {
    log(message, level: LogLevel.debug, source: source);
  }

  /// Add an info log entry
  void info(String message, {LogSource source = LogSource.sync}) {
    log(message, level: LogLevel.info, source: source);
  }

  /// Add a warning log entry
  void warning(String message, {LogSource source = LogSource.sync}) {
    log(message, level: LogLevel.warning, source: source);
  }

  /// Add an error log entry
  void error(String message, {LogSource source = LogSource.sync}) {
    log(message, level: LogLevel.error, source: source);
  }

  /// Add a log entry (legacy method for backward compatibility)
  void addLogEntry(String message, {LogSource source = LogSource.unknown}) {
    // Determine log level based on message content
    LogLevel level = LogLevel.info;
    if (message.contains('Error') ||
        message.contains('failed') ||
        message.contains('error')) {
      level = LogLevel.error;
    } else if (message.contains('Warning') ||
        message.contains('warning') ||
        message.contains('conflict')) {
      level = LogLevel.warning;
    } else if (message.contains('Debug') || message.contains('debug')) {
      level = LogLevel.debug;
    }

    // Try to determine source from message content if not specified
    LogSource determinedSource = source;
    if (source == LogSource.unknown) {
      if (message.contains('AUTH:') || message.toLowerCase().contains('auth')) {
        determinedSource = LogSource.auth;
      } else if (message.toLowerCase().contains('sync')) {
        determinedSource = LogSource.sync;
      } else if (message.toLowerCase().contains('database') ||
          message.toLowerCase().contains('db')) {
        determinedSource = LogSource.database;
      } else if (message.toLowerCase().contains('backup') ||
          message.toLowerCase().contains('restore')) {
        determinedSource = LogSource.backup;
      } else {
        determinedSource = LogSource.system;
      }
    }

    log(message, level: level, source: determinedSource);
  }

  /// Get all logs
  List<LogEntry> getLogs() {
    return List.unmodifiable(_syncLogs);
  }

  /// Get filtered logs based on minimum level
  List<LogEntry> getFilteredLogs({LogLevel minLevel = LogLevel.info}) {
    return _syncLogs.where((entry) => entry.level >= minLevel).toList();
  }

  /// Get logs filtered by source
  List<LogEntry> getLogsBySource(LogSource source) {
    return _syncLogs.where((entry) => entry.isFromSource(source)).toList();
  }

  /// Get logs filtered by multiple sources
  List<LogEntry> getLogsBySources(List<LogSource> sources) {
    return _syncLogs.where((entry) => entry.isFromAnySources(sources)).toList();
  }

  /// Get filtered logs based on both level and source
  List<LogEntry> getFilteredLogsBySource({
    LogLevel minLevel = LogLevel.info,
    required LogSource source,
  }) {
    return _syncLogs
        .where((entry) => entry.level >= minLevel && entry.isFromSource(source))
        .toList();
  }

  /// Get filtered logs based on level and multiple sources
  List<LogEntry> getFilteredLogsBySources({
    LogLevel minLevel = LogLevel.info,
    required List<LogSource> sources,
  }) {
    return _syncLogs
        .where(
          (entry) => entry.level >= minLevel && entry.isFromAnySources(sources),
        )
        .toList();
  }

  /// Clear logs
  void clearLogs() {
    _syncLogs.clear();
    _ref.read(syncLogsProvider.notifier).state = [];
  }

  /// Check if a verbose log should be skipped
  bool _shouldSkipVerboseLog(String message) {
    // List of message patterns to skip for verbose debug logs
    final skipPatterns = [
      'Logs refreshed by user',
      'Checking connectivity',
      'Connection status:',
      'Routine sync check',
      'Background sync validation',
      'Periodic sync status',
      'Sync queue status:',
      'Debounce timer',
      'Sync operation queued',
      'Processing sync queue',
    ];

    // Skip if the message contains any of the patterns
    return skipPatterns.any((pattern) => message.contains(pattern));
  }
}

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;

import '../../config/supabase_config.dart';
import '../../datasources/app_database.dart';

import '../../providers/sync_preference_provider.dart';
import '../auth_service.dart';
import '../sync_schedule_service.dart';
import 'connectivity_monitor.dart';

import 'sync_download_handler.dart';

import 'sync_logger.dart';
import 'sync_queue.dart';
import 'sync_state_manager.dart';
import 'sync_upload_handler.dart';

/// Enum for sync operation types
enum SyncOperation { upload, download, full }

/// Provider for tracking sync state
final syncStateProvider = rp.StateProvider<rp.AsyncValue<String>>((ref) {
  return const rp.AsyncValue.data('synced');
});

/// Provider for the last sync time
final lastSyncTimeProvider = rp.StateProvider<DateTime?>((ref) => null);

/// Provider for the next scheduled sync time
final nextScheduledSyncProvider = rp.StateProvider<DateTime?>((ref) => null);

/// Class responsible for coordinating sync operations
class SyncOperations {
  final AppDatabase _db;
  final SupabaseConfig _supabase;
  final ConnectivityMonitor _connectivityMonitor;
  final SyncLogger _logger;
  final SyncQueue _syncQueue;
  final rp.Ref _ref;

  // Split handlers
  late final SyncUploadHandler _uploadHandler;
  late final SyncDownloadHandler _downloadHandler;
  late final SyncStateManager _stateManager;

  /// Constructor
  SyncOperations(
    this._db,
    this._supabase,
    this._logger,
    this._connectivityMonitor,
    // Removed unused parameters:
    // bool syncEnabled,
    // AuthService authService,
    this._syncQueue,
    this._ref,
  ) {
    _initializeHandlers();
    _initializeSyncData();
    _initializeSyncQueue();
  }

  /// Initialize the split handler components
  void _initializeHandlers() {
    _uploadHandler = SyncUploadHandler(_db, _supabase, _logger);
    _downloadHandler = SyncDownloadHandler(_db, _supabase, _logger);
    _stateManager = SyncStateManager(_db, _logger, _ref);
  }

  /// Initialize the sync queue
  Future<void> _initializeSyncQueue() async {
    await _syncQueue.initialize();
    _logger.info(
      'Sync queue initialized with ${_syncQueue.pendingOperationsCount} pending operations',
    );
  }

  /// Initialize sync data (last sync time and next scheduled sync)
  Future<void> _initializeSyncData() async {
    await _stateManager.initializeSyncData();
  }

  /// Get the total number of pending changes
  Future<int> _getTotalPendingChangesCount() async {
    try {
      // Get all unsynchronized records from each table
      final incomeRecords = await _db.getUnsyncedIncome();
      final orderRecords = await _db.getUnsyncedOrders();
      final performanceRecords = await _db.getUnsyncedPerformance();
      final sparePartRecords = await _db.getUnsyncedSpareParts();
      final sparePartHistoryRecords = await _db.getUnsyncedSparePartsHistory();

      // Calculate total
      final totalCount =
          incomeRecords.length +
          orderRecords.length +
          performanceRecords.length +
          sparePartRecords.length +
          sparePartHistoryRecords.length;

      return totalCount;
    } catch (e) {
      _logger.addLogEntry('Error getting pending changes count: $e');
      return 0;
    }
  }

  /// Check if a sync should be performed based on schedule or pending changes
  Future<bool> _shouldPerformSync(SyncOperation operation) async {
    // For manual sync, always return true
    if (operation == SyncOperation.upload) {
      // For upload operations, check if there are any pending changes
      final pendingChanges = await _getTotalPendingChangesCount();
      return pendingChanges > 0;
    }

    // For full sync, check schedule and pending changes
    if (operation == SyncOperation.full) {
      // Check if there are enough pending changes to trigger immediate sync
      final shouldTrigger = await _stateManager.shouldTriggerImmediateSync();
      if (shouldTrigger) {
        _logger.addLogEntry(
          'Significant number of pending changes detected, triggering immediate sync',
        );
        return true;
      }

      // Check if it's time for a scheduled sync
      final isScheduledSyncDue = await SyncScheduleService.isScheduledSyncDue();
      if (isScheduledSyncDue) {
        _logger.addLogEntry('Scheduled sync is due, triggering sync');
        return true;
      }

      // Not time for sync yet
      final nextSync = await SyncScheduleService.getNextScheduledSync();
      if (nextSync != null) {
        _logger.addLogEntry(
          'Scheduled sync not due yet, next sync at ${nextSync.toIso8601String()}',
        );
      }
      return false;
    }

    // For download operations, always return true
    return true;
  }

  /// Main sync method with debounce
  Future<void> syncData(
    SyncOperation operation, {
    Duration debounceTime = const Duration(seconds: 5),
  }) async {
    // Check if auto sync is enabled
    final syncEnabled = _ref.read(syncEnabledProvider);
    if (!syncEnabled) {
      _logger.addLogEntry(
        'Auto sync is disabled, skipping automatic sync operation: $operation',
      );
      return;
    }

    // Use state manager for debounced sync
    _stateManager.scheduleDebounceSync(() async {
      try {
        // Check if sync should be performed based on schedule or pending changes
        final shouldSync = await _shouldPerformSync(operation);
        if (!shouldSync) {
          _logger.addLogEntry('Skipping scheduled sync operation: $operation');
          return;
        }

        await _executeSyncOperation(operation);
      } catch (e) {
        debugPrint('Sync error: $e');
      }
    }, delay: debounceTime);
  }

  /// Execute sync operation without debouncing
  Future<void> syncNow(
    SyncOperation operation, {
    bool isManualSync = false,
  }) async {
    try {
      // If it's not explicitly marked as a manual sync, try to detect if it's from the sync screen
      if (!isManualSync) {
        try {
          // Get the current stack trace to check if this is called from the sync screen
          final stackTrace = StackTrace.current.toString();
          isManualSync = stackTrace.contains('sync_screen.dart');
        } catch (e) {
          // If we can't determine, assume it's not a manual sync
          isManualSync = false;
        }
      }

      // If it's not a manual sync, check if auto sync is enabled
      if (!isManualSync) {
        final syncEnabled = _ref.read(syncEnabledProvider);
        if (!syncEnabled) {
          _logger.addLogEntry(
            'Auto sync is disabled, skipping sync operation: $operation',
          );
          return;
        }
      } else {
        _logger.addLogEntry('Manual sync triggered: $operation');
      }

      await _executeSyncOperation(operation);
    } catch (e) {
      debugPrint('Immediate sync error: $e');
      rethrow;
    }
  }

  /// Execute the sync operation
  Future<void> _executeSyncOperation(SyncOperation operation) async {
    // Don't run multiple syncs at the same time
    if (_stateManager.isSyncing) {
      _logger.addLogEntry(
        'Sync already in progress, skipping operation: $operation',
      );
      return;
    }

    _stateManager.startSync(operation.toString());

    try {
      // Check connectivity and authentication
      if (!await _checkConnectivityAndAuth()) {
        return;
      }

      bool syncSuccessful = true;
      const String errorMessage = '';

      // Execute the appropriate sync operation
      switch (operation) {
        case SyncOperation.upload:
          syncSuccessful = await _handleUploadOperation(errorMessage);
          break;
        case SyncOperation.download:
          syncSuccessful = await _handleDownloadOperation(errorMessage);
          break;
        case SyncOperation.full:
          syncSuccessful = await _handleFullSyncOperation(errorMessage);
          break;
      }

      // Update last sync time and UI state
      await _stateManager.completeSync(syncSuccessful, errorMessage);
    } catch (e) {
      _logger.addLogEntry('Sync failed with error: $e');

      // Update UI state to show error
      _ref.read(syncStateProvider.notifier).state = rp.AsyncValue.error(
        'Sync failed: $e',
        StackTrace.current,
      );
      // Don't rethrow, so the app can continue
    } finally {
      // State manager handles completion in completeSync method
    }
  }

  /// Check connectivity and authentication
  Future<bool> _checkConnectivityAndAuth() async {
    // Check internet connection
    final connected = await _connectivityMonitor.isConnected();
    if (!connected) {
      _logger.addLogEntry('No internet connection, skipping sync');

      // Update UI state to show error
      _ref.read(syncStateProvider.notifier).state = rp.AsyncValue.error(
        'No internet connection',
        StackTrace.current,
      );

      return false;
    }

    // Ensure Supabase is initialized
    if (!_supabase.isInitialized) {
      _logger.addLogEntry('Initializing Supabase connection');
      try {
        await _supabase.initialize();
      } catch (e) {
        _logger.addLogEntry('Error initializing Supabase: $e');
        _ref.read(syncStateProvider.notifier).state = rp.AsyncValue.error(
          'Error initializing Supabase: $e',
          StackTrace.current,
        );
        return false;
      }
    }

    // Check if user is authenticated
    final authService = _ref.read(authServiceProvider);
    if (!authService.isAuthenticated) {
      _logger.addLogEntry(
        'User not authenticated, skipping sync. Authentication is optional but required for sync.',
      );

      // Update UI state to show error with more helpful message
      _ref.read(syncStateProvider.notifier).state = rp.AsyncValue.error(
        'Login required for sync. Go to More > Login to enable sync.',
        StackTrace.current,
      );

      return false;
    }

    return true;
  }

  /// Handle upload operation
  Future<bool> _handleUploadOperation(String errorMessage) async {
    return await _uploadHandler.handleUploadOperation();
  }

  /// Handle download operation
  Future<bool> _handleDownloadOperation(String errorMessage) async {
    return await _downloadHandler.handleDownloadOperation();
  }

  /// Handle full sync operation (upload + download)
  Future<bool> _handleFullSyncOperation(String errorMessage) async {
    _logger.addLogEntry('Starting full sync operation');

    final uploadSuccessful = await _uploadHandler.handleUploadOperation();
    final downloadSuccessful = await _downloadHandler.handleDownloadOperation();

    return uploadSuccessful && downloadSuccessful;
  }

  /// Schedule debounced sync
  void scheduleDebounceSync(
    Function() syncFunction, {
    Duration delay = const Duration(seconds: 5),
  }) {
    _stateManager.scheduleDebounceSync(syncFunction, delay: delay);
  }

  /// Cancel debounced sync
  void cancelDebounceSync() {
    _stateManager.cancelDebounceSync();
  }

  /// Dispose resources
  void dispose() {
    _stateManager.dispose();
  }
}

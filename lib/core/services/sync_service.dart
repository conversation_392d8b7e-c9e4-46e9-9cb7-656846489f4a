import 'package:flutter_riverpod/flutter_riverpod.dart' as rp;

import '../providers/app_settings_provider.dart';
import 'sync/sync_service.dart';

export 'sync/sync_logger.dart' show syncLogsProvider;
// Re-export providers from the new implementation
export 'sync/sync_operations.dart' show syncStateProvider, lastSyncTimeProvider;
export 'sync/sync_operations.dart' show SyncOperation;

// Provider for the sync service
final syncServiceProvider = rp.Provider<SyncService>((ref) {
  final db = ref.watch(databaseProvider);
  return SyncService(db, ref);
});

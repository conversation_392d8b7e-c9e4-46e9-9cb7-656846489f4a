/// Core barrel export file for the bidtrakr application
/// 
/// This file provides a single import point for all core functionality:
/// ```dart
/// import 'package:bidtrakr/core/core.dart';
/// ```
library;

// Components and UI
export 'components/app_components.dart';

// Constants
export 'constants/app_constants.dart';

// Data sources and database
export 'datasources/app_database.dart';
export 'datasources/converters/converters.dart';
export 'datasources/tables/tables.dart';

// Errors and exceptions
export 'errors/exceptions.dart';
export 'errors/failures.dart';

// Presentation utilities
export 'presentation/presentation.dart';

// Providers
export 'providers/providers.dart';

// Repositories
export 'repositories/repositories.dart';

// Services
export 'services/services.dart';

// Utils
export 'utils/utils.dart';

// Version management
export 'version/database_version_manager.dart';

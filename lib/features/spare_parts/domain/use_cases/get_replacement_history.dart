import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/replacement_history.dart';
import '../repositories/spare_parts_repository.dart';

class GetReplacementHistory {
  final SparePartsRepository repository;

  GetReplacementHistory(this.repository);

  Future<Either<Failure, List<ReplacementHistory>>> execute(
    int sparePartId,
  ) async {
    return await repository.getReplacementHistoryForPart(sparePartId);
  }
}

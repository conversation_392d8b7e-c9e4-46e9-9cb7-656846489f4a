import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/replacement_history.dart';
import '../entities/spare_part.dart';

abstract class SparePartsRepository {
  // Basic CRUD operations
  Future<Either<Failure, List<SparePart>>> getAllSpareParts();
  Future<Either<Failure, SparePart>> getSparePartById(int id);
  Future<Either<Failure, SparePart>> saveSparePart(SparePart sparePart);
  Future<Either<Failure, SparePart>> updateSparePart(SparePart sparePart);
  Future<Either<Failure, bool>> deleteSparePart(int id);

  //  operations
  Future<Either<Failure, List<ReplacementHistory>>>
  getReplacementHistoryForPart(int sparePartId);
  Future<Either<Failure, ReplacementHistory>> saveReplacementHistory({
    required String partName,
    required String partType,
    required double price,
    required DateTime replacementDate,
    required int mileageAtReplacement,
    required int sparePartId,
    required DateTime installationDate,
    required int initialMileage,
    String replacementReason = 'Regular maintenance',
    int? replacedByPartId,
    int replacementCount = 1,
    String notes = '',
  });

  // Mileage operations
  Future<Either<Failure, int>> getHighestMileage();
  Future<Either<Failure, bool>> updateAllPartsMileage(int latestMileage);

  // Replacement operations
  Future<Either<Failure, ReplacementResult>> replaceSparePart({
    required SparePart oldPart,
    required DateTime replacementDate,
    required int currentMileage,
    required double newPartPrice,
    String? newPartName,
    String? newPartType,
    int? newMileageLimit,
    String replacementReason = 'Regular maintenance',
    String notes = '',
  });
}

/// Data class to hold the result of a replacement operation
class ReplacementResult {
  final SparePart sparePart;
  final ReplacementHistory replacementHistory;

  ReplacementResult({
    required this.sparePart,
    required this.replacementHistory,
  });
}

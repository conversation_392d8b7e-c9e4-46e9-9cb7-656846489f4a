class ReplacementHistory {
  final int? id;
  final String partName;
  final String partType;
  final double price;
  final DateTime replacementDate;
  final int mileageAtReplacement;
  final int sparePartId;

  // Core fields for tracking usage
  final DateTime installationDate;
  final int initialMileage;

  // Additional fields for better history tracking
  final String replacementReason;
  final int? replacedByPartId;
  final int replacementCount;
  final String notes;
  final DateTime createdAt;

  // Computed fields for UI display
  final int usageDays; // days between installation and replacement
  final int usageMileage; // mileageAtReplacement - initialMileage

  ReplacementHistory({
    this.id,
    required this.partName,
    required this.partType,
    required this.price,
    required this.replacementDate,
    required this.mileageAtReplacement,
    required this.sparePartId,
    required this.installationDate,
    required this.initialMileage,
    this.replacementReason = 'Regular maintenance',
    this.replacedByPartId,
    this.replacementCount = 1,
    this.notes = '',
    DateTime? createdAt,
    this.usageDays = 0,
    this.usageMileage = 0,
  }) : createdAt = createdAt ?? DateTime.now();

  // Factory constructor to create an instance with computed values
  factory ReplacementHistory.withComputedValues({
    int? id,
    required String partName,
    required String partType,
    required double price,
    required DateTime replacementDate,
    required int mileageAtReplacement,
    required int sparePartId,
    required DateTime installationDate,
    required int initialMileage,
    String replacementReason = 'Regular maintenance',
    int? replacedByPartId,
    int replacementCount = 1,
    String notes = '',
    DateTime? createdAt,
  }) {
    // Calculate usage days based on actual dates
    final usageDays = replacementDate.difference(installationDate).inDays;

    // Calculate usage mileage based on actual mileage values
    final usageMileage = mileageAtReplacement - initialMileage;

    return ReplacementHistory(
      id: id,
      partName: partName,
      partType: partType,
      price: price,
      replacementDate: replacementDate,
      mileageAtReplacement: mileageAtReplacement,
      sparePartId: sparePartId,
      installationDate: installationDate,
      initialMileage: initialMileage,
      replacementReason: replacementReason,
      replacedByPartId: replacedByPartId,
      replacementCount: replacementCount,
      notes: notes,
      createdAt: createdAt,
      usageDays: usageDays,
      usageMileage: usageMileage,
    );
  }
}

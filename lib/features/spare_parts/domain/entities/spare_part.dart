class SparePart {
  final int? id;
  final String partName;
  final String partType;
  final double price;
  final int mileageLimit;
  final int initialMileage;
  final DateTime installationDate;
  final int currentMileage;
  final bool warningStatus;

  // New fields for better tracking
  final int replacementCount; // How many times this part has been replaced
  final String notes; // Optional notes about the part
  final DateTime createdAt; // When the record was created
  final DateTime updatedAt; // When the record was last updated

  // Computed fields for UI display
  final int usageMileage; // currentMileage - initialMileage
  final int daysInUse; // days since installation
  final double usagePercent; // (usageMileage / mileageLimit) * 100

  SparePart({
    this.id,
    required this.partName,
    required this.partType,
    required this.price,
    required this.mileageLimit,
    required this.initialMileage,
    required this.installationDate,
    this.currentMileage = 0,
    this.warningStatus = false,
    this.replacementCount = 0,
    this.notes = '',
    DateTime? createdAt,
    DateTime? updatedAt,
    this.usageMileage = 0,
    this.daysInUse = 0,
    this.usagePercent = 0.0,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Factory constructor to create an instance with computed values
  factory SparePart.withComputedValues({
    int? id,
    required String partName,
    required String partType,
    required double price,
    required int mileageLimit,
    required int initialMileage,
    required DateTime installationDate,
    required int currentMileage,
    bool warningStatus = false,
    int replacementCount = 0,
    String notes = '',
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    final usageMileage = currentMileage - initialMileage;
    final daysInUse = DateTime.now().difference(installationDate).inDays;
    final usagePercent = mileageLimit > 0
        ? (usageMileage / mileageLimit) * 100
        : 0.0;

    return SparePart(
      id: id,
      partName: partName,
      partType: partType,
      price: price,
      mileageLimit: mileageLimit,
      initialMileage: initialMileage,
      installationDate: installationDate,
      currentMileage: currentMileage,
      warningStatus: warningStatus,
      replacementCount: replacementCount,
      notes: notes,
      createdAt: createdAt,
      updatedAt: updatedAt,
      usageMileage: usageMileage,
      daysInUse: daysInUse,
      usagePercent: usagePercent,
    );
  }
}

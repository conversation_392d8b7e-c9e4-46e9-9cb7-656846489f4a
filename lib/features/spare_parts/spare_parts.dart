/// Spare Parts feature barrel export file
///
/// This file provides a single import point for all spare parts functionality:
/// ```dart
/// import 'package:bidtrakr/features/spare_parts/spare_parts.dart';
/// ```
library;

// Data layer
export 'data/repositories/spare_parts_repository_impl.dart';

// Domain layer
export 'domain/entities/spare_part.dart';
export 'domain/entities/replacement_history.dart';
export 'domain/repositories/spare_parts_repository.dart';
export 'domain/use_cases/add_spare_part.dart';
export 'domain/use_cases/get_replacement_history.dart';
export 'domain/use_cases/get_spare_part.dart';
export 'domain/use_cases/get_spare_parts.dart';
export 'domain/use_cases/replace_spare_part.dart';

// Presentation layer
export 'presentation/screens/replace_spare_part_screen.dart';
export 'presentation/screens/replacement_history_screen.dart';
export 'presentation/screens/spare_part_form_screen.dart';
export 'presentation/screens/spare_parts_screen.dart';
export 'presentation/providers/spare_parts_providers.dart';
export 'presentation/widgets/additional_information_section.dart';
export 'presentation/widgets/current_part_statistics_widget.dart';
export 'presentation/widgets/empty_history_widget.dart';
export 'presentation/widgets/error_widget.dart';
export 'presentation/widgets/history_card_widget.dart';
export 'presentation/widgets/history_list_widget.dart';
export 'presentation/widgets/maintenance_alert_card.dart';
export 'presentation/widgets/mileage_information_section.dart';
export 'presentation/widgets/part_information_section.dart';
export 'presentation/widgets/section_header.dart';
export 'presentation/widgets/spare_part_card.dart';
export 'presentation/widgets/spare_parts_empty_view.dart';
export 'presentation/widgets/spare_parts_error_view.dart';
export 'presentation/widgets/spare_parts_options_bottom_sheet.dart';
export 'presentation/widgets/stat_widgets.dart';

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'spare_parts_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sparePartsRepositoryHash() =>
    r'8c7f673fab5070e85b81628a5ba2579312f44d86';

/// See also [sparePartsRepository].
@ProviderFor(sparePartsRepository)
final sparePartsRepositoryProvider =
    AutoDisposeProvider<SparePartsRepository>.internal(
      sparePartsRepository,
      name: r'sparePartsRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sparePartsRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SparePartsRepositoryRef = AutoDisposeProviderRef<SparePartsRepository>;
String _$getSparePartsHash() => r'df6b0f436c07126037631a5d48bdd74453eb8ca1';

/// See also [getSpareParts].
@ProviderFor(getSpareParts)
final getSparePartsProvider = AutoDisposeProvider<GetSpareParts>.internal(
  getSpareParts,
  name: r'getSparePartsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getSparePartsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetSparePartsRef = AutoDisposeProviderRef<GetSpareParts>;
String _$getSparePartHash() => r'2357b09fb3fcda9e2709dab9932f79b2e79f3964';

/// See also [getSparePart].
@ProviderFor(getSparePart)
final getSparePartProvider = AutoDisposeProvider<GetSparePart>.internal(
  getSparePart,
  name: r'getSparePartProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getSparePartHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetSparePartRef = AutoDisposeProviderRef<GetSparePart>;
String _$addSparePartHash() => r'3126ea3ecf1bd3873b3f26638d5e977424894b31';

/// See also [addSparePart].
@ProviderFor(addSparePart)
final addSparePartProvider = AutoDisposeProvider<AddSparePart>.internal(
  addSparePart,
  name: r'addSparePartProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addSparePartHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AddSparePartRef = AutoDisposeProviderRef<AddSparePart>;
String _$replaceSparePartHash() => r'20cb1f7b7accfbfc7b04209e868fa0764e0f0116';

/// See also [replaceSparePart].
@ProviderFor(replaceSparePart)
final replaceSparePartProvider = AutoDisposeProvider<ReplaceSparePart>.internal(
  replaceSparePart,
  name: r'replaceSparePartProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$replaceSparePartHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ReplaceSparePartRef = AutoDisposeProviderRef<ReplaceSparePart>;
String _$getReplacementHistoryHash() =>
    r'4cc8481ce83b8aa5c29a8c869fa1c63cd84b520b';

/// See also [getReplacementHistory].
@ProviderFor(getReplacementHistory)
final getReplacementHistoryProvider =
    AutoDisposeProvider<GetReplacementHistory>.internal(
      getReplacementHistory,
      name: r'getReplacementHistoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$getReplacementHistoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetReplacementHistoryRef =
    AutoDisposeProviderRef<GetReplacementHistory>;
String _$sparePartsListHash() => r'725d4249c8b095702222a4dd89db8e34600d63a7';

/// See also [sparePartsList].
@ProviderFor(sparePartsList)
final sparePartsListProvider =
    AutoDisposeFutureProvider<List<SparePart>>.internal(
      sparePartsList,
      name: r'sparePartsListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sparePartsListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SparePartsListRef = AutoDisposeFutureProviderRef<List<SparePart>>;
String _$sparePartHash() => r'4b7dfa23530dd3efc5c1d31431e246408dee4a31';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [sparePart].
@ProviderFor(sparePart)
const sparePartProvider = SparePartFamily();

/// See also [sparePart].
class SparePartFamily extends Family<AsyncValue<SparePart?>> {
  /// See also [sparePart].
  const SparePartFamily();

  /// See also [sparePart].
  SparePartProvider call(int id) {
    return SparePartProvider(id);
  }

  @override
  SparePartProvider getProviderOverride(covariant SparePartProvider provider) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sparePartProvider';
}

/// See also [sparePart].
class SparePartProvider extends AutoDisposeFutureProvider<SparePart?> {
  /// See also [sparePart].
  SparePartProvider(int id)
    : this._internal(
        (ref) => sparePart(ref as SparePartRef, id),
        from: sparePartProvider,
        name: r'sparePartProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sparePartHash,
        dependencies: SparePartFamily._dependencies,
        allTransitiveDependencies: SparePartFamily._allTransitiveDependencies,
        id: id,
      );

  SparePartProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final int id;

  @override
  Override overrideWith(
    FutureOr<SparePart?> Function(SparePartRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SparePartProvider._internal(
        (ref) => create(ref as SparePartRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<SparePart?> createElement() {
    return _SparePartProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SparePartProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SparePartRef on AutoDisposeFutureProviderRef<SparePart?> {
  /// The parameter `id` of this provider.
  int get id;
}

class _SparePartProviderElement
    extends AutoDisposeFutureProviderElement<SparePart?>
    with SparePartRef {
  _SparePartProviderElement(super.provider);

  @override
  int get id => (origin as SparePartProvider).id;
}

String _$replacementHistoryHash() =>
    r'3a14b980a24d7599882761d3e981640682b52e56';

/// See also [replacementHistory].
@ProviderFor(replacementHistory)
const replacementHistoryProvider = ReplacementHistoryFamily();

/// See also [replacementHistory].
class ReplacementHistoryFamily
    extends Family<AsyncValue<List<ReplacementHistory>>> {
  /// See also [replacementHistory].
  const ReplacementHistoryFamily();

  /// See also [replacementHistory].
  ReplacementHistoryProvider call(int sparePartId) {
    return ReplacementHistoryProvider(sparePartId);
  }

  @override
  ReplacementHistoryProvider getProviderOverride(
    covariant ReplacementHistoryProvider provider,
  ) {
    return call(provider.sparePartId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'replacementHistoryProvider';
}

/// See also [replacementHistory].
class ReplacementHistoryProvider
    extends AutoDisposeFutureProvider<List<ReplacementHistory>> {
  /// See also [replacementHistory].
  ReplacementHistoryProvider(int sparePartId)
    : this._internal(
        (ref) => replacementHistory(ref as ReplacementHistoryRef, sparePartId),
        from: replacementHistoryProvider,
        name: r'replacementHistoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$replacementHistoryHash,
        dependencies: ReplacementHistoryFamily._dependencies,
        allTransitiveDependencies:
            ReplacementHistoryFamily._allTransitiveDependencies,
        sparePartId: sparePartId,
      );

  ReplacementHistoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sparePartId,
  }) : super.internal();

  final int sparePartId;

  @override
  Override overrideWith(
    FutureOr<List<ReplacementHistory>> Function(ReplacementHistoryRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ReplacementHistoryProvider._internal(
        (ref) => create(ref as ReplacementHistoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sparePartId: sparePartId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ReplacementHistory>> createElement() {
    return _ReplacementHistoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ReplacementHistoryProvider &&
        other.sparePartId == sparePartId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sparePartId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ReplacementHistoryRef
    on AutoDisposeFutureProviderRef<List<ReplacementHistory>> {
  /// The parameter `sparePartId` of this provider.
  int get sparePartId;
}

class _ReplacementHistoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ReplacementHistory>>
    with ReplacementHistoryRef {
  _ReplacementHistoryProviderElement(super.provider);

  @override
  int get sparePartId => (origin as ReplacementHistoryProvider).sparePartId;
}

String _$addSparePartOperationHash() =>
    r'17375bef36dbc331bd78cacfc08e9b617a45bf0d';

/// See also [AddSparePartOperation].
@ProviderFor(AddSparePartOperation)
final addSparePartOperationProvider =
    AutoDisposeAsyncNotifierProvider<
      AddSparePartOperation,
      SparePart?
    >.internal(
      AddSparePartOperation.new,
      name: r'addSparePartOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$addSparePartOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AddSparePartOperation = AutoDisposeAsyncNotifier<SparePart?>;
String _$replaceSparePartOperationHash() =>
    r'd673ea8871bfb420944bafad66a728bfc9311757';

/// See also [ReplaceSparePartOperation].
@ProviderFor(ReplaceSparePartOperation)
final replaceSparePartOperationProvider =
    AutoDisposeAsyncNotifierProvider<
      ReplaceSparePartOperation,
      ReplacementResult?
    >.internal(
      ReplaceSparePartOperation.new,
      name: r'replaceSparePartOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$replaceSparePartOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ReplaceSparePartOperation =
    AutoDisposeAsyncNotifier<ReplacementResult?>;
String _$deleteSparePartOperationHash() =>
    r'09e44822612cb122bd96e73823c7241610e57e6d';

/// See also [DeleteSparePartOperation].
@ProviderFor(DeleteSparePartOperation)
final deleteSparePartOperationProvider =
    AutoDisposeAsyncNotifierProvider<DeleteSparePartOperation, bool?>.internal(
      DeleteSparePartOperation.new,
      name: r'deleteSparePartOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$deleteSparePartOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DeleteSparePartOperation = AutoDisposeAsyncNotifier<bool?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

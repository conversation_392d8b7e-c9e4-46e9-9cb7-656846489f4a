import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/utils/snackbar_utils.dart';
import '../../domain/entities/spare_part.dart';
import '../providers/spare_parts_providers.dart';
import '../widgets/additional_information_section.dart';
import '../widgets/mileage_information_section.dart';
import '../widgets/part_information_section.dart';
import '../widgets/section_header.dart';

class SparePartFormScreen extends ConsumerStatefulWidget {
  final int? sparePartId;

  const SparePartFormScreen({super.key, this.sparePartId});

  @override
  ConsumerState<SparePartFormScreen> createState() =>
      _SparePartFormScreenState();
}

class _SparePartFormScreenState extends ConsumerState<SparePartFormScreen> {
  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Text controllers for form fields
  final _partNameController = TextEditingController();
  final _partTypeController = TextEditingController();
  final _priceController = TextEditingController();
  final _mileageLimitController = TextEditingController();
  final _initialMileageController = TextEditingController();
  final _notesController = TextEditingController();

  // State variables
  DateTime _installationDate = DateTime.now();
  bool _isLoading = false;
  bool _isEditing = false;
  bool _useCurrentMileage = true;

  // ===== Lifecycle Methods =====

  @override
  void initState() {
    super.initState();
    _isEditing = widget.sparePartId != null;

    if (_isEditing) {
      _loadSparePartData();
    } else {
      _loadCurrentMileage();
    }
  }

  @override
  void dispose() {
    // Clean up controllers when the widget is disposed
    _partNameController.dispose();
    _partTypeController.dispose();
    _priceController.dispose();
    _mileageLimitController.dispose();
    _initialMileageController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // ===== UI Helper Methods =====

  // Show error message in a snackbar
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    SnackbarUtils.showError(message);
  }

  // Show success message in a snackbar
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    SnackbarUtils.showSuccess(message);
  }

  // ===== Data Loading Methods =====

  // Load spare part data when editing an existing part
  Future<void> _loadSparePartData() async {
    setState(() => _isLoading = true);

    try {
      final sparePartAsync = await ref.read(
        sparePartProvider(widget.sparePartId!).future,
      );

      if (sparePartAsync != null) {
        _partNameController.text = sparePartAsync.partName;
        _partTypeController.text = sparePartAsync.partType;
        _priceController.text = sparePartAsync.price == 0
            ? ''
            : sparePartAsync.price.toString();
        _mileageLimitController.text = sparePartAsync.mileageLimit == 0
            ? ''
            : sparePartAsync.mileageLimit.toString();
        _initialMileageController.text = sparePartAsync.initialMileage == 0
            ? ''
            : sparePartAsync.initialMileage.toString();
        _notesController.text = sparePartAsync.notes;
        _installationDate = sparePartAsync.installationDate;
        _useCurrentMileage =
            false; // When editing, use the stored initial mileage
      }
    } catch (e) {
      _showErrorSnackBar('Error loading spare part: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Load current vehicle mileage for new parts
  Future<void> _loadCurrentMileage() async {
    setState(() => _isLoading = true);

    try {
      final repository = ref.read(sparePartsRepositoryProvider);
      final mileageResult = await repository.getHighestMileage();

      mileageResult.fold(
        (failure) {
          _showErrorSnackBar('Error loading current mileage: $failure');
          _initialMileageController.text = '0';
        },
        (mileage) {
          _initialMileageController.text = mileage.toString();
        },
      );
    } catch (e) {
      _showErrorSnackBar('Error loading current mileage: $e');
      _initialMileageController.text = '0';
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // ===== Form Submission =====

  // Save or update the spare part
  Future<void> _saveSparePart() async {
    // Validate the form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Extract form values
      final partName = _partNameController.text.trim();
      final partType = _partTypeController.text.trim();
      final price = _priceController.text.isEmpty
          ? 0.0
          : double.parse(_priceController.text);
      final mileageLimit = _mileageLimitController.text.isEmpty
          ? 0
          : int.parse(_mileageLimitController.text);
      final initialMileage = _initialMileageController.text.isEmpty
          ? 0
          : int.parse(_initialMileageController.text);
      final notes = _notesController.text.trim();

      if (_isEditing) {
        // Update existing spare part
        try {
          final repository = ref.read(sparePartsRepositoryProvider);

          // Get the current spare part to preserve existing values
          final sparePartResult = await repository.getSparePartById(
            widget.sparePartId!,
          );

          final result = await sparePartResult.fold(
            (failure) {
              throw Exception(
                'Failed to get spare part: ${failure.toString()}',
              );
            },
            (sparePart) async {
              // Create updated spare part with new values
              final updatedPart = SparePart.withComputedValues(
                id: widget.sparePartId,
                partName: partName,
                partType: partType,
                price: price,
                mileageLimit: mileageLimit,
                initialMileage: initialMileage,
                installationDate: _installationDate,
                currentMileage: sparePart.currentMileage,
                warningStatus: sparePart.warningStatus,
                replacementCount: sparePart.replacementCount,
                notes: notes,
                createdAt: sparePart.createdAt,
                updatedAt: DateTime.now(),
              );

              // Return the update result
              return repository.updateSparePart(updatedPart);
            },
          );

          // Handle the result
          result.fold(
            (failure) {
              throw Exception(
                'Failed to update spare part: ${failure.toString()}',
              );
            },
            (_) {
              // Refresh the spare parts list
              ref.invalidate(sparePartsListProvider);

              if (mounted) {
                _showSuccessSnackBar('Spare part updated successfully');
                Navigator.of(context).pop();
              }
            },
          );
        } catch (e) {
          _showErrorSnackBar('Error updating spare part: $e');
        }
      } else {
        // Add new spare part
        try {
          final addOperation = ref.read(addSparePartOperationProvider.notifier);
          await addOperation.addSparePart(
            partName: partName,
            partType: partType,
            price: price,
            mileageLimit: mileageLimit,
            installationDate: _installationDate,
            initialMileage: initialMileage,
            notes: notes,
          );

          if (mounted) {
            _showSuccessSnackBar('Spare part added successfully');
            Navigator.of(context).pop();
          }
        } catch (e) {
          _showErrorSnackBar('Error adding spare part: $e');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error saving spare part: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // ===== UI Building =====

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Spare Part' : 'Add Spare Part'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveSparePart,
            tooltip: _isEditing ? 'Update' : 'Save',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Part Information Section
                    PartInformationSection(
                      partNameController: _partNameController,
                      partTypeController: _partTypeController,
                      priceController: _priceController,
                      buildSectionHeader: (title) =>
                          SectionHeader(title: title),
                    ),

                    const SizedBox(height: 24),

                    // Mileage Information Section
                    MileageInformationSection(
                      mileageLimitController: _mileageLimitController,
                      initialMileageController: _initialMileageController,
                      installationDate: _installationDate,
                      isEditing: _isEditing,
                      useCurrentMileage: _useCurrentMileage,
                      onDateSelected: (date) {
                        setState(() => _installationDate = date);
                      },
                      onUseCurrentMileageChanged: (value) {
                        setState(() {
                          _useCurrentMileage = value ?? true;
                          if (_useCurrentMileage) {
                            _loadCurrentMileage();
                          } else {
                            _initialMileageController.text = '';
                          }
                        });
                      },
                      buildSectionHeader: (title) =>
                          SectionHeader(title: title),
                    ),

                    const SizedBox(height: 24),

                    // Additional Information Section
                    AdditionalInformationSection(
                      notesController: _notesController,
                      buildSectionHeader: (title) =>
                          SectionHeader(title: title),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }
}

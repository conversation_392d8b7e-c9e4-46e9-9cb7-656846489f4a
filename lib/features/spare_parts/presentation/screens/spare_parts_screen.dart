import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../domain/entities/spare_part.dart';
import '../providers/spare_parts_providers.dart';
import '../widgets/maintenance_alert_card.dart';
import '../widgets/spare_part_card.dart';
import '../widgets/spare_parts_empty_view.dart';
import '../widgets/spare_parts_error_view.dart';
import '../widgets/spare_parts_options_bottom_sheet.dart';
import 'replace_spare_part_screen.dart';
import 'replacement_history_screen.dart';
import 'spare_part_form_screen.dart';

class SparePartsScreen extends ConsumerStatefulWidget {
  const SparePartsScreen({super.key});

  @override
  ConsumerState<SparePartsScreen> createState() => _SparePartsScreenState();
}

class _SparePartsScreenState extends ConsumerState<SparePartsScreen> {
  @override
  void initState() {
    super.initState();
    // Automatically update mileage when the screen is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _autoUpdateMileage();
    });
  }

  Future<void> _autoUpdateMileage() async {
    try {
      // Get the highest mileage from income
      final repository = ref.read(sparePartsRepositoryProvider);
      final mileageResult = await repository.getHighestMileage();

      if (!mounted) return;

      await mileageResult.fold(
        (failure) {
          // If there's an error getting the mileage, just log it
          debugPrint('Error getting highest mileage: ${failure.toString()}');
          return Future.value(false);
        },
        (mileage) async {
          if (mileage > 0) {
            // Update all spare parts with the latest mileage
            final updateResult = await repository.updateAllPartsMileage(
              mileage,
            );

            return updateResult.fold(
              (failure) {
                debugPrint('Error updating mileage: ${failure.toString()}');
                return false;
              },
              (success) {
                // Refresh the list to show updated mileage
                if (mounted) {
                  ref.invalidate(sparePartsListProvider);
                }
                return true;
              },
            );
          }
          return false;
        },
      );
    } catch (e) {
      debugPrint('Error in auto-updating mileage: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use the provider to fetch the actual spare parts data
    final sparePartsAsync = ref.watch(sparePartsListProvider);

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        title: const Text('Spare Parts'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Spare Part',
            onPressed: () => _navigateToAddForm(context),
          ),
        ],
      ),
      body: sparePartsAsync.when(
        data: (spareParts) {
          if (spareParts.isEmpty) {
            return SparePartsEmptyView(
              onAddPressed: () => _navigateToAddForm(context),
              onRefresh: _autoUpdateMileage,
            );
          }

          return _buildSparePartsList(context, spareParts);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => SparePartsErrorView(
          error: error,
          onRetry: () => ref.invalidate(sparePartsListProvider),
        ),
      ),
    );
  }

  Widget _buildSparePartsList(
    BuildContext context,
    List<SparePart> spareParts,
  ) {
    // Group parts by warning status
    final warningParts = spareParts
        .where((part) => part.warningStatus == true)
        .toList();
    final normalParts = spareParts
        .where((part) => part.warningStatus == false)
        .toList();

    // Sort each group by usage percentage (highest first)
    warningParts.sort((a, b) => b.usagePercent.compareTo(a.usagePercent));
    normalParts.sort((a, b) => b.usagePercent.compareTo(a.usagePercent));

    return RefreshIndicator(
      onRefresh: _autoUpdateMileage,
      child: CustomScrollView(
        slivers: [
          if (warningParts.isNotEmpty)
            SliverToBoxAdapter(
              child: MaintenanceAlertCard(
                warningPartsCount: warningParts.length,
              ),
            ),
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
                // Show warning parts first, then normal parts
                final part = index < warningParts.length
                    ? warningParts[index]
                    : normalParts[index - warningParts.length];

                return SparePartCard(
                  sparePart: part,
                  onTap: () => _navigateToDetail(context, part.id!),
                );
              }, childCount: spareParts.length),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToDetail(BuildContext context, int sparePartId) {
    // Show a bottom sheet with options
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SparePartsOptionsBottomSheet(
        sparePartId: sparePartId,
        onViewHistory: () =>
            _navigateToReplacementHistory(context, sparePartId),
        onReplacePart: () => _navigateToReplacePart(context, sparePartId),
        onEditPart: () => _navigateToEditPart(context, sparePartId),
        onDeletePart: () => _showDeleteConfirmation(context, sparePartId),
      ),
    );
  }

  void _navigateToReplacementHistory(BuildContext context, int sparePartId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            ReplacementHistoryScreen(sparePartId: sparePartId),
      ),
    );
  }

  void _navigateToReplacePart(BuildContext context, int sparePartId) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) =>
                ReplaceSparePartScreen(sparePartId: sparePartId),
          ),
        )
        .then((result) {
          if (result == true) {
            // Refresh the list if replacement was successful
            ref.invalidate(sparePartsListProvider);

            // Also refresh the replacement history for this spare part
            debugPrint(
              'Invalidating replacement history provider for spare part ID: $sparePartId',
            );
            ref.invalidate(replacementHistoryProvider(sparePartId));
          }
        });
  }

  void _navigateToEditPart(BuildContext context, int sparePartId) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => SparePartFormScreen(sparePartId: sparePartId),
          ),
        )
        .then((_) {
          // Refresh the list when returning from the form screen
          ref.invalidate(sparePartsListProvider);
        });
  }

  void _showDeleteConfirmation(BuildContext context, int sparePartId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Spare Part'),
        content: const Text(
          'Are you sure you want to delete this spare part? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSparePart(context, sparePartId);
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSparePart(BuildContext context, int sparePartId) async {
    String? errorMessage;
    bool success = false;

    try {
      // Use the repository directly to delete the spare part
      final repository = ref.read(sparePartsRepositoryProvider);
      final result = await repository.deleteSparePart(sparePartId);

      result.fold(
        (failure) {
          errorMessage = 'Error deleting spare part: ${failure.toString()}';
        },
        (_) {
          success = true;
        },
      );
    } catch (e) {
      errorMessage = 'Error deleting spare part: $e';
    }

    // Check if the widget is still mounted before using the context
    if (!mounted) return;

    // Now it's safe to use the context after the async gap
    if (success) {
      _showSnackBar('Spare part deleted successfully');
      // Refresh the spare parts list
      ref.invalidate(sparePartsListProvider);
    } else if (errorMessage != null) {
      _showSnackBar(errorMessage.toString());
    }
  }

  // Helper method to show a snackbar
  void _showSnackBar(String message) {
    if (!mounted) return;

    // Use the new SnackbarUtils class for consistent snackbar display
    if (message.contains('successfully')) {
      SnackbarUtils.showSuccess(message);
    } else if (message.contains('Error')) {
      SnackbarUtils.showError(message);
    } else {
      SnackbarUtils.showInfo(message);
    }
  }

  void _navigateToAddForm(BuildContext context) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(builder: (context) => const SparePartFormScreen()),
        )
        .then((_) {
          // Refresh the list when returning from the form screen
          ref.invalidate(sparePartsListProvider);
        });
  }
}

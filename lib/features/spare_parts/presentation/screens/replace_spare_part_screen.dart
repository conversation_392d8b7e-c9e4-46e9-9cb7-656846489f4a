import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/utils/input_validators.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/custom_date_picker.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../domain/entities/spare_part.dart';
import '../providers/spare_parts_providers.dart';
import 'replacement_history_screen.dart';

/// State class for the ReplaceSparePartScreen
class ReplaceSparePartState {
  final bool isLoading;
  final SparePart? sparePart;
  final int currentMileage;
  final DateTime replacementDate;
  final bool keepSameName;
  final bool keepSameType;
  final bool keepSameMileageLimit;
  final String? errorMessage;

  ReplaceSparePartState({
    this.isLoading = false,
    this.sparePart,
    this.currentMileage = 0,
    DateTime? replacementDate,
    this.keepSameName = true,
    this.keepSameType = true,
    this.keepSameMileageLimit = true,
    this.errorMessage,
  }) : replacementDate =
           replacementDate ?? DateHelper.ensureUtc(DateTime.now());

  ReplaceSparePartState copyWith({
    bool? isLoading,
    SparePart? sparePart,
    int? currentMileage,
    DateTime? replacementDate,
    bool? keepSameName,
    bool? keepSameType,
    bool? keepSameMileageLimit,
    String? errorMessage,
  }) {
    return ReplaceSparePartState(
      isLoading: isLoading ?? this.isLoading,
      sparePart: sparePart ?? this.sparePart,
      currentMileage: currentMileage ?? this.currentMileage,
      replacementDate: replacementDate ?? this.replacementDate,
      keepSameName: keepSameName ?? this.keepSameName,
      keepSameType: keepSameType ?? this.keepSameType,
      keepSameMileageLimit: keepSameMileageLimit ?? this.keepSameMileageLimit,
      errorMessage: errorMessage,
    );
  }
}

/// ViewModel for the ReplaceSparePartScreen
class ReplaceSparePartViewModel {
  final WidgetRef ref;
  final int sparePartId;

  ReplaceSparePartViewModel(this.ref, this.sparePartId);

  /// Load the spare part data and current mileage
  Future<ReplaceSparePartState> loadInitialData() async {
    try {
      // Load the spare part data
      final sparePartAsync = await ref.read(
        sparePartProvider(sparePartId).future,
      );

      // Load the current mileage
      final repository = ref.read(sparePartsRepositoryProvider);
      final mileageResult = await repository.getHighestMileage();

      int currentMileage = 0;

      mileageResult.fold(
        (failure) {
          throw Exception('Error loading current mileage: $failure');
        },
        (mileage) {
          currentMileage = mileage;
        },
      );

      return ReplaceSparePartState(
        sparePart: sparePartAsync,
        currentMileage: currentMileage,
        replacementDate: DateHelper.ensureUtc(DateTime.now()),
      );
    } catch (e) {
      return ReplaceSparePartState(errorMessage: 'Error loading data: $e');
    }
  }

  /// Replace the spare part
  Future<ReplaceSparePartState> replaceSparePart({
    required ReplaceSparePartState currentState,
    required String partName,
    required String partType,
    required String price,
    required String mileageLimit,
    required String mileageAtReplacement,
    required String replacementReason,
    required String notes,
  }) async {
    try {
      final priceValue = price.isEmpty ? 0.0 : double.parse(price);
      final mileageAtReplacementValue = int.tryParse(mileageAtReplacement) ?? 0;
      final replacementReasonValue = replacementReason.trim();
      final notesValue = notes.trim();

      if (mileageAtReplacementValue <= 0) {
        throw Exception('Mileage at replacement must be greater than 0');
      }

      if (replacementReasonValue.isEmpty) {
        throw Exception('Replacement reason is required');
      }

      // Only pass new values if the user has chosen to change them
      final newPartName = currentState.keepSameName ? null : partName.trim();
      final newPartType = currentState.keepSameType ? null : partType.trim();
      final newMileageLimit = currentState.keepSameMileageLimit
          ? null
          : (mileageLimit.isEmpty ? 0 : int.parse(mileageLimit));

      final replaceOperation = ref.read(
        replaceSparePartOperationProvider.notifier,
      );
      await replaceOperation.replaceSparePart(
        sparePartId: sparePartId,
        replacementDate: currentState.replacementDate,
        newPartPrice: priceValue,
        newPartName: newPartName,
        newPartType: newPartType,
        newMileageLimit: newMileageLimit,
        currentMileage: mileageAtReplacementValue,
        replacementReason: replacementReasonValue,
        notes: notesValue,
      );

      return currentState.copyWith(isLoading: false);
    } catch (e) {
      return currentState.copyWith(
        isLoading: false,
        errorMessage: 'Error replacing spare part: $e',
      );
    }
  }
}

/// Screen for replacing a spare part with a new one
class ReplaceSparePartScreen extends ConsumerStatefulWidget {
  final int sparePartId;

  const ReplaceSparePartScreen({super.key, required this.sparePartId});

  @override
  ConsumerState<ReplaceSparePartScreen> createState() =>
      _ReplaceSparePartScreenState();
}

/// UI component for the part information section
class _PartInfoSection extends StatelessWidget {
  final TextEditingController partNameController;
  final TextEditingController partTypeController;
  final TextEditingController priceController;
  final bool keepSameName;
  final bool keepSameType;
  final Function(bool) onNameToggle;
  final Function(bool) onTypeToggle;

  const _PartInfoSection({
    required this.partNameController,
    required this.partTypeController,
    required this.priceController,
    required this.keepSameName,
    required this.keepSameType,
    required this.onNameToggle,
    required this.onTypeToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(context, 'Part Information', Icons.build),
        const SizedBox(height: 16),

        // Part Name
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.info.withAlpha(10),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Checkbox(
                value: keepSameName,
                onChanged: (value) => onNameToggle(value ?? true),
                activeColor: AppColors.info,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Text(
                'Keep same part name',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        CustomTextField(
          controller: partNameController,
          labelText: 'Part Name',
          hintText: 'Enter the name of the new part',
          prefixIcon: Icons.build,
          enabled: !keepSameName,
          validator: keepSameName
              ? null
              : InputValidators.required('Part name is required'),
        ),
        const SizedBox(height: 16),

        // Part Type
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.info.withAlpha(10),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Checkbox(
                value: keepSameType,
                onChanged: (value) => onTypeToggle(value ?? true),
                activeColor: AppColors.info,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Text(
                'Keep same part type',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        CustomTextField(
          controller: partTypeController,
          labelText: 'Part Type/Category',
          hintText: 'Enter the type or category of the new part',
          prefixIcon: Icons.category,
          enabled: !keepSameType,
          validator: keepSameType
              ? null
              : InputValidators.required('Part type is required'),
        ),
        const SizedBox(height: 16),

        // Part Price
        CustomTextField(
          controller: priceController,
          labelText: 'Part Price',
          hintText: 'Enter the price of the new part',
          prefixIcon: Icons.monetization_on,
          keyboardType: TextInputType.number,
          validator: InputValidators.compose([
            InputValidators.required('Price is required'),
            InputValidators.numeric('Price must be a number'),
            InputValidators.min(0, 'Price must be positive'),
          ]),
        ),
      ],
    );
  }
}

/// UI component for the mileage information section
class _MileageInfoSection extends StatelessWidget {
  final TextEditingController mileageLimitController;
  final TextEditingController mileageAtReplacementController;
  final TextEditingController currentMileageController;
  final bool keepSameMileageLimit;
  final Function(bool) onMileageLimitToggle;

  const _MileageInfoSection({
    required this.mileageLimitController,
    required this.mileageAtReplacementController,
    required this.currentMileageController,
    required this.keepSameMileageLimit,
    required this.onMileageLimitToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(context, 'Mileage Information', Icons.speed),
        const SizedBox(height: 16),

        // Mileage Limit
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.info.withAlpha(10),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Checkbox(
                value: keepSameMileageLimit,
                onChanged: (value) => onMileageLimitToggle(value ?? true),
                activeColor: AppColors.info,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Text(
                'Keep same mileage limit',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        CustomTextField(
          controller: mileageLimitController,
          labelText: 'Mileage Limit (km)',
          hintText: 'After how many kilometers should this part be replaced?',
          prefixIcon: Icons.speed,
          keyboardType: TextInputType.number,
          enabled: !keepSameMileageLimit,
          validator: keepSameMileageLimit
              ? null
              : InputValidators.compose([
                  InputValidators.required('Mileage limit is required'),
                  InputValidators.integer(
                    'Mileage limit must be a whole number',
                  ),
                  InputValidators.min(1, 'Mileage limit must be positive'),
                ]),
        ),
        const SizedBox(height: 16),

        // Mileage at Replacement
        CustomTextField(
          controller: mileageAtReplacementController,
          labelText: 'Mileage at Replacement (km)',
          hintText: 'Mileage when the part is being replaced',
          prefixIcon: Icons.speed,
          keyboardType: TextInputType.number,
          validator: InputValidators.compose([
            InputValidators.required('Mileage at replacement is required'),
            InputValidators.integer('Mileage must be a whole number'),
            InputValidators.min(1, 'Mileage must be positive'),
          ]),
        ),
        const SizedBox(height: 16),

        // Current Mileage
        CustomTextField(
          controller: currentMileageController,
          labelText: 'Current Vehicle Mileage (km)',
          hintText: 'Current vehicle mileage',
          prefixIcon: Icons.speed,
          keyboardType: TextInputType.number,
          enabled: false, // Read-only
          helperText:
              'This is the current vehicle mileage from the income records',
        ),
      ],
    );
  }
}

/// UI component for the replacement information section
class _ReplacementInfoSection extends StatelessWidget {
  final TextEditingController replacementReasonController;
  final TextEditingController notesController;
  final DateTime replacementDate;
  final Function(DateTime) onDateSelected;

  const _ReplacementInfoSection({
    required this.replacementReasonController,
    required this.notesController,
    required this.replacementDate,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Replacement Date
        _buildSectionHeader(context, 'Replacement Date', Icons.calendar_today),
        const SizedBox(height: 16),

        Card(
          elevation: 0,
          margin: const EdgeInsets.only(bottom: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: AppColors.primary.withAlpha(30), width: 1),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomDatePicker(
                  initialDate: replacementDate,
                  onDateSelected: (date) =>
                      onDateSelected(DateHelper.ensureUtc(date)),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                  labelText: 'Select Replacement Date',
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Additional Information Section
        _buildSectionHeader(
          context,
          'Replacement Information',
          Icons.swap_horiz,
        ),
        const SizedBox(height: 16),

        // Replacement Reason
        CustomTextField(
          controller: replacementReasonController,
          labelText: 'Replacement Reason',
          hintText: 'Why is this part being replaced?',
          prefixIcon: Icons.info_outline,
          maxLines: 2,
          validator: InputValidators.required('Replacement reason is required'),
        ),

        // Notes field
        const SizedBox(height: 16),
        CustomTextField(
          controller: notesController,
          labelText: 'Notes (Optional)',
          hintText: 'Add any notes about this replacement',
          prefixIcon: Icons.note,
          maxLines: 3,
        ),
      ],
    );
  }
}

/// Helper method to build section headers
Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          Icon(icon, size: 18, color: AppColors.primary),
          const SizedBox(width: 8),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
        ],
      ),
      const SizedBox(height: 8),
      Divider(color: Colors.grey.withAlpha(30), thickness: 1),
      const SizedBox(height: 8),
    ],
  );
}

class _ReplaceSparePartScreenState
    extends ConsumerState<ReplaceSparePartScreen> {
  final _formKey = GlobalKey<FormState>();
  late final ReplaceSparePartViewModel _viewModel;

  // Controllers
  final _partNameController = TextEditingController();
  final _partTypeController = TextEditingController();
  final _priceController = TextEditingController();
  final _mileageLimitController = TextEditingController();
  final _mileageAtReplacementController = TextEditingController();
  final _currentMileageController = TextEditingController();
  final _replacementReasonController = TextEditingController(
    text: 'Regular maintenance',
  );
  final _notesController = TextEditingController();

  // State
  ReplaceSparePartState _state = ReplaceSparePartState();

  @override
  void initState() {
    super.initState();
    _viewModel = ReplaceSparePartViewModel(ref, widget.sparePartId);
    _loadData();
  }

  @override
  void dispose() {
    _partNameController.dispose();
    _partTypeController.dispose();
    _priceController.dispose();
    _mileageLimitController.dispose();
    _mileageAtReplacementController.dispose();
    _currentMileageController.dispose();
    _replacementReasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _state = _state.copyWith(isLoading: true);
    });

    final newState = await _viewModel.loadInitialData();

    if (newState.errorMessage != null && mounted) {
      SnackbarUtils.showError(newState.errorMessage!);
    }

    if (newState.sparePart != null) {
      _partNameController.text = newState.sparePart!.partName;
      _partTypeController.text = newState.sparePart!.partType;
      _mileageLimitController.text = newState.sparePart!.mileageLimit == 0
          ? ''
          : newState.sparePart!.mileageLimit.toString();
    }

    _currentMileageController.text = newState.currentMileage.toString();
    _mileageAtReplacementController.text = newState.currentMileage.toString();

    if (mounted) {
      setState(() {
        _state = newState;
      });
    }
  }

  Future<void> _replaceSparePart() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _state = _state.copyWith(isLoading: true);
    });

    final newState = await _viewModel.replaceSparePart(
      currentState: _state,
      partName: _partNameController.text,
      partType: _partTypeController.text,
      price: _priceController.text,
      mileageLimit: _mileageLimitController.text,
      mileageAtReplacement: _mileageAtReplacementController.text,
      replacementReason: _replacementReasonController.text,
      notes: _notesController.text,
    );

    if (mounted) {
      setState(() {
        _state = newState;
      });

      if (newState.errorMessage != null) {
        SnackbarUtils.showError(newState.errorMessage!);
      } else {
        SnackbarUtils.showSuccess('Spare part replaced successfully');

        // Return true to indicate success
        Navigator.of(context).pop(true);

        // Navigate to the replacement history screen
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                ReplacementHistoryScreen(sparePartId: widget.sparePartId),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primary,
        title: const Text('Replace Spare Part'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _state.isLoading ? null : _replaceSparePart,
            tooltip: 'Replace',
          ),
        ],
      ),
      body: _state.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Part Information Section
                    _PartInfoSection(
                      partNameController: _partNameController,
                      partTypeController: _partTypeController,
                      priceController: _priceController,
                      keepSameName: _state.keepSameName,
                      keepSameType: _state.keepSameType,
                      onNameToggle: (value) {
                        setState(() {
                          _state = _state.copyWith(keepSameName: value);
                        });
                      },
                      onTypeToggle: (value) {
                        setState(() {
                          _state = _state.copyWith(keepSameType: value);
                        });
                      },
                    ),

                    const SizedBox(height: 24),

                    // Mileage Information Section
                    _MileageInfoSection(
                      mileageLimitController: _mileageLimitController,
                      mileageAtReplacementController:
                          _mileageAtReplacementController,
                      currentMileageController: _currentMileageController,
                      keepSameMileageLimit: _state.keepSameMileageLimit,
                      onMileageLimitToggle: (value) {
                        setState(() {
                          _state = _state.copyWith(keepSameMileageLimit: value);
                        });
                      },
                    ),

                    const SizedBox(height: 24),

                    // Replacement Information Section
                    _ReplacementInfoSection(
                      replacementReasonController: _replacementReasonController,
                      notesController: _notesController,
                      replacementDate: _state.replacementDate,
                      onDateSelected: (date) {
                        setState(() {
                          _state = _state.copyWith(replacementDate: date);
                        });
                      },
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }
}

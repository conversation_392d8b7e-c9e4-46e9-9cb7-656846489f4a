import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class MaintenanceAlertCard extends StatefulWidget {
  final int warningPartsCount;

  const MaintenanceAlertCard({super.key, required this.warningPartsCount});

  @override
  State<MaintenanceAlertCard> createState() => _MaintenanceAlertCardState();
}

class _MaintenanceAlertCardState extends State<MaintenanceAlertCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutBack,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 1.0, curve: Curves.easeIn),
      ),
    );

    // Start animation after a small delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return FadeTransition(
      opacity: _opacityAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  isDark 
                      ? AppColors.warning.withAlpha(38) // 15% of 255
                      : AppColors.warning.withAlpha(20), // 8% of 255
                  isDark 
                      ? AppColors.warning.withAlpha(20) // 8% of 255
                      : AppColors.warning.withAlpha(8), // 3% of 255
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.warning.withAlpha(77), // 30% of 255
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.warning.withAlpha(26), // 10% of 255
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Decorative elements
                Positioned(
                  top: -10,
                  right: -10,
                  child: Opacity(
                    opacity: 0.4,
                    child: Icon(
                      Icons.construction_rounded,
                      size: 80,
                      color: AppColors.warning,
                    ),
                  ),
                ),
                // Main content
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Animated warning icon
                      TweenAnimationBuilder(
                        tween: Tween<double>(begin: 0, end: 1),
                        duration: const Duration(milliseconds: 1000),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: 0.8 + (0.2 * value),
                            child: child,
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.warning.withAlpha(51), // 20% of 255
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.warning_amber_rounded,
                            color: AppColors.warning,
                            size: 24,
                          ),
                        ),
                      ),
                      
                      const SizedBox(width: 16),
                      
                      // Text content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Maintenance Required',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: isDark ? Colors.white : Colors.grey[900],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${widget.warningPartsCount} ${widget.warningPartsCount == 1 ? 'part needs' : 'parts need'} immediate attention',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: isDark ? Colors.white70 : Colors.grey[700],
                                height: 1.4,
                              ),
                            ),
                            const SizedBox(height: 8),
                            // View button
                            Align(
                              alignment: Alignment.centerLeft,
                              child: TextButton.icon(
                                onPressed: () {
                                  // TODO: Navigate to parts needing attention
                                },
                                style: TextButton.styleFrom(
                                  foregroundColor: AppColors.warning,
                                  padding: EdgeInsets.zero,
                                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                ),
                                icon: const Text('View All'),
                                label: const Icon(Icons.arrow_forward_ios_rounded, size: 12),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Badge with count
                      Container(
                        width: 28,
                        height: 28,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: AppColors.warning.withAlpha(51), // 20% of 255
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          '${widget.warningPartsCount}',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: AppColors.warning,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/replacement_history.dart';
import '../providers/spare_parts_providers.dart';
import 'history_card_widget.dart';

/// Groups replacement history entries by their date
class _HistoryGroup {
  final String title;
  final List<ReplacementHistory> entries;

  _HistoryGroup({required this.title, required this.entries});
}

/// Widget to display a list of replacement history entries
class HistoryListWidget extends StatelessWidget {
  final List<ReplacementHistory> historyEntries;
  final int sparePartId;

  const HistoryListWidget({
    super.key,
    required this.historyEntries,
    required this.sparePartId,
  });

  // Group history entries by date
  List<_HistoryGroup> _groupHistoryByDate(List<ReplacementHistory> entries) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final weekAgo = today.subtract(const Duration(days: 7));
    final monthAgo = DateTime(now.year, now.month - 1, now.day);

    final Map<String, List<ReplacementHistory>> groups = {};

    for (final entry in entries) {
      final entryDate = entry.replacementDate;
      final entryDay = DateTime(entryDate.year, entryDate.month, entryDate.day);
      
      String groupTitle;
      
      if (entryDay.isAtSameMomentAs(today)) {
        groupTitle = 'Today';
      } else if (entryDay.isAtSameMomentAs(yesterday)) {
        groupTitle = 'Yesterday';
      } else if (entryDay.isAfter(weekAgo)) {
        groupTitle = 'This Week';
      } else if (entryDay.isAfter(monthAgo)) {
        groupTitle = 'This Month';
      } else {
        groupTitle = DateFormat('MMMM yyyy').format(entryDate);
      }

      groups.putIfAbsent(groupTitle, () => []).add(entry);
    }

    return groups.entries
        .map((e) => _HistoryGroup(title: e.key, entries: e.value))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final container = ProviderScope.containerOf(context);
    final groupedHistory = _groupHistoryByDate(historyEntries);

    return RefreshIndicator(
      onRefresh: () async {
        container.refresh(replacementHistoryProvider(sparePartId));
      },
      child: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.only(top: 16, bottom: 24),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final group = groupedHistory[index];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 8,
                        ),
                        child: Text(
                          group.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ),
                      ...group.entries.map((entry) => Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            child: HistoryCardWidget(
                              history: entry,
                              index: historyEntries.indexOf(entry),
                            ),
                          )),
                      if (index < groupedHistory.length - 1)
                        const Divider(height: 32, thickness: 1),
                    ],
                  );
                },
                childCount: groupedHistory.length,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

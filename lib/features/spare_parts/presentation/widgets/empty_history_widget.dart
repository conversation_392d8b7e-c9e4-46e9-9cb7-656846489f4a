import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../providers/spare_parts_providers.dart';
import 'current_part_statistics_widget.dart';

/// Widget displayed when there is no replacement history for a spare part
class EmptyHistoryWidget extends ConsumerWidget {
  final int sparePartId;

  const EmptyHistoryWidget({super.key, required this.sparePartId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sparePartAsync = ref.watch(sparePartProvider(sparePartId));

    return sparePartAsync.when(
      data: (sparePart) {
        if (sparePart == null) {
          return const Center(child: Text('Spare part not found'));
        }

        final container = ProviderScope.containerOf(context);
        final theme = Theme.of(context);
        final isDark = theme.brightness == Brightness.dark;

        return RefreshIndicator(
          onRefresh: () async {
            container.refresh(replacementHistoryProvider(sparePartId));
          },
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              SliverFillRemaining(
                hasScrollBody: false,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32.0,
                        vertical: 24.0,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Illustration
                          Container(
                            width: 180,
                            height: 180,
                            margin: const EdgeInsets.only(bottom: 24),
                            decoration: BoxDecoration(
                              color: isDark
                                  ? theme.colorScheme.primary.withAlpha(26)
                                  : theme.colorScheme.primary.withAlpha(51),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.history_outlined,
                              size: 80,
                              color: theme.colorScheme.primary,
                            ),
                          ),

                          // Title
                          Text(
                            'No Replacement History',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 12),

                          // Description
                          Text(
                            'This part has not been replaced yet. Track your maintenance history by recording replacements to monitor part lifespan and maintenance intervals.',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.textTheme.bodySmall?.color?.withAlpha(204),
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 32),

                          // Primary Action Button
                          SizedBox(
                            width: double.infinity,
                            child: FilledButton.icon(
                              onPressed: () {
                                // TODO: Navigate to replace part screen
                                // Navigator.push(context, MaterialPageRoute(
                                //   builder: (context) => ReplaceSparePartScreen(
                                //     sparePartId: sparePartId,
                                //   ),
                                // ));
                              },
                              icon: const Icon(Icons.add_circle_outline, size: 20),
                              label: const Text('Record First Replacement'),
                              style: FilledButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Secondary Action
                          TextButton(
                            onPressed: () {
                              container.refresh(replacementHistoryProvider(sparePartId));
                            },
                            child: const Text('Refresh Data'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // Current part statistics at the bottom
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.only(
                    left: 24.0,
                    right: 24.0,
                    bottom: 32.0,
                    top: 16.0,
                  ),
                  child: CurrentPartStatisticsWidget(sparePart: sparePart),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Text(
          'Error loading spare part',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
        ),
      ),
    );
  }
}

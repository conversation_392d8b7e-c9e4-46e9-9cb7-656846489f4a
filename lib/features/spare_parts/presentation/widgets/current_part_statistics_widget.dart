import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/spare_part.dart';

/// Widget to display current part statistics in a clean, modern card layout
class CurrentPartStatisticsWidget extends StatelessWidget {
  final SparePart sparePart;

  const CurrentPartStatisticsWidget({super.key, required this.sparePart});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    // Calculate usage period
    final daysInUse = DateTime.now()
        .difference(sparePart.installationDate)
        .inDays;
    final usagePeriod = daysInUse <= 0
        ? 'Today'
        : DateFormatter.formatTimePeriod(daysInUse);

    // Format distance
    final distanceText = sparePart.usageMileage <= 0
        ? 'No distance yet'
        : '${sparePart.usageMileage.toStringAsFixed(0)} km';

    // Calculate usage percentage for progress indicator
    final usagePercent = (sparePart.usagePercent / 100).clamp(0.0, 1.0);

    // Determine color based on usage percentage
    Color progressColor;
    if (usagePercent < 0.7) {
      progressColor = AppColors.success;
    } else if (usagePercent < 0.9) {
      progressColor = AppColors.warning;
    } else {
      progressColor = AppColors.error;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.settings_outlined,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Current Part Statistics',
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),

          // Progress bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Usage Progress',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: colorScheme.onSurface.withAlpha(204),
                      ),
                    ),
                    Text(
                      '${sparePart.usagePercent.toStringAsFixed(0)}%',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: progressColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: usagePercent,
                    backgroundColor: colorScheme.surfaceContainer,
                    valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                    minHeight: 8,
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),

          // Stats grid
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Left column
                Expanded(
                  child: _buildStatItem(
                    context,
                    icon: Icons.speed_outlined,
                    title: 'Mileage',
                    value: distanceText,
                    color: colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 16),
                // Right column
                Expanded(
                  child: _buildStatItem(
                    context,
                    icon: Icons.calendar_today_outlined,
                    title: 'Usage Period',
                    value: usagePeriod,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          // Installation date and remaining mileage
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Installation Details',
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface.withAlpha(204),
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow(
                  context,
                  icon: Icons.event_outlined,
                  label: 'Installed on',
                  value: DateHelper.formatForDisplay(
                    sparePart.installationDate,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDetailRow(
                  context,
                  icon: Icons.speed_outlined,
                  label: 'Initial mileage',
                  value: '${sparePart.initialMileage} km',
                ),
                const SizedBox(height: 8),
                _buildDetailRow(
                  context,
                  icon: Icons.flag_outlined,
                  label: 'Mileage limit',
                  value: '${sparePart.mileageLimit} km',
                ),
                const SizedBox(height: 8),
                _buildDetailRow(
                  context,
                  icon: Icons.repeat_outlined,
                  label: 'Replacement count',
                  value: '${sparePart.replacementCount}',
                ),
              ],
            ),
          ),

          // View more button
          /*Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: 16,
              top: 8,
            ),
            child: TextButton(
              onPressed: () {
                // TODO: Navigate to detailed statistics
              },
              style: TextButton.styleFrom(
                foregroundColor: colorScheme.primary,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                backgroundColor: colorScheme.primary.withAlpha(13),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('View Full Statistics'),
                  SizedBox(width: 8),
                  Icon(Icons.arrow_forward, size: 16),
                ],
              ),
            ),
          ),*/
        ],
      ),
    );
  }

  // Helper method to build a stat item
  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(13),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            title,
            style: theme.textTheme.labelSmall?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(153),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build a detail row
  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(icon, size: 20, color: theme.colorScheme.onSurface.withAlpha(153)),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(153),
            ),
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:bidtrakr/core/theme/app_colors.dart';
import 'package:bidtrakr/features/more/presentation/widgets/menu_card.dart';
import 'package:bidtrakr/features/more/presentation/widgets/menu_item.dart';
import 'package:bidtrakr/features/more/presentation/widgets/menu_divider.dart';

class SparePartsOptionsBottomSheet extends StatelessWidget {
  final int sparePartId;
  final VoidCallback onViewHistory;
  final VoidCallback onReplacePart;
  final VoidCallback onEditPart;
  final VoidCallback onDeletePart;

  const SparePartsOptionsBottomSheet({
    super.key,
    required this.sparePartId,
    required this.onViewHistory,
    required this.onReplacePart,
    required this.onEditPart,
    required this.onDeletePart,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 4),
            height: 4,
            width: 40,
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(80),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Title
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              children: [
                /*const Icon(Icons.build, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),*/
                Text(
                  'Spare Part Options',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),

          const Divider(indent: 16, endIndent: 16),

          // Action buttons
          MenuCard(
            showBorder: false,
            children: [
              _buildActionButton(
                context: context,
                icon: Icons.history,
                iconColor: AppColors.primary,
                label: 'View Replacement History',
                description: 'See previous replacements for this part',
                onTap: () {
                  Navigator.pop(context);
                  onViewHistory();
                },
              ),
              const MenuDivider(),
              _buildActionButton(
                context: context,
                icon: Icons.swap_horiz,
                iconColor: AppColors.primary,
                label: 'Replace Part',
                description: 'Record a replacement for this part',
                onTap: () {
                  Navigator.pop(context);
                  onReplacePart();
                },
              ),
              const MenuDivider(),
              _buildActionButton(
                context: context,
                icon: Icons.edit,
                iconColor: AppColors.primary,
                label: 'Edit Part',
                description: 'Modify part details and settings',
                onTap: () {
                  Navigator.pop(context);
                  onEditPart();
                },
              ),
              const MenuDivider(),
              _buildActionButton(
                context: context,
                icon: Icons.delete,
                iconColor: AppColors.error,
                label: 'Delete Part',
                description: 'Remove this part from your records',
                isDestructive: true,
                onTap: () {
                  Navigator.pop(context);
                  onDeletePart();
                },
              ),
            ],
          ),

          // Cancel button
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey.shade200,
                  foregroundColor: Colors.black87,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String label,
    required String description,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);

    return DefaultTextStyle.merge(
      style: isDestructive
          ? theme.textTheme.titleSmall?.copyWith(
              color: AppColors.error,
              fontWeight: FontWeight.w600,
            )
          : null,
      child: DefaultTextStyle.merge(
        style: isDestructive
            ? theme.textTheme.bodySmall?.copyWith(
                color: AppColors.error.withAlpha(180),
              )
            : null,
        child: MenuItem(
          icon: icon,
          title: label,
          subtitle: description,
          iconColor: isDestructive ? AppColors.error : iconColor,
          onTap: onTap,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/spare_part.dart';

class SparePartCard extends StatelessWidget {
  final SparePart sparePart;
  final VoidCallback onTap;
  final VoidCallback? onLongPress;

  const SparePartCard({
    super.key,
    required this.sparePart,
    required this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final formattedDate = DateHelper.formatForDisplay(
      sparePart.installationDate,
    );
    final formattedPrice = Calculations.formatCurrency(sparePart.price);
    final usagePeriod = DateFormatter.formatTimePeriod(sparePart.daysInUse);
    // Remove clamp to allow values above 100%
    final usagePercent = sparePart.usagePercent / 100.0;

    // Helper method to get status information
    (String, Color, Color, IconData) getStatusInfo(
      double usagePercent,
      bool isWarning,
      bool isDark,
    ) {
      if (isWarning) {
        return (
          'Warning',
          AppColors.error,
          AppColors.error.withAlpha(20),
          Icons.warning_amber_rounded,
        );
      } else if (usagePercent < 0.5) {
        return (
          'Good',
          AppColors.success,
          AppColors.success.withAlpha(20),
          Icons.check_circle_outline,
        );
      } else if (usagePercent < 0.8) {
        return (
          'Fair',
          AppColors.warning,
          AppColors.warning.withAlpha(20),
          Icons.warning_outlined,
        );
      } else {
        return (
          'Bad',
          AppColors.error,
          AppColors.error.withAlpha(20),
          Icons.error_outline,
        );
      }
    }

    final statusInfo = getStatusInfo(
      usagePercent,
      sparePart.warningStatus,
      isDark,
    );
    final status = statusInfo.$1;
    final statusColor = statusInfo.$2;
    final statusBgColor = statusInfo.$3;
    final statusIcon = statusInfo.$4;

    // Calculate days until replacement is due (if applicable)
    // Ignoring the result as it's not currently used in the UI
    // final daysUntilReplacement = sparePart.mileageLimit > 0
    //     ? ((sparePart.mileageLimit - sparePart.usageMileage) /
    //             (sparePart.usageMileage / sparePart.daysInUse))
    //         .round()
    //     : 0;

    // Determine if part is due for replacement soon (within 30 days or 500km)
    // final isDueSoon = daysUntilReplacement > 0 &&
    //     (daysUntilReplacement <= 30 ||
    //         (sparePart.mileageLimit - sparePart.usageMileage) <= 500);

    // Status badge
    final statusBadge = Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: statusBgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusColor.withAlpha(77),
          width: 1,
        ), // 30% of 255
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, size: 14, color: statusColor),
          const SizedBox(width: 4),
          Text(
            status,
            style: theme.textTheme.labelSmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
        color: isDark ? theme.cardColor : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 5% of 255
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Status indicator strip
              Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 4,
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      bottomLeft: Radius.circular(16),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with part type and status
                    Row(
                      children: [
                        // Part type chip
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: isDark
                                ? theme.primaryColor.withAlpha(20)
                                : theme.primaryColor.withAlpha(10),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getIconForPartType(sparePart.partType),
                                size: 14,
                                color: theme.primaryColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                sparePart.partType.toUpperCase(),
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Spacer(),
                        statusBadge,
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Part name
                    Text(
                      sparePart.partName,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.grey[900],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 16),

                    // Info grid
                    Row(
                      children: [
                        _buildInfoItem(
                          context,
                          Icons.calendar_today_outlined,
                          'Installed',
                          formattedDate,
                        ),
                        const SizedBox(width: 16),
                        _buildInfoItem(
                          context,
                          Icons.attach_money_rounded,
                          'Price',
                          formattedPrice,
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    Row(
                      children: [
                        _buildInfoItem(
                          context,
                          Icons.speed_outlined,
                          'Mileage',
                          '${sparePart.usageMileage} / ${sparePart.mileageLimit} km',
                        ),
                        const SizedBox(width: 16),
                        _buildInfoItem(
                          context,
                          Icons.access_time_outlined,
                          'Age',
                          usagePeriod,
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Usage progress
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.analytics_outlined,
                                  size: 16,
                                  color: statusColor,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'Usage Status',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: isDark
                                        ? Colors.white70
                                        : Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                            Text(
                              '${sparePart.usagePercent.toStringAsFixed(1)}%',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: usagePercent > 1.0 ? 1.0 : usagePercent,
                          backgroundColor: Colors.grey.withAlpha(30),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            statusColor,
                          ),
                          minHeight: 6,
                          borderRadius: BorderRadius.circular(3),
                        ),
                        const SizedBox(height: 6),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${sparePart.usageMileage} km used',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                            Text(
                              '${sparePart.mileageLimit} km limit',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                      ],
                    ),

                    // Display notes if available
                    if (sparePart.notes.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Divider(height: 1),
                      const SizedBox(height: 8),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          const Icon(
                            Icons.note_rounded,
                            color: Colors.blue,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              sparePart.notes,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Warning status (if applicable)
                    if (sparePart.warningStatus) ...[
                      //const SizedBox(height: 16),
                      const Divider(height: 1),
                      const SizedBox(height: 12),
                      Row(
                        children: <Widget>[
                          const Icon(
                            Icons.warning_amber_rounded,
                            color: AppColors.error,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  'Replacement Recommended',
                                  style: Theme.of(context).textTheme.titleSmall
                                      ?.copyWith(
                                        color: AppColors.error,
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'This part has exceeded ${sparePart.usagePercent.toStringAsFixed(1)}% of its recommended usage limit',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: AppColors.error.withAlpha(200),
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                    ],

                    // Created and updated dates
                    const Divider(height: 1),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Created: ${DateHelper.formatForDisplay(sparePart.createdAt)}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: AppColors.textSecondary,
                                fontSize: 10,
                              ),
                        ),
                        Text(
                          'Updated: ${DateHelper.formatForDisplay(sparePart.updatedAt)}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: AppColors.textSecondary,
                                fontSize: 10,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    // Theme is accessed directly in the build method
    return Expanded(
      child: Builder(
        builder: (context) => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 14, color: AppColors.textSecondary),
            const SizedBox(width: 6),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: 11,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withAlpha(230) // ~90% opacity
                          : Colors.grey[800],
                      fontWeight: FontWeight.w500,
                      fontSize: 13,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get appropriate icon based on part type
  IconData _getIconForPartType(String partType) {
    switch (partType.toLowerCase()) {
      case 'oil':
        return Icons.oil_barrel;
      case 'filter':
        return Icons.filter_alt;
      case 'brake':
        return Icons.do_not_step; // Similar to brake symbol
      case 'tire':
      case 'tyre':
        return Icons.circle; // Represents a tire
      case 'battery':
        return Icons.battery_full;
      case 'belt':
        return Icons.line_style;
      case 'chain':
        return Icons.link;
      case 'spark plug':
        return Icons.electric_bolt;
      case 'suspension':
        return Icons.height;
      case 'transmission':
        return Icons.settings;
      case 'clutch':
        return Icons.adjust; // Represents clutch mechanism
      case 'cooling':
        return Icons.ac_unit;
      case 'electrical':
        return Icons.electrical_services;
      case 'fuel':
        return Icons.local_gas_station;
      case 'exhaust':
        return Icons.air;
      case 'steering':
        return Icons.control_camera; // Similar to steering control
      case 'light':
        return Icons.lightbulb;
      default:
        return Icons.build; // Default icon for other part types
    }
  }
}

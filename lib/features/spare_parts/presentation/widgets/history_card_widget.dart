import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/replacement_history.dart';

/// Widget to display a single replacement history card in a clean, modern card layout
class HistoryCardWidget extends StatelessWidget {
  final ReplacementHistory history;
  final int index;

  const HistoryCardWidget({
    super.key,
    required this.history,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    // Format dates and values
    final formattedReplacementDate = DateHelper.formatForDisplay(
      history.replacementDate,
    );
    final formattedMileage = '${history.mileageAtReplacement} km';
    final formattedPrice = Calculations.formatCurrency(history.price);

    // Format usage statistics
    final usagePeriod = history.usageDays <= 0
        ? 'Same day'
        : DateFormatter.formatTimePeriod(history.usageDays);

    final distanceTraveled = history.usageMileage <= 0
        ? 'No distance'
        : '${history.usageMileage} km';

    // Determine color based on index
    final Color cardColor = index == 0
        ? colorScheme.primary
        : colorScheme.onSurface.withAlpha(153);
    final Color iconColor = index == 0
        ? colorScheme.primary
        : colorScheme.onSurface.withAlpha(204);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: cardColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.history_outlined,
                    color: cardColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Replacement #${index + 1}',
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: cardColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    index == 0 ? 'Most Recent' : 'Previous',
                    style: textTheme.labelSmall?.copyWith(
                      color: cardColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Part info
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            history.partName,
                            style: textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            history.partType,
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurface.withAlpha(153),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      formattedPrice,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),

          // Stats grid
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Left column
                Expanded(
                  child: _buildStatItem(
                    context,
                    icon: Icons.calendar_month_outlined,
                    title: 'Replaced',
                    value: formattedReplacementDate,
                    color: iconColor,
                  ),
                ),
                const SizedBox(width: 16),
                // Right column
                Expanded(
                  child: _buildStatItem(
                    context,
                    icon: Icons.speed_outlined,
                    title: 'Mileage',
                    value: formattedMileage,
                    color: iconColor,
                  ),
                ),
              ],
            ),
          ),

          // Usage details
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Left column
                Expanded(
                  child: _buildStatItem(
                    context,
                    icon: Icons.access_time_outlined,
                    title: 'Used For',
                    value: usagePeriod,
                    color: iconColor,
                  ),
                ),
                const SizedBox(width: 16),
                // Right column
                Expanded(
                  child: _buildStatItem(
                    context,
                    icon: Icons.straighten_outlined,
                    title: 'Distance',
                    value: distanceTraveled,
                    color: iconColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Divider
          Divider(
            height: 1,
            color: Colors.grey.withAlpha(30),
            indent: 16,
            endIndent: 16,
          ),

          // Replacement Reason Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section Header
                Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20,
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withAlpha(204),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Replacement Reason',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withAlpha(204),
                        ),
                      ),
                    ],
                  ),
                ),

                // Reason Text
                Text(
                  history.replacementReason,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),

                // Notes if they exist
                if (history.notes.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Notes:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    history.notes,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],

                const SizedBox(height: 16),

                // Additional Information
                _buildDetailRow(
                  context,
                  'Replacement #',
                  history.replacementCount.toString(),
                  'Record Created',
                  DateHelper.formatTimestamp(history.createdAt),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build a stat item with icon, title and value
  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Text(
              title,
              style: textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  // Helper method to build detail rows in a single row
  Widget _buildDetailRow(
    BuildContext context,
    String label1,
    String value1,
    String label2,
    String value2,
  ) {
    final theme = Theme.of(context);
    final textStyle = theme.textTheme.bodySmall?.copyWith(
      color: AppColors.textSecondary,
      fontWeight: FontWeight.w500,
    );
    final valueStyle = theme.textTheme.bodyMedium?.copyWith(
      color: AppColors.textPrimary,
    );

    return Row(
      children: [
        // Left side (Replacement #)
        Row(
          children: [
            Text('$label1: ', style: textStyle),
            Text(value1, style: valueStyle),
          ],
        ),

        const Spacer(),

        // Right side (Record Created)
        Row(
          children: [
            Text('$label2: ', style: textStyle),
            Text(value2, style: valueStyle),
          ],
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class SparePartsEmptyView extends StatelessWidget {
  final VoidCallback onAddPressed;
  final Future<void> Function() onRefresh;

  const SparePartsEmptyView({
    super.key,
    required this.onAddPressed,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 32.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Animated illustration
                  Container(
                    height: 180,
                    margin: const EdgeInsets.only(bottom: 24),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Background circle
                        Container(
                          width: 160,
                          height: 160,
                          decoration: BoxDecoration(
                            color: isDark 
                                ? AppColors.primary.withAlpha(26)
                                : AppColors.primary.withAlpha(51), // 20% of 255
                            shape: BoxShape.circle,
                          ),
                        ),
                        // Icons animation
                        _buildAnimatedIcons(isDark),
                      ],
                    ),
                  ),
                  
                  // Title
                  Text(
                    'No Spare Parts Yet',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Description
                  Text(
                    'Keep track of your vehicle maintenance by adding spare parts. '
                    'Monitor their usage and get notified when maintenance is due.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.textTheme.bodySmall?.color?.withAlpha(204), // 80% of 255
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Primary Action Button
                  SizedBox(
                    width: double.infinity,
                    child: FilledButton.icon(
                      onPressed: onAddPressed,
                      icon: const Icon(Icons.add, size: 20),
                      label: const Text('Add Your First Spare Part'),
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Secondary Action
                  TextButton(
                    onPressed: onAddPressed,
                    child: const Text('Learn how to get started'),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Pull to refresh hint
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.arrow_downward,
                        size: 16,
                        color: theme.hintColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Pull down to refresh',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAnimatedIcons(bool isDark) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Rotating gear
        TweenAnimationBuilder(
          tween: Tween(begin: 0.0, end: 1.0),
          duration: const Duration(seconds: 10),
          builder: (context, value, _) {
            return Transform.rotate(
              angle: value * 6.28, // 360 degrees in radians
              child: Icon(
                Icons.settings,
                size: 40,
                color: isDark ? Colors.blueGrey[400] : Colors.blueGrey[600],
              ),
            );
          },
        ),
        
        // Main part icon
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isDark ? Colors.blueGrey[900] : Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 5% of 255
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            Icons.build,
            size: 32,
            color: AppColors.primary,
          ),
        ),
        
        // Small floating parts
        Positioned(
          right: 20,
          top: 30,
          child: _buildFloatingIcon(Icons.settings_input_component, 0, isDark),
        ),
        Positioned(
          left: 20,
          bottom: 30,
          child: _buildFloatingIcon(Icons.bolt, 1, isDark),
        ),
        Positioned(
          right: 10,
          bottom: 10,
          child: _buildFloatingIcon(Icons.engineering, 2, isDark),
        ),
      ],
    );
  }
  
  Widget _buildFloatingIcon(IconData icon, int index, bool isDark) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 800 + (index * 200)),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 4 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: isDark ? Colors.blueGrey[800] : Colors.grey[100],
                shape: BoxShape.circle,
                border: Border.all(
                  color: isDark ? Colors.blueGrey[600]! : Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                size: 16,
                color: isDark ? Colors.blueGrey[200] : Colors.blueGrey[600],
              ),
            ),
          ),
        );
      },
    );
  }
}

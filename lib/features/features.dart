/// Features barrel export file for the bidtrakr application
/// 
/// This file provides a single import point for all feature functionality:
/// ```dart
/// import 'package:bidtrakr/features/features.dart';
/// ```
library;

// Authentication feature
export 'auth/auth.dart';

// Income feature
export 'income/income.dart';

// Orders feature
export 'orders/orders.dart';

// Performance feature
export 'performance/performance.dart';

// Settings feature
export 'settings/settings.dart';

// Spare Parts feature
export 'spare_parts/spare_parts.dart';

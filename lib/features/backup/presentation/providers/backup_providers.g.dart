// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backup_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$backupServiceHash() => r'498ca8914aab21cd9d3a6eba62f9037618350aa1';

/// See also [backupService].
@ProviderFor(backupService)
final backupServiceProvider = AutoDisposeProvider<BackupService>.internal(
  backupService,
  name: r'backupServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$backupServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BackupServiceRef = AutoDisposeProviderRef<BackupService>;
String _$availableBackupsHash() => r'4e57155f4a356df89215bb75ef9d00fe1f81e15a';

/// See also [availableBackups].
@ProviderFor(availableBackups)
final availableBackupsProvider =
    AutoDisposeFutureProvider<List<BackupInfo>>.internal(
      availableBackups,
      name: r'availableBackupsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$availableBackupsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AvailableBackupsRef = AutoDisposeFutureProviderRef<List<BackupInfo>>;
String _$backupOperationHash() => r'c139f27da76a42bfe29ee7cfcc5abab7a8f23f70';

/// See also [BackupOperation].
@ProviderFor(BackupOperation)
final backupOperationProvider =
    AutoDisposeNotifierProvider<
      BackupOperation,
      AsyncValue<BackupInfo?>
    >.internal(
      BackupOperation.new,
      name: r'backupOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$backupOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BackupOperation = AutoDisposeNotifier<AsyncValue<BackupInfo?>>;
String _$restoreOperationHash() => r'6337fc37a077130a8a9cac593a5b6b59ace79b77';

/// See also [RestoreOperation].
@ProviderFor(RestoreOperation)
final restoreOperationProvider =
    AutoDisposeNotifierProvider<RestoreOperation, AsyncValue<bool>>.internal(
      RestoreOperation.new,
      name: r'restoreOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$restoreOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$RestoreOperation = AutoDisposeNotifier<AsyncValue<bool>>;
String _$deleteBackupOperationHash() =>
    r'088bf80be879f2121867faf26189edcb427a3e5b';

/// See also [DeleteBackupOperation].
@ProviderFor(DeleteBackupOperation)
final deleteBackupOperationProvider =
    AutoDisposeNotifierProvider<
      DeleteBackupOperation,
      AsyncValue<bool>
    >.internal(
      DeleteBackupOperation.new,
      name: r'deleteBackupOperationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$deleteBackupOperationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DeleteBackupOperation = AutoDisposeNotifier<AsyncValue<bool>>;
String _$backupDirectoryPathHash() =>
    r'd2e9ec2be8842ac1e0155179e925232120be4b66';

/// See also [BackupDirectoryPath].
@ProviderFor(BackupDirectoryPath)
final backupDirectoryPathProvider =
    AutoDisposeAsyncNotifierProvider<BackupDirectoryPath, String?>.internal(
      BackupDirectoryPath.new,
      name: r'backupDirectoryPathProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$backupDirectoryPathHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BackupDirectoryPath = AutoDisposeAsyncNotifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

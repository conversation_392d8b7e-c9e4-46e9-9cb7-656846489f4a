import 'package:flutter/material.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/providers/app_settings_provider.dart';
import '../../../../core/services/permission_service.dart';
import '../../../income/presentation/providers/income_providers.dart';
import '../../../levels/presentation/providers/level_providers.dart';
import '../../../orders/presentation/providers/order_providers.dart';
import '../../../performance/presentation/providers/performance_providers.dart';
import '../../../spare_parts/presentation/providers/spare_parts_providers.dart';
import '../../data/services/backup_service.dart';
import '../../domain/entities/backup_info.dart';

part 'backup_providers.g.dart';

// Provider for BackupService
@riverpod
BackupService backupService(Ref ref) {
  final database = ref.watch(databaseProvider);
  return BackupService(database: database);
}

// Provider for available backups
@riverpod
Future<List<BackupInfo>> availableBackups(Ref ref) async {
  final backupService = ref.watch(backupServiceProvider);
  return backupService.getAvailableBackups();
}

// Provider for backup operation state
@riverpod
class BackupOperation extends _$BackupOperation {
  @override
  AsyncValue<BackupInfo?> build() {
    return const AsyncValue.data(null);
  }

  Future<void> createBackup() async {
    state = const AsyncValue.loading();
    try {
      final backupService = ref.read(backupServiceProvider);

      // Check and request permissions first
      final hasPermissions = await backupService.checkAndRequestPermissions();
      if (!hasPermissions) {
        state = const AsyncValue.error(
          'Storage permission denied',
          StackTrace.empty,
        );
        return;
      }

      final backupInfo = await backupService.createBackup();
      if (backupInfo != null) {
        state = AsyncValue.data(backupInfo);
        // Refresh available backups list
        ref.invalidate(availableBackupsProvider);
      } else {
        state = const AsyncValue.error(
          'Failed to create backup',
          StackTrace.empty,
        );
      }
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
}

// Provider for restore operation state
@riverpod
class RestoreOperation extends _$RestoreOperation {
  @override
  AsyncValue<bool> build() {
    return const AsyncValue.data(false);
  }

  Future<void> restoreFromBackup(String backupPath) async {
    state = const AsyncValue.loading();
    try {
      final backupService = ref.read(backupServiceProvider);

      // Check and request permissions first
      final hasPermissions = await backupService.checkAndRequestPermissions();
      if (!hasPermissions) {
        state = const AsyncValue.error(
          'Storage permission denied',
          StackTrace.empty,
        );
        return;
      }

      final success = await backupService.restoreBackup(backupPath);

      if (success) {
        // Force recreation of the database connection
        await _recreateDatabaseConnection();

        // Refresh all data providers
        _refreshAllProviders();
      }

      state = AsyncValue.data(success);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> _recreateDatabaseConnection() async {
    try {
      // Instead of creating a new database instance, just invalidate the provider
      // This will force Riverpod to rebuild all dependent providers
      ref.invalidate(databaseProvider);

      // Wait a moment to allow the database to be properly invalidated
      await Future.delayed(const Duration(milliseconds: 100));

      // Access the provider to ensure it's using the new instance
      final db = ref.read(databaseProvider);

      // Verify the new connection works by running a simple query
      try {
        await db.customSelect('SELECT 1').get();
        debugPrint('Database connection successfully recreated');
      } catch (e) {
        debugPrint('Error verifying new database connection: $e');
        // Continue even if verification fails
      }
    } catch (e) {
      debugPrint('Error recreating database connection: $e');
      // Continue even if recreation fails
    }
  }

  void _refreshAllProviders() {
    debugPrint('Refreshing all data providers after database restore');

    try {
      // Optimized: Group related providers and invalidate in logical order
      // This reduces the chance of dependency issues and improves performance

      // 1. Core system providers first
      ref.invalidate(appSettingsNotifierProvider);
      ref.invalidate(databaseProvider); // Ensure database connection is fresh

      // 2. Backup-related providers
      ref.invalidate(availableBackupsProvider);
      ref.invalidate(backupDirectoryPathProvider);

      // 3. Data providers - invalidate individually with error handling
      try {
        // Income providers
        ref.invalidate(incomeListProvider);
      } catch (e) {
        debugPrint('Warning: Could not invalidate incomeListProvider: $e');
      }

      try {
        // Order providers
        ref.invalidate(orderListProvider);
      } catch (e) {
        debugPrint('Warning: Could not invalidate orderListProvider: $e');
      }

      try {
        // Performance providers
        ref.invalidate(performanceListProvider);
      } catch (e) {
        debugPrint('Warning: Could not invalidate performanceListProvider: $e');
      }

      try {
        // Spare parts providers
        ref.invalidate(sparePartsListProvider);
      } catch (e) {
        debugPrint('Warning: Could not invalidate sparePartsListProvider: $e');
      }

      try {
        // Level providers
        ref.invalidate(levelRequirementsProvider);
      } catch (e) {
        debugPrint('Info: levelRequirementsProvider not available: $e');
      }

      try {
        // Date range provider
        ref.invalidate(persistedDateRangeProvider);
      } catch (e) {
        debugPrint('Info: persistedDateRangeProvider not available: $e');
      }

      // 4. Repository providers (if they exist)
      try {
        ref.invalidate(incomeRepositoryProvider);
      } catch (e) {
        debugPrint('Info: incomeRepositoryProvider not available: $e');
      }

      try {
        ref.invalidate(orderRepositoryProvider);
      } catch (e) {
        debugPrint('Info: orderRepositoryProvider not available: $e');
      }

      try {
        ref.invalidate(performanceRepositoryProvider);
      } catch (e) {
        debugPrint('Info: performanceRepositoryProvider not available: $e');
      }

      debugPrint('All data providers refreshed successfully');
    } catch (e) {
      debugPrint('Error refreshing providers: $e');
    }
  }
}

// Provider for delete operation state
@riverpod
class DeleteBackupOperation extends _$DeleteBackupOperation {
  @override
  AsyncValue<bool> build() {
    return const AsyncValue.data(false);
  }

  Future<void> deleteBackup(String backupPath) async {
    state = const AsyncValue.loading();
    try {
      final backupService = ref.read(backupServiceProvider);
      final success = await backupService.deleteBackup(backupPath);
      state = AsyncValue.data(success);

      // Refresh available backups list
      if (success) {
        ref.invalidate(availableBackupsProvider);
      }
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
}

// Provider for backup directory path
@riverpod
class BackupDirectoryPath extends _$BackupDirectoryPath {
  @override
  Future<String?> build() async {
    final backupService = ref.watch(backupServiceProvider);
    return backupService.getCustomBackupDirectory();
  }

  Future<bool> setBackupDirectory() async {
    final backupService = ref.read(backupServiceProvider);

    // Check and request permissions first
    final hasPermissions = await backupService.checkAndRequestPermissions();
    if (!hasPermissions) {
      return false;
    }

    // Request directory access using Storage Access Framework
    final directoryPath = await PermissionService.requestDirectoryAccess();
    if (directoryPath == null) {
      return false;
    }

    // Check if we can write to this directory
    final canWrite = await PermissionService.canWriteToDirectory(directoryPath);
    if (!canWrite) {
      return false;
    }

    final success = await backupService.setCustomBackupDirectory(directoryPath);
    if (success) {
      ref.invalidateSelf();
    }
    return success;
  }

  Future<void> clearBackupDirectory() async {
    final backupService = ref.read(backupServiceProvider);
    final success = await backupService.clearCustomBackupDirectory();
    if (success) {
      ref.invalidateSelf();
    }
  }
}

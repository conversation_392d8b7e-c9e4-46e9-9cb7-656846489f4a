/// Income feature barrel export file
///
/// This file provides a single import point for all income functionality:
/// ```dart
/// import 'package:bidtrakr/features/income/income.dart';
/// ```
library;

// Data layer
export 'data/repositories/income_repository_impl.dart';

// Domain layer
export 'domain/entities/income.dart';
export 'domain/repositories/income_repository.dart';
export 'domain/use_cases/calculate_net_income.dart';
export 'domain/use_cases/get_income_history.dart';

// Presentation layer
export 'presentation/screens/income_screen.dart';
export 'presentation/screens/income_form_screen.dart';
export 'presentation/providers/income_providers.dart';
export 'presentation/widgets/balance_card.dart';
export 'presentation/widgets/income_details_sheet.dart';
export 'presentation/widgets/income_history_header.dart';
export 'presentation/widgets/income_item_card.dart';
export 'presentation/widgets/income_shimmer_loading.dart';
export 'presentation/widgets/income_summary.dart' hide IncomeSummary;
export 'presentation/widgets/income_trends_card.dart';
export 'presentation/widgets/income_unified_shimmer_loading.dart'
    hide IncomeListShimmerLoading;
export 'presentation/widgets/mileage_summary.dart';

import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart' as db;
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/datasources/converters/sync_status_converter.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/repositories/base_repository.dart';
import '../../../../core/services/calculation/income_calculation_service.dart';

import '../../domain/entities/income.dart';
import '../../domain/repositories/income_repository.dart';

class IncomeRepositoryImpl
    extends BaseDateRangeRepository<Income, db.IncomeData, db.IncomeCompanion>
    implements IncomeRepository {
  final IncomeCalculationService calculationService;

  IncomeRepositoryImpl({
    required super.database,
    required this.calculationService,
    required super.syncService,
  });

  // Implement abstract methods from BaseRepository

  @override
  db.IncomeCompanion mapToCompanion(Income entity) {
    return db.IncomeCompanion(
      id: entity.id == null ? const db.Value.absent() : db.Value(entity.id!),
      uuid: db.Value(const Uuid().v4()),
      date: db.Value(entity.date),
      initialMileage: db.Value(entity.initialMileage),
      finalMileage: db.Value(entity.finalMileage),
      initialGopay: db.Value(entity.initialGopay),
      initialBca: db.Value(entity.initialBca),
      initialCash: db.Value(entity.initialCash),
      initialOvo: db.Value(entity.initialOvo),
      initialBri: db.Value(entity.initialBri),
      initialRekpon: db.Value(entity.initialRekpon),
      finalGopay: db.Value(entity.finalGopay),
      finalBca: db.Value(entity.finalBca),
      finalCash: db.Value(entity.finalCash),
      finalOvo: db.Value(entity.finalOvo),
      finalBri: db.Value(entity.finalBri),
      finalRekpon: db.Value(entity.finalRekpon),
      initialCapital: db.Value(entity.initialCapital),
      finalResult: db.Value(entity.finalResult),
      mileage: db.Value(entity.mileage),
      netIncome: db.Value(entity.netIncome),
      createdAt: db.Value(DateTime.now()),
      updatedAt: db.Value(DateTime.now()),
      syncStatus: const db.Value(SyncStatus.pendingUpload),
    );
  }

  @override
  Income mapFromData(db.IncomeData data) {
    return Income(
      id: data.id,
      date: data.date,
      initialMileage: data.initialMileage,
      finalMileage: data.finalMileage,
      initialGopay: data.initialGopay,
      initialBca: data.initialBca,
      initialCash: data.initialCash,
      initialOvo: data.initialOvo,
      initialBri: data.initialBri,
      initialRekpon: data.initialRekpon,
      finalGopay: data.finalGopay,
      finalBca: data.finalBca,
      finalCash: data.finalCash,
      finalOvo: data.finalOvo,
      finalBri: data.finalBri,
      finalRekpon: data.finalRekpon,
      initialCapital: data.initialCapital,
      finalResult: data.finalResult,
      mileage: data.mileage,
      netIncome: data.netIncome,
    );
  }

  @override
  Future<int> insertEntity(db.IncomeCompanion companion) async {
    final id = await database.into(database.income).insert(companion);
    // Update spare parts mileage with the latest mileage
    final incomeData = await getDataById(id);
    if (incomeData != null) {
      final income = mapFromData(incomeData);
      await database.updateSparePartsMileage(income.finalMileage);
    }
    return id;
  }

  @override
  Future<bool> updateEntity(Income entity) async {
    final affectedRows = await (database.update(
      database.income,
    )..where((tbl) => tbl.id.equals(entity.id!))).write(mapToCompanion(entity));
    return affectedRows > 0;
  }

  @override
  Future<bool> deleteEntity(int id) async {
    // Use soft delete instead of hard delete
    final affectedRows =
        await (database.update(
          database.income,
        )..where((tbl) => tbl.id.equals(id))).write(
          db.IncomeCompanion(
            deletedAt: db.Value(DateTime.now()),
            syncStatus: const db.Value(SyncStatus.pendingUpload),
          ),
        );
    return affectedRows > 0;
  }

  @override
  Future<List<db.IncomeData>> getAllData() async {
    final query = database.select(database.income)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..orderBy([
        (t) => db.OrderingTerm(expression: t.date, mode: db.OrderingMode.desc),
      ]);
    return query.get();
  }

  @override
  Future<db.IncomeData?> getDataById(int id) async {
    final query = database.select(database.income)
      ..where((tbl) => tbl.id.equals(id));
    return query.getSingleOrNull();
  }

  @override
  Future<List<db.IncomeData>> getUnsyncedData() async {
    return database.getUnsyncedIncome();
  }

  @override
  Future<void> markDataAsSynced(String uuid) async {
    await database.markIncomeAsSynced(uuid);
  }

  Income _copyEntityWithId(Income entity, int id) {
    return entity.copyWith(id: id);
  }

  // Implement BaseDateRangeRepository abstract methods

  @override
  Future<List<db.IncomeData>> getDataForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    final query = database.select(database.income)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..where((tbl) => tbl.date.isBetweenValues(start, end))
      ..orderBy([
        (t) => db.OrderingTerm(expression: t.date, mode: db.OrderingMode.desc),
      ]);
    return query.get();
  }

  @override
  Future<bool> checkDataDateExists(DateTime date, {int? excludeId}) async {
    var query = database.select(database.income)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..where((tbl) => tbl.date.equals(date));

    if (excludeId != null) {
      query = query..where((tbl) => tbl.id.equals(excludeId).not());
    }

    final result = await query.getSingleOrNull();
    return result != null;
  }

  // Override save method to include calculation logic
  @override
  Future<Either<Failure, Income>> save(Income entity) async {
    return executeWithErrorHandling<int, Income>(() async {
      // Calculate all derived fields using the calculation service
      final calculatedIncomeResult = calculationService.calculateIncome(entity);

      return calculatedIncomeResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedIncome) async {
          final companion = mapToCompanion(calculatedIncome);
          final id = await insertEntity(companion);
          return id;
        },
      );
    }, (id) => _copyEntityWithId(entity, id));
  }

  // Override update method to include calculation logic
  @override
  Future<Either<Failure, Income>> update(Income entity) async {
    return executeWithErrorHandling<bool, Income>(() async {
      if (entity.id == null) {
        throw Exception('Income ID cannot be null for update');
      }

      // Calculate all derived fields using the calculation service
      final calculatedIncomeResult = calculationService.calculateIncome(entity);

      return calculatedIncomeResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedIncome) async {
          final success = await updateEntity(calculatedIncome);
          if (!success) {
            throw Exception('Update failed');
          }
          return success;
        },
      );
    }, (success) => entity);
  }

  // Implement income-specific methods that are not handled by BaseRepository

  @override
  Future<Either<Failure, List<Income>>> getPaginated({
    required int page,
    required int pageSize,
    DateTime? start,
    DateTime? end,
  }) async {
    return executeWithErrorHandling<List<db.IncomeData>, List<Income>>(
      () async {
        // Create a query for income records
        final query = database.select(database.income)
          ..where((tbl) => tbl.deletedAt.isNull())
          ..orderBy([(tbl) => db.OrderingTerm.desc(tbl.date)]);

        // Add date range filter if provided
        if (start != null && end != null) {
          query.where((tbl) => tbl.date.isBetweenValues(start, end));
        }

        // Calculate offset based on page number (1-based) and page size
        final offset = (page - 1) * pageSize;

        // Apply pagination
        query.limit(pageSize, offset: offset);

        // Execute the query
        return query.get();
      },
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }

  @override
  Future<Either<Failure, Income?>> getLatestIncome() async {
    return executeWithErrorHandling<db.IncomeData?, Income?>(() async {
      final query = database.select(database.income)
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => db.OrderingTerm.desc(tbl.date)])
        ..limit(1);

      return query.getSingleOrNull();
    }, (data) => data != null ? mapFromData(data) : null);
  }

  @override
  Future<Either<Failure, int>> getHighestMileage() async {
    return executeWithErrorHandling<db.QueryRow?, int>(
      () async {
        // Use a custom query to get the maximum final_mileage value from non-deleted records
        return database
            .customSelect(
              'SELECT MAX(final_mileage) as maxMileage FROM income WHERE deleted_at IS NULL',
            )
            .getSingleOrNull();
      },
      (result) {
        if (result == null || result.data['maxMileage'] == null) {
          return 0; // Default to 0 if no records found
        }
        return result.data['maxMileage'] as int;
      },
    );
  }

  // ISoftDeleteRepository implementation

  @override
  Future<Either<Failure, bool>> softDelete(int id) async {
    return delete(id); // Use the inherited delete method which does soft delete
  }

  @override
  Future<Either<Failure, List<Income>>> getAllIncludingDeleted() async {
    return executeWithErrorHandling<List<db.IncomeData>, List<Income>>(
      () async {
        // Get all records including soft-deleted ones
        final query = database.select(database.income)
          ..orderBy([(tbl) => db.OrderingTerm.desc(tbl.date)]);
        return query.get();
      },
      (dataList) => dataList.map((data) => mapFromData(data)).toList(),
    );
  }

  @override
  Future<Either<Failure, bool>> restore(int id) async {
    return executeWithErrorHandling<bool, bool>(() async {
      final affectedRows =
          await (database.update(
            database.income,
          )..where((tbl) => tbl.id.equals(id))).write(
            db.IncomeCompanion(
              deletedAt: const db.Value(null),
              syncStatus: const db.Value(SyncStatus.pendingUpload),
            ),
          );
      return affectedRows > 0;
    }, (success) => success);
  }
}

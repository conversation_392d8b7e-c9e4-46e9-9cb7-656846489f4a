import 'package:flutter/material.dart';

import '../../../../core/utils/date_helper.dart';
import '../../../../core/widgets/app_delete_confirmation_dialog.dart';
import '../../../../core/widgets/item_actions_bottom_sheet.dart';
import '../../domain/entities/income.dart';
import 'income_details_sheet.dart';

/// Utility class for handling income-related actions
class IncomeActionHandlers {
  /// Show actions bottom sheet for income record
  static void showActionsBottomSheet({
    required BuildContext context,
    required Income income,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    ItemActionsBottomSheet.show(
      context: context,
      title: 'Income Record',
      subtitle: 'Date: ${DateHelper.formatForDisplay(income.date)}',
      onEdit: onEdit,
      onDelete: onDelete,
      itemIcon: Icons.account_balance_wallet,
    );
  }

  /// Show delete confirmation dialog for income record
  static Future<void> showDeleteConfirmation({
    required BuildContext context,
    required Income income,
    required Future<void> Function(Income) onConfirmedDelete,
  }) async {
    final confirmed = await AppDeleteConfirmationDialog.showForRecord(
      context: context,
      recordType: 'Income Record',
      recordIdentifier: DateHelper.formatForDisplay(income.date),
    );

    if (confirmed == true) {
      await onConfirmedDelete(income);
    }
  }

  /// Show income details modal bottom sheet
  static void showIncomeDetails({
    required BuildContext context,
    required Income income,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => IncomeDetailsSheet(income: income),
    );
  }
}

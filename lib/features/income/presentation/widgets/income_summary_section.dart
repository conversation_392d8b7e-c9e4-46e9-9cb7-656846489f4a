import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../providers/income_providers.dart' as providers;

/// Summary section widget for income screen with optimized rebuilds
class IncomeSummarySection extends ConsumerWidget {
  final String entityName;
  final AsyncValue<providers.IncomeSummary> summaryAsync;
  final Widget Function(providers.IncomeSummary) buildSummaryContent;
  final Widget Function(Object) buildErrorContainer;

  const IncomeSummarySection({
    super.key,
    required this.entityName,
    required this.summaryAsync,
    required this.buildSummaryContent,
    required this.buildErrorContainer,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Summary',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Text(
                'Overview of your ${entityName.toLowerCase()} performance and data',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
            summaryAsync.maybeWhen(
              data: (summary) => buildSummaryContent(summary),
              error: (error, stack) => buildErrorContainer(error),
              orElse: () => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }
}

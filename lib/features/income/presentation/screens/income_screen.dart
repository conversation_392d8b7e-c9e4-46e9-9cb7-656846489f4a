import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/models/paginated_result.dart';
import '../../../../core/presentation/screens/base_crud_screen.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/pagination_handler.dart';
import '../../domain/entities/income.dart';
import '../providers/income_providers.dart' as providers;
import '../widgets/income_action_handlers.dart';
import '../widgets/income_item_card.dart';
import '../widgets/income_summary.dart';
import '../widgets/income_trends_card.dart';
import '../widgets/income_unified_shimmer_loading.dart';
import '../widgets/mileage_summary.dart';
import 'income_form_screen.dart';

/// Screen that displays income records and summary information
class IncomeScreen extends BaseCrudScreen<Income, providers.IncomeSummary> {
  const IncomeScreen({super.key});

  @override
  IncomeScreenState createState() => IncomeScreenState();
}

class IncomeScreenState
    extends BaseCrudScreenState<Income, providers.IncomeSummary, IncomeScreen>
    with PaginationMixin<IncomeScreen> {
  // Abstract properties implementation
  @override
  String get screenTitle => 'Income';

  @override
  String get addButtonTooltip => 'Add new income record';

  @override
  IconData get entityIcon => Icons.account_balance_wallet;

  @override
  String get entityName => 'Income Record';

  // Pagination support
  @override
  bool get usesPagination => true;

  @override
  AsyncValue<PaginatedResult<Income>>? get paginatedEntityListAsync =>
      ref.watch(providers.paginatedIncomeListProvider);

  @override
  void onLoadNextPage() {
    ref.read(providers.paginatedIncomeListProvider.notifier).loadNextPage();
  }

  // Abstract methods implementation (kept for backward compatibility with summary section)
  @override
  AsyncValue<List<Income>> get entityListAsync {
    // For the base class, we need to provide a list, but Income uses pagination
    // We'll use the paginated result and extract the list
    final paginatedAsync = ref.watch(providers.paginatedIncomeListProvider);
    return paginatedAsync.when(
      data: (paginatedResult) => AsyncValue.data(paginatedResult.items),
      loading: () => const AsyncValue.loading(),
      error: (error, stack) => AsyncValue.error(error, stack),
    );
  }

  @override
  AsyncValue<providers.IncomeSummary> get summaryAsync {
    final summary = ref.watch(providers.incomeSummaryProvider);
    return AsyncValue.data(summary);
  }

  @override
  Widget buildListItem(Income entity) {
    return IncomeItemCard(
      income: entity,
      onTap: () => _showIncomeDetails(context, entity),
      onLongPress: () => _showActionsBottomSheet(context, entity),
    );
  }

  @override
  void navigateToForm({Income? entity}) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => IncomeFormScreen(income: entity),
          ),
        )
        .then((_) {
          // Refresh both lists when returning from the form screen
          ref.invalidate(providers.incomeListProvider);
          ref.read(providers.paginatedIncomeListProvider.notifier).refresh();
        });
  }

  @override
  Future<void> deleteEntity(Income entity) async {
    if (entity.id == null) return;

    // Show loading indicator
    SnackbarUtils.showLoading(message: 'Deleting...');

    // Use the provider to delete the income record
    final success = await ref
        .read(providers.incomeListProvider.notifier)
        .deleteIncome(entity.id!);

    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess('Income record deleted successfully');

        // Optimized: Use targeted refresh instead of full invalidation
        // This preserves pagination state and only updates the data
        await ref
            .read(providers.paginatedIncomeListProvider.notifier)
            .refresh();

        // Only invalidate summary if it's likely to be affected
        // (e.g., if this was a significant income record)
        // For now, we'll always invalidate summary as it's lightweight
        ref.invalidate(providers.incomeSummaryProvider);
      } else {
        SnackbarUtils.showError('Failed to delete income record');
      }
    }
  }

  @override
  Widget buildSummarySection(providers.IncomeSummary summary) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Income summary card
        IncomeSummary(
          totalIncome: summary.totalNetIncome,
          recordCount: summary.recordCount,
          totalRecordCount: summary.totalRecordCount,
          highestIncome: summary.highestIncome,
        ),
        const SizedBox(height: 16),

        // 2. Mileage summary card
        MileageSummary(
          totalMileage: summary.totalMileage,
          recordCount: summary.recordCount,
        ),
        const SizedBox(height: 16),

        // 3. Income trends card
        Consumer(
          builder: (context, ref, child) {
            final filteredIncomeAsync = ref.watch(
              providers.filteredIncomeListProvider,
            );
            return filteredIncomeAsync.when(
              data: (incomeRecords) =>
                  IncomeTrendsCard(incomeRecords: incomeRecords),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => buildErrorContainer(error),
            );
          },
        ),
      ],
    );
  }

  @override
  Widget buildLoadingState() {
    return const IncomeUnifiedShimmerLoading();
  }

  @override
  void refreshData() {
    // Optimized: Only invalidate income-related providers, not all providers
    // Use batch invalidation to reduce overhead
    ref.invalidate(providers.incomeListProvider);

    // Use provider's built-in refresh method instead of invalidation
    // This is more efficient as it doesn't recreate the provider
    ref.read(providers.paginatedIncomeListProvider.notifier).refresh();
  }

  // Helper methods for Income-specific functionality

  void _showActionsBottomSheet(BuildContext context, Income income) {
    IncomeActionHandlers.showActionsBottomSheet(
      context: context,
      income: income,
      onEdit: () => navigateToForm(entity: income),
      onDelete: () => _showDeleteConfirmation(context, income),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Income income) async {
    await IncomeActionHandlers.showDeleteConfirmation(
      context: context,
      income: income,
      onConfirmedDelete: deleteEntity,
    );
  }

  void _showIncomeDetails(BuildContext context, Income income) {
    IncomeActionHandlers.showIncomeDetails(context: context, income: income);
  }
}

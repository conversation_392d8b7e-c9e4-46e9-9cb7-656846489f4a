import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/order.dart' as domain;

abstract class OrderRepository {
  /// Get all order records
  Future<Either<Failure, List<domain.Order>>> getAllOrders();

  /// Get order by id
  Future<Either<Failure, domain.Order>> getOrderById(int id);

  /// Save a new order
  Future<Either<Failure, domain.Order>> saveOrder(domain.Order order);

  /// Update an existing order
  Future<Either<Failure, domain.Order>> updateOrder(domain.Order order);

  /// Delete an order
  Future<Either<Failure, bool>> deleteOrder(int id);

  /// Get orders for a specific date range
  Future<Either<Failure, List<domain.Order>>> getOrdersForDateRange(
    DateTime start,
    DateTime end,
  );

  /// Get orders for performance calculation (last 14 days)
  Future<Either<Failure, List<domain.Order>>>
  getOrdersForPerformanceCalculation(DateTime endDate);

  /// Get paginated order records
  /// [page] is the page number (1-based)
  /// [pageSize] is the number of records per page
  Future<Either<Failure, List<domain.Order>>> getPaginated({
    required int page,
    required int pageSize,
    DateTime? start,
    DateTime? end,
  });

  /// Get total completed orders for the last 14 days (excluding current day)
  Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(
    DateTime endDate,
  );

  /// Get total points for a month
  Future<Either<Failure, int>> getTotalPointsForMonth(int year, int month);

  /// Check if an order record already exists for a specific date
  Future<Either<Failure, bool>> checkDateExists(
    DateTime date, {
    int? excludeId,
  });
}

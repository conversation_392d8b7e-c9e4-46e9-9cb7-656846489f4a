import 'package:dartz/dartz.dart' hide Order;

import '../../../../core/errors/failures.dart';
import '../entities/order.dart' as domain;
import '../repositories/order_repository.dart';

/// Use case to retrieve order summary data
class GetOrderSummary {
  final OrderRepository repository;

  GetOrderSummary(this.repository);

  /// Get all orders
  Future<Either<Failure, List<domain.Order>>> getAll() async {
    return await repository.getAllOrders();
  }

  /// Get orders for a specific date range
  Future<Either<Failure, List<domain.Order>>> getForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    return await repository.getOrdersForDateRange(start, end);
  }

  /// Get orders for performance calculation (last 14 days)
  Future<Either<Failure, List<domain.Order>>> getForPerformanceCalculation(
    DateTime endDate,
  ) async {
    return await repository.getOrdersForPerformanceCalculation(endDate);
  }

  /// Get paginated orders
  /// [page] is the page number (1-based)
  /// [pageSize] is the number of records per page
  Future<Either<Failure, List<domain.Order>>> getPaginated({
    required int page,
    required int pageSize,
    DateTime? start,
    DateTime? end,
  }) async {
    return await repository.getPaginated(
      page: page,
      pageSize: pageSize,
      start: start,
      end: end,
    );
  }

  /// Get a specific order by id
  Future<Either<Failure, domain.Order>> getById(int id) async {
    return await repository.getOrderById(id);
  }

  /// Get total points for a month (used for level calculations)
  Future<Either<Failure, int>> getTotalPointsForMonth(
    int year,
    int month,
  ) async {
    return await repository.getTotalPointsForMonth(year, month);
  }

  /// Calculate summary metrics for a list of orders
  Either<Failure, OrderSummary> calculateSummary(List<domain.Order> orders) {
    try {
      if (orders.isEmpty) {
        return Right(OrderSummary.empty());
      }

      int totalIncomingOrders = 0;
      int totalOrdersReceived = 0;
      int totalOrdersCompleted = 0;
      int totalOrdersMissed = 0;
      int totalOrdersCanceled = 0;
      int totalCbsOrders = 0;
      int totalPoints = 0;
      double totalTrip = 0;
      double totalBonus = 0;
      double totalTips = 0;
      double totalIncome = 0;

      for (final order in orders) {
        totalIncomingOrders += (order.incomingOrder ?? 0).toInt();
        totalOrdersReceived += (order.orderReceived ?? 0).toInt();
        totalOrdersCompleted += order.orderCompleted.toInt();
        totalOrdersMissed += order.orderMissed.toInt();
        totalOrdersCanceled += order.orderCanceled.toInt();
        totalCbsOrders += order.cbsOrder.toInt();
        totalPoints += order.points.toInt();
        totalTrip += order.trip;
        totalBonus += order.bonus;
        totalTips += order.tips;
        totalIncome += (order.income ?? 0).toInt();
      }

      // Calculate average rates
      final avgBidAcceptance = totalIncomingOrders > 0
          ? (totalOrdersReceived + totalCbsOrders) / totalIncomingOrders
          : 0.0;

      final avgTripCompletion = totalOrdersReceived > 0
          ? totalOrdersCompleted / totalOrdersReceived
          : 0.0;

      return Right(
        OrderSummary(
          totalIncomingOrders: totalIncomingOrders,
          totalOrdersReceived: totalOrdersReceived,
          totalOrdersCompleted: totalOrdersCompleted,
          totalOrdersMissed: totalOrdersMissed,
          totalOrdersCanceled: totalOrdersCanceled,
          totalCbsOrders: totalCbsOrders,
          totalPoints: totalPoints,
          totalTrip: totalTrip,
          totalBonus: totalBonus,
          totalTips: totalTips,
          totalIncome: totalIncome,
          avgBidAcceptance: avgBidAcceptance,
          avgTripCompletion: avgTripCompletion,
          recordCount: orders.length,
        ),
      );
    } catch (e) {
      return Left(
        Failure.businessLogic(message: 'Error calculating order summary: $e'),
      );
    }
  }
}

/// Data class to hold order summary information
class OrderSummary {
  final int totalIncomingOrders;
  final int totalOrdersReceived;
  final int totalOrdersCompleted;
  final int totalOrdersMissed;
  final int totalOrdersCanceled;
  final int totalCbsOrders;
  final int totalPoints;
  final double totalTrip;
  final double totalBonus;
  final double totalTips;
  final double totalIncome;
  final double avgBidAcceptance;
  final double avgTripCompletion;
  final int recordCount;

  OrderSummary({
    required this.totalIncomingOrders,
    required this.totalOrdersReceived,
    required this.totalOrdersCompleted,
    required this.totalOrdersMissed,
    required this.totalOrdersCanceled,
    required this.totalCbsOrders,
    required this.totalPoints,
    required this.totalTrip,
    required this.totalBonus,
    required this.totalTips,
    required this.totalIncome,
    required this.avgBidAcceptance,
    required this.avgTripCompletion,
    required this.recordCount,
  });

  factory OrderSummary.empty() {
    return OrderSummary(
      totalIncomingOrders: 0,
      totalOrdersReceived: 0,
      totalOrdersCompleted: 0,
      totalOrdersMissed: 0,
      totalOrdersCanceled: 0,
      totalCbsOrders: 0,
      totalPoints: 0,
      totalTrip: 0,
      totalBonus: 0,
      totalTips: 0,
      totalIncome: 0,
      avgBidAcceptance: 0,
      avgTripCompletion: 0,
      recordCount: 0,
    );
  }
}

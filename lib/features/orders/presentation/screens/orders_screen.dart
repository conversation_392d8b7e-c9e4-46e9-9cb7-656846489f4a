import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/models/paginated_result.dart';
import '../../../../core/presentation/screens/base_crud_screen.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/app_delete_confirmation_dialog.dart';
import '../../../../core/widgets/item_actions_bottom_sheet.dart';
import '../../../../core/widgets/pagination_handler.dart';
import '../../domain/entities/order.dart';
import '../../domain/use_cases/get_order_summary.dart';
import '../providers/order_providers.dart';
import '../widgets/order_details_bottom_sheet.dart';
import '../widgets/order_list_item.dart';
import '../widgets/order_metrics.dart';
import '../widgets/order_standardized_shimmer_loading.dart';
import '../widgets/order_trends_card.dart';
import 'order_form_screen.dart';

class OrdersScreen extends BaseCrudScreen<Order, OrderSummary> {
  const OrdersScreen({super.key});

  @override
  OrdersScreenState createState() => OrdersScreenState();
}

class OrdersScreenState
    extends BaseCrudScreenState<Order, OrderSummary, OrdersScreen>
    with PaginationMixin<OrdersScreen> {
  // Abstract properties implementation
  @override
  String get screenTitle => 'Orders';

  @override
  String get addButtonTooltip => 'Add new order';

  @override
  IconData get entityIcon => Icons.assignment;

  @override
  String get entityName => 'Order Record';

  // Pagination support
  @override
  bool get usesPagination => true;

  @override
  AsyncValue<PaginatedResult<Order>>? get paginatedEntityListAsync =>
      ref.watch(paginatedOrderListProvider);

  @override
  void onLoadNextPage() {
    ref.read(paginatedOrderListProvider.notifier).loadNextPage();
  }

  // Abstract methods implementation (kept for backward compatibility with summary section)
  @override
  AsyncValue<List<Order>> get entityListAsync =>
      ref.watch(filteredOrderListProvider);

  @override
  AsyncValue<OrderSummary> get summaryAsync => ref.watch(orderSummaryProvider);

  @override
  Widget buildListItem(Order entity) {
    return OrderListItem(
      order: entity,
      onTap: _showOrderDetails,
      onLongPress: _showActionsBottomSheet,
    );
  }

  @override
  void navigateToForm({Order? entity}) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => OrderFormScreen(order: entity),
          ),
        )
        .then((_) {
          // Refresh the list when returning from the form screen
          ref.invalidate(orderListProvider);
          ref.read(paginatedOrderListProvider.notifier).refresh();
        });
  }

  @override
  Future<void> deleteEntity(Order entity) async {
    if (entity.id == null) return;

    // Show loading indicator
    SnackbarUtils.showLoading(message: 'Deleting...');

    // Use the provider to delete the order record
    final success = await ref
        .read(orderListProvider.notifier)
        .deleteOrder(entity.id!);

    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess('Order record deleted successfully');
      } else {
        SnackbarUtils.showError('Failed to delete order record');
      }
    }
  }

  @override
  Widget buildSummarySection(OrderSummary summary) {
    // Get the filtered order list for analytics
    final orderListAsync = ref.watch(filteredOrderListProvider);

    return Column(
      children: [
        // Order metrics card
        OrderMetrics(summary: summary),

        // Add spacing between cards
        const SizedBox(height: 20),

        // Order trends card
        orderListAsync.maybeWhen(
          data: (orderList) => orderList.isNotEmpty
              ? OrderTrendsCard(orders: orderList)
              : const SizedBox.shrink(),
          error: (error, stack) => buildErrorContainer(error),
          orElse: () => const SizedBox.shrink(),
        ),
      ],
    );
  }

  @override
  Widget buildLoadingState() {
    return const OrderStandardizedShimmerLoading();
  }

  @override
  void refreshData() {
    // Use efficient refresh method for paginated provider
    ref.read(paginatedOrderListProvider.notifier).refresh();
    // Invalidate other providers that don't have refresh methods
    ref.invalidate(orderListProvider);
    ref.invalidate(orderSummaryProvider);
  }

  // Helper methods for Order-specific functionality

  void _showActionsBottomSheet(BuildContext context, Order order) {
    ItemActionsBottomSheet.show(
      context: context,
      title: 'Order Record',
      subtitle: 'Date: ${DateHelper.formatForDisplay(order.date)}',
      onEdit: () => navigateToForm(entity: order),
      onDelete: () => _showDeleteConfirmation(context, order),
      itemIcon: Icons.assignment,
    );
  }

  void _showDeleteConfirmation(BuildContext context, Order order) async {
    final confirmed = await AppDeleteConfirmationDialog.showForRecord(
      context: context,
      recordType: 'Order Record',
      recordIdentifier: DateHelper.formatForDisplay(order.date),
    );

    if (confirmed == true) {
      await deleteEntity(order);
    }
  }

  void _showOrderDetails(BuildContext context, Order order) {
    OrderDetailsBottomSheet.show(context, order);
  }
}

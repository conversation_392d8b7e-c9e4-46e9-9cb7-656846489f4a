// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderRepositoryHash() => r'173a0809767cd31a22d1f8b6d7d98cdf6c932cf0';

/// See also [orderRepository].
@ProviderFor(orderRepository)
final orderRepositoryProvider = AutoDisposeProvider<OrderRepository>.internal(
  orderRepository,
  name: r'orderRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OrderRepositoryRef = AutoDisposeProviderRef<OrderRepository>;
String _$getOrderSummaryHash() => r'30c30f2c5ed12616bb2309646db44c8ea2df602d';

/// See also [getOrderSummary].
@ProviderFor(getOrderSummary)
final getOrderSummaryProvider = AutoDisposeProvider<GetOrderSummary>.internal(
  getOrderSummary,
  name: r'getOrderSummaryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$getOrderSummaryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetOrderSummaryRef = AutoDisposeProviderRef<GetOrderSummary>;
String _$filteredOrderListHash() => r'79cfc6fabddf1bb76086f82110004ea90d8d1349';

/// See also [filteredOrderList].
@ProviderFor(filteredOrderList)
final filteredOrderListProvider =
    AutoDisposeFutureProvider<List<domain.Order>>.internal(
      filteredOrderList,
      name: r'filteredOrderListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$filteredOrderListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FilteredOrderListRef = AutoDisposeFutureProviderRef<List<domain.Order>>;
String _$orderSummaryHash() => r'afec156724f1d4c2ce37f04850775582be4d1899';

/// See also [orderSummary].
@ProviderFor(orderSummary)
final orderSummaryProvider = AutoDisposeFutureProvider<OrderSummary>.internal(
  orderSummary,
  name: r'orderSummaryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderSummaryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OrderSummaryRef = AutoDisposeFutureProviderRef<OrderSummary>;
String _$currentMonthPointsHash() =>
    r'd2644b017304dd63a002e8d69c3e610415b416b8';

/// See also [currentMonthPoints].
@ProviderFor(currentMonthPoints)
final currentMonthPointsProvider = AutoDisposeFutureProvider<int>.internal(
  currentMonthPoints,
  name: r'currentMonthPointsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentMonthPointsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentMonthPointsRef = AutoDisposeFutureProviderRef<int>;
String _$currentMonthBidAcceptanceHash() =>
    r'732b0b75b11b7a8097a92768b9dd90983dce3189';

/// See also [currentMonthBidAcceptance].
@ProviderFor(currentMonthBidAcceptance)
final currentMonthBidAcceptanceProvider =
    AutoDisposeFutureProvider<double>.internal(
      currentMonthBidAcceptance,
      name: r'currentMonthBidAcceptanceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentMonthBidAcceptanceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentMonthBidAcceptanceRef = AutoDisposeFutureProviderRef<double>;
String _$currentMonthTripCompletionHash() =>
    r'fd5bdcd435683d01c0483bdde3ae4893a3218425';

/// See also [currentMonthTripCompletion].
@ProviderFor(currentMonthTripCompletion)
final currentMonthTripCompletionProvider =
    AutoDisposeFutureProvider<double>.internal(
      currentMonthTripCompletion,
      name: r'currentMonthTripCompletionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$currentMonthTripCompletionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentMonthTripCompletionRef = AutoDisposeFutureProviderRef<double>;
String _$orderListHash() => r'e67223c2a0746bf468983d8d58105cf4dd8f02f8';

/// See also [OrderList].
@ProviderFor(OrderList)
final orderListProvider =
    AutoDisposeAsyncNotifierProvider<OrderList, List<domain.Order>>.internal(
      OrderList.new,
      name: r'orderListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$orderListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OrderList = AutoDisposeAsyncNotifier<List<domain.Order>>;
String _$orderDateRangeHash() => r'e53b04377763b9de570630881e0e8cf508bbf88a';

/// See also [OrderDateRange].
@ProviderFor(OrderDateRange)
final orderDateRangeProvider =
    AutoDisposeNotifierProvider<
      OrderDateRange,
      AsyncValue<DateTimeRange>
    >.internal(
      OrderDateRange.new,
      name: r'orderDateRangeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$orderDateRangeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OrderDateRange = AutoDisposeNotifier<AsyncValue<DateTimeRange>>;
String _$paginatedOrderListHash() =>
    r'8703a75a8fa8aff7b066f867957370cc08f7edb3';

/// See also [PaginatedOrderList].
@ProviderFor(PaginatedOrderList)
final paginatedOrderListProvider =
    AutoDisposeAsyncNotifierProvider<
      PaginatedOrderList,
      PaginatedResult<domain.Order>
    >.internal(
      PaginatedOrderList.new,
      name: r'paginatedOrderListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$paginatedOrderListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PaginatedOrderList =
    AutoDisposeAsyncNotifier<PaginatedResult<domain.Order>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

/// Orders feature barrel export file
///
/// This file provides a single import point for all orders functionality:
/// ```dart
/// import 'package:bidtrakr/features/orders/orders.dart';
/// ```
library;

// Data layer
export 'data/repositories/order_repository_impl.dart';

// Domain layer
export 'domain/entities/order.dart';
export 'domain/repositories/order_repository.dart';
export 'domain/use_cases/calculate_bid_acceptance.dart';
export 'domain/use_cases/get_order_summary.dart';

// Presentation layer - providers
export 'presentation/providers/order_providers.dart';

// Presentation layer - screens
export 'presentation/screens/order_form_screen.dart';
export 'presentation/screens/orders_screen.dart';

// Presentation layer - widgets
export 'presentation/widgets/empty_order_list.dart';
export 'presentation/widgets/order_detail_item.dart';
export 'presentation/widgets/order_detail_section.dart';
export 'presentation/widgets/order_details_bottom_sheet.dart';
export 'presentation/widgets/order_history_header.dart';
export 'presentation/widgets/order_list_item.dart';
export 'presentation/widgets/order_metric_progress.dart';
export 'presentation/widgets/order_metrics.dart';
export 'presentation/widgets/order_shimmer_loading.dart';
export 'presentation/widgets/order_standardized_shimmer_loading.dart';
export 'presentation/widgets/order_summary_item.dart';
export 'presentation/widgets/order_trends_card.dart';
export 'presentation/widgets/order_unified_shimmer_loading.dart';

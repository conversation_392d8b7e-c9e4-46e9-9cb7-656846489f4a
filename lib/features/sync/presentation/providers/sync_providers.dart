import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/providers/provider_templates.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../data/repositories/sync_repository_impl.dart';
import '../../domain/repositories/sync_repository.dart';
import '../../domain/use_cases/get_sync_status.dart';
import '../../domain/use_cases/schedule_sync.dart';
import '../../domain/use_cases/sync_data.dart';

/// Provider for the sync repository
final syncRepositoryProvider = Provider<SyncRepository>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  return SyncRepositoryImpl(syncService);
});

/// Provider for the sync data use case
final syncDataProvider = Provider<SyncData>((ref) {
  return createUseCaseProvider<SyncData, SyncRepository>(
    ref,
    syncRepositoryProvider,
    (repository) => SyncData(repository),
  );
});

/// Provider for the get sync status use case
final getSyncStatusProvider = Provider<GetSyncStatus>((ref) {
  return createUseCaseProvider<GetSyncStatus, SyncRepository>(
    ref,
    syncRepositoryProvider,
    (repository) => GetSyncStatus(repository),
  );
});

/// Provider for the schedule sync use case
final scheduleSyncProvider = Provider<ScheduleSync>((ref) {
  return createUseCaseProvider<ScheduleSync, SyncRepository>(
    ref,
    syncRepositoryProvider,
    (repository) => ScheduleSync(repository),
  );
});

/// Provider for current sync status (async)
final currentSyncStatusProvider = FutureProvider((ref) async {
  final getSyncStatus = ref.watch(getSyncStatusProvider);
  final result = await getSyncStatus.getCurrentStatus();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (status) => status,
  );
});

/// Provider for sync in progress status (async)
final syncInProgressProvider = FutureProvider<bool>((ref) async {
  final getSyncStatus = ref.watch(getSyncStatusProvider);
  final result = await getSyncStatus.isInProgress();
  return result.fold((failure) => false, (isInProgress) => isInProgress);
});

/// Provider for sync connectivity status (async)
final syncConnectivityProvider = FutureProvider<bool>((ref) async {
  final getSyncStatus = ref.watch(getSyncStatusProvider);
  final result = await getSyncStatus.checkConnectivity();
  return result.fold((failure) => false, (isConnected) => isConnected);
});

/// Provider for sync history (async)
final syncHistoryProvider = FutureProvider((ref) async {
  final getSyncStatus = ref.watch(getSyncStatusProvider);
  final result = await getSyncStatus.getRecentHistory();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (history) => history,
  );
});

/// Provider for sync statistics (async)
final syncStatisticsProvider = FutureProvider((ref) async {
  final getSyncStatus = ref.watch(getSyncStatusProvider);
  final result = await getSyncStatus.getStatistics();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (statistics) => statistics,
  );
});

/// Provider for detailed sync status (async)
final detailedSyncStatusProvider = FutureProvider((ref) async {
  final getSyncStatus = ref.watch(getSyncStatusProvider);
  final result = await getSyncStatus.getDetailedStatus();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (detailedStatus) => detailedStatus,
  );
});

/// Provider for pending sync operations (async)
final pendingSyncOperationsProvider = FutureProvider((ref) async {
  final scheduleSync = ref.watch(scheduleSyncProvider);
  final result = await scheduleSync.getPendingOperations();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (operations) => operations,
  );
});

/// Provider for last sync times by entity type (async)
final lastSyncTimesProvider = FutureProvider.family<DateTime?, String>((
  ref,
  entityType,
) async {
  final getSyncStatus = ref.watch(getSyncStatusProvider);
  final result = await getSyncStatus.getLastSyncTime(entityType);
  return result.fold((failure) => null, (lastSyncTime) => lastSyncTime);
});

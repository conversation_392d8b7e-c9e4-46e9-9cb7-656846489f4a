import 'dart:async';
import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/services/sync/sync_operations.dart' as core_sync;
import '../../../../core/services/sync/sync_service.dart';
import '../../domain/entities/sync_operation.dart';
import '../../domain/entities/sync_result.dart';
import '../../domain/entities/sync_status.dart';
import '../../domain/repositories/sync_repository.dart';

/// Implementation of SyncRepository using the existing core sync services
class SyncRepositoryImpl implements SyncRepository {
  final SyncService _syncService;
  StreamController<Either<Failure, SyncStatus>>? _statusController;

  SyncRepositoryImpl(this._syncService);

  @override
  Future<Either<Failure, bool>> initialize() async {
    try {
      _setupStatusListener();
      return const Right(true);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to initialize sync repository: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, SyncResult>> executeSync(
    SyncOperation operation,
  ) async {
    try {
      final startTime = DateTime.now();

      // Convert domain operation to core operation
      final coreOperation = _mapToCoreSyncOperation(operation.type);

      // Execute sync
      await _syncService.syncData(coreOperation);

      // Create successful result
      final result = SyncResult.success(
        operationId: operation.id,
        operationType: operation.type,
        startedAt: startTime,
        completedAt: DateTime.now(),
        metadata: operation.metadata,
      );

      return Right(result);
    } catch (e) {
      final result = SyncResult.failure(
        operationId: operation.id,
        operationType: operation.type,
        startedAt: DateTime.now(),
        errorMessage: e.toString(),
      );

      return Right(result);
    }
  }

  @override
  Future<Either<Failure, SyncStatus?>> getCurrentSyncStatus() async {
    try {
      // TODO: Implement isSyncing in SyncService
      return const Right(null);
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to get sync status: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SyncResult>>> getSyncHistory({
    int? limit,
    DateTime? since,
    SyncOperationType? operationType,
  }) async {
    try {
      // For now, return empty list as core service doesn't expose history
      // This would need to be implemented with proper persistence
      return const Right([]);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to get sync history: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, String>> scheduleSync(SyncOperation operation) async {
    try {
      // For now, execute immediately as core service doesn't support scheduling
      final result = await executeSync(operation);
      return result.fold(
        (failure) => Left(failure),
        (syncResult) => Right(operation.id),
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to schedule sync: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> cancelSync(String operationId) async {
    try {
      // Core service doesn't support cancellation
      return Left(
        Failure.unexpected(message: 'Sync cancellation not implemented'),
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to cancel sync: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SyncOperation>>> getPendingOperations() async {
    try {
      // For now, return empty list as core service doesn't expose pending operations
      return const Right([]);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to get pending operations: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> isSyncInProgress() async {
    try {
      // TODO: Implement isSyncing in SyncService
      return const Right(false);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to check sync progress: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, DateTime?>> getLastSyncTime(String entityType) async {
    try {
      // TODO: Implement getLastSyncTime in SyncService
      return const Right(null);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to get last sync time: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> updateLastSyncTime(
    String entityType,
    DateTime syncTime,
  ) async {
    try {
      // TODO: Implement updateLastSyncTime in SyncService
      return const Right(true);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to update last sync time: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, SyncStatistics>> getSyncStatistics({
    DateTime? since,
    DateTime? until,
  }) async {
    try {
      // For now, return empty statistics as core service doesn't expose detailed stats
      return const Right(
        SyncStatistics(
          totalOperations: 0,
          successfulOperations: 0,
          failedOperations: 0,
          totalItemsProcessed: 0,
          totalItemsFailed: 0,
          averageDuration: Duration.zero,
          operationsByType: {},
          itemsByEntity: {},
        ),
      );
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to get sync statistics: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, SyncResult>> forceSyncEntities(
    List<String> entityTypes,
  ) async {
    try {
      final operation = SyncOperation.full(
        id: 'force_sync_${DateTime.now().millisecondsSinceEpoch}',
        entityTypes: entityTypes,
        priority: SyncPriority.high,
      );

      return await executeSync(operation);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to force sync entities: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> isConnected() async {
    try {
      // TODO: Implement isConnected in SyncService
      return const Right(true);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to check connectivity: $e'),
      );
    }
  }

  @override
  Stream<Either<Failure, SyncStatus>> get syncStatusStream {
    _statusController ??=
        StreamController<Either<Failure, SyncStatus>>.broadcast();
    return _statusController!.stream;
  }

  @override
  Future<Either<Failure, int>> cleanupOldHistory({
    Duration? olderThan,
    int? keepCount,
  }) async {
    try {
      // For now, return 0 as core service doesn't support history cleanup
      return const Right(0);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to cleanup old history: $e'),
      );
    }
  }

  /// Map domain sync operation type to core sync operation
  core_sync.SyncOperation _mapToCoreSyncOperation(SyncOperationType type) {
    switch (type) {
      case SyncOperationType.upload:
        return core_sync.SyncOperation.upload;
      case SyncOperationType.download:
        return core_sync.SyncOperation.download;
      case SyncOperationType.full:
      case SyncOperationType.incremental:
        return core_sync.SyncOperation.full;
    }
  }

  void _setupStatusListener() {
    _statusController ??=
        StreamController<Either<Failure, SyncStatus>>.broadcast();

    // Listen to sync state changes from core service
    // This would need to be implemented when core service exposes status stream
  }

  void dispose() {
    _statusController?.close();
  }
}

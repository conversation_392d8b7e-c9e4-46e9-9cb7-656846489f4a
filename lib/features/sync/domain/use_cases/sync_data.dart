import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/sync_operation.dart';
import '../entities/sync_result.dart';
import '../repositories/sync_repository.dart';

/// Use case for synchronizing data
class SyncData {
  final SyncRepository repository;

  SyncData(this.repository);

  /// Execute a full sync operation
  Future<Either<Failure, SyncResult>> executeFullSync({
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? metadata,
  }) async {
    // Check if sync is already in progress
    final inProgressResult = await repository.isSyncInProgress();

    return inProgressResult.fold((failure) => Left(failure), (
      isInProgress,
    ) async {
      if (isInProgress) {
        return Left(
          Failure.businessLogic(message: 'Sync operation already in progress'),
        );
      }

      // Create full sync operation
      final operation = SyncOperation.full(
        id: 'full_sync_${DateTime.now().millisecondsSinceEpoch}',
        entityTypes: entityTypes,
        priority: priority,
        metadata: metadata,
      );

      // Execute the sync
      return await repository.executeSync(operation);
    });
  }

  /// Execute an upload sync operation
  Future<Either<Failure, SyncResult>> executeUploadSync({
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? metadata,
  }) async {
    // Check if sync is already in progress
    final inProgressResult = await repository.isSyncInProgress();

    return inProgressResult.fold((failure) => Left(failure), (
      isInProgress,
    ) async {
      if (isInProgress) {
        return Left(
          Failure.businessLogic(message: 'Sync operation already in progress'),
        );
      }

      // Create upload sync operation
      final operation = SyncOperation.upload(
        id: 'upload_sync_${DateTime.now().millisecondsSinceEpoch}',
        entityTypes: entityTypes,
        priority: priority,
        filters: filters,
        metadata: metadata,
      );

      // Execute the sync
      return await repository.executeSync(operation);
    });
  }

  /// Execute a download sync operation
  Future<Either<Failure, SyncResult>> executeDownloadSync({
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? metadata,
  }) async {
    // Check if sync is already in progress
    final inProgressResult = await repository.isSyncInProgress();

    return inProgressResult.fold((failure) => Left(failure), (
      isInProgress,
    ) async {
      if (isInProgress) {
        return Left(
          Failure.businessLogic(message: 'Sync operation already in progress'),
        );
      }

      // Create download sync operation
      final operation = SyncOperation.download(
        id: 'download_sync_${DateTime.now().millisecondsSinceEpoch}',
        entityTypes: entityTypes,
        priority: priority,
        filters: filters,
        metadata: metadata,
      );

      // Execute the sync
      return await repository.executeSync(operation);
    });
  }

  /// Execute an incremental sync operation
  Future<Either<Failure, SyncResult>> executeIncrementalSync({
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? metadata,
  }) async {
    // Check if sync is already in progress
    final inProgressResult = await repository.isSyncInProgress();

    return inProgressResult.fold((failure) => Left(failure), (
      isInProgress,
    ) async {
      if (isInProgress) {
        return Left(
          Failure.businessLogic(message: 'Sync operation already in progress'),
        );
      }

      // Get last sync time for incremental sync
      DateTime? lastSyncTime;
      if (entityTypes != null && entityTypes.isNotEmpty) {
        // For simplicity, use the earliest last sync time among all entity types
        for (final entityType in entityTypes) {
          final lastSyncResult = await repository.getLastSyncTime(entityType);
          lastSyncResult.fold(
            (failure) => null, // Ignore failures for individual entity types
            (entityLastSync) {
              if (entityLastSync != null) {
                if (lastSyncTime == null ||
                    entityLastSync.isBefore(lastSyncTime!)) {
                  lastSyncTime = entityLastSync;
                }
              }
            },
          );
        }
      }

      // Create incremental sync operation
      final operation = SyncOperation.incremental(
        id: 'incremental_sync_${DateTime.now().millisecondsSinceEpoch}',
        entityTypes: entityTypes,
        priority: priority,
        lastSyncTime: lastSyncTime,
        metadata: metadata,
      );

      // Execute the sync
      return await repository.executeSync(operation);
    });
  }

  /// Force sync specific entities
  Future<Either<Failure, SyncResult>> forceSyncEntities(
    List<String> entityTypes, {
    SyncPriority priority = SyncPriority.high,
  }) async {
    if (entityTypes.isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Entity types cannot be empty'),
      );
    }

    return await repository.forceSyncEntities(entityTypes);
  }
}

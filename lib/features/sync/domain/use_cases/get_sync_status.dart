import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/sync_operation.dart';
import '../entities/sync_result.dart';
import '../entities/sync_status.dart';
import '../repositories/sync_repository.dart';

/// Use case for getting sync status and history
class GetSyncStatus {
  final SyncRepository repository;

  GetSyncStatus(this.repository);

  /// Get the current sync status
  Future<Either<Failure, SyncStatus?>> getCurrentStatus() async {
    return await repository.getCurrentSyncStatus();
  }

  /// Check if sync is currently in progress
  Future<Either<Failure, bool>> isInProgress() async {
    return await repository.isSyncInProgress();
  }

  /// Get sync history
  Future<Either<Failure, List<SyncResult>>> getHistory({
    int? limit,
    DateTime? since,
    SyncOperationType? operationType,
  }) async {
    return await repository.getSyncHistory(
      limit: limit,
      since: since,
      operationType: operationType,
    );
  }

  /// Get recent sync history (last 10 operations)
  Future<Either<Failure, List<SyncResult>>> getRecentHistory() async {
    return await repository.getSyncHistory(limit: 10);
  }

  /// Get sync history for a specific operation type
  Future<Either<Failure, List<SyncResult>>> getHistoryByType(
    SyncOperationType operationType, {
    int? limit,
    DateTime? since,
  }) async {
    return await repository.getSyncHistory(
      limit: limit,
      since: since,
      operationType: operationType,
    );
  }

  /// Get last sync time for a specific entity type
  Future<Either<Failure, DateTime?>> getLastSyncTime(String entityType) async {
    if (entityType.isEmpty) {
      return Left(Failure.invalidInput(message: 'Entity type cannot be empty'));
    }

    return await repository.getLastSyncTime(entityType);
  }

  /// Get last sync times for multiple entity types
  Future<Either<Failure, Map<String, DateTime?>>> getLastSyncTimes(
    List<String> entityTypes,
  ) async {
    if (entityTypes.isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Entity types cannot be empty'),
      );
    }

    final results = <String, DateTime?>{};

    for (final entityType in entityTypes) {
      final result = await repository.getLastSyncTime(entityType);
      result.fold(
        (failure) => results[entityType] = null,
        (lastSyncTime) => results[entityType] = lastSyncTime,
      );
    }

    return Right(results);
  }

  /// Get sync statistics
  Future<Either<Failure, SyncStatistics>> getStatistics({
    DateTime? since,
    DateTime? until,
  }) async {
    return await repository.getSyncStatistics(since: since, until: until);
  }

  /// Get sync statistics for the last 30 days
  Future<Either<Failure, SyncStatistics>> getMonthlyStatistics() async {
    final since = DateTime.now().subtract(const Duration(days: 30));
    return await repository.getSyncStatistics(since: since);
  }

  /// Get sync statistics for the last 7 days
  Future<Either<Failure, SyncStatistics>> getWeeklyStatistics() async {
    final since = DateTime.now().subtract(const Duration(days: 7));
    return await repository.getSyncStatistics(since: since);
  }

  /// Check connectivity status
  Future<Either<Failure, bool>> checkConnectivity() async {
    return await repository.isConnected();
  }

  /// Get detailed sync status with additional information
  Future<Either<Failure, DetailedSyncStatus>> getDetailedStatus() async {
    // Get current status
    final currentStatusResult = await getCurrentStatus();

    return currentStatusResult.fold((failure) => Left(failure), (
      currentStatus,
    ) async {
      // Get connectivity status
      final connectivityResult = await checkConnectivity();

      return connectivityResult.fold((failure) => Left(failure), (
        isConnected,
      ) async {
        // Get recent history
        final historyResult = await getRecentHistory();

        return historyResult.fold((failure) => Left(failure), (history) async {
          // Get pending operations
          final pendingResult = await repository.getPendingOperations();

          return pendingResult.fold((failure) => Left(failure), (
            pendingOperations,
          ) {
            return Right(
              DetailedSyncStatus(
                currentStatus: currentStatus,
                isConnected: isConnected,
                recentHistory: history,
                pendingOperations: pendingOperations,
              ),
            );
          });
        });
      });
    });
  }
}

/// Detailed sync status with additional information
class DetailedSyncStatus {
  final SyncStatus? currentStatus;
  final bool isConnected;
  final List<SyncResult> recentHistory;
  final List<SyncOperation> pendingOperations;

  const DetailedSyncStatus({
    required this.currentStatus,
    required this.isConnected,
    required this.recentHistory,
    required this.pendingOperations,
  });

  /// Check if sync is available (connected and not in progress)
  bool get isSyncAvailable =>
      isConnected && (currentStatus?.isInProgress != true);

  /// Get the last successful sync from history
  SyncResult? get lastSuccessfulSync {
    for (final result in recentHistory) {
      if (result.isSuccess) {
        return result;
      }
    }
    return null;
  }

  /// Get the last failed sync from history
  SyncResult? get lastFailedSync {
    for (final result in recentHistory) {
      if (!result.isSuccess) {
        return result;
      }
    }
    return null;
  }

  /// Check if there are any pending operations
  bool get hasPendingOperations => pendingOperations.isNotEmpty;
}

import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/sync_operation.dart';
import '../repositories/sync_repository.dart';

/// Use case for scheduling sync operations
class ScheduleSync {
  final SyncRepository repository;

  ScheduleSync(this.repository);

  /// Schedule a sync operation
  Future<Either<Failure, String>> schedule(SyncOperation operation) async {
    // Validate operation
    final validationResult = _validateOperation(operation);
    if (validationResult != null) {
      return Left(validationResult);
    }

    return await repository.scheduleSync(operation);
  }

  /// Schedule a full sync
  Future<Either<Failure, String>> scheduleFullSync({
    DateTime? scheduledAt,
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? metadata,
  }) async {
    final operation = SyncOperation.full(
      id: 'scheduled_full_${DateTime.now().millisecondsSinceEpoch}',
      entityTypes: entityTypes,
      priority: priority,
      metadata: metadata,
    );

    if (scheduledAt != null) {
      final updatedOperation = operation.copyWith(scheduledAt: scheduledAt);
      return await schedule(updatedOperation);
    }

    return await schedule(operation);
  }

  /// Schedule an upload sync
  Future<Either<Failure, String>> scheduleUploadSync({
    DateTime? scheduledAt,
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? metadata,
  }) async {
    final operation = SyncOperation.upload(
      id: 'scheduled_upload_${DateTime.now().millisecondsSinceEpoch}',
      entityTypes: entityTypes,
      priority: priority,
      filters: filters,
      metadata: metadata,
    );

    if (scheduledAt != null) {
      final updatedOperation = operation.copyWith(scheduledAt: scheduledAt);
      return await schedule(updatedOperation);
    }

    return await schedule(operation);
  }

  /// Schedule a download sync
  Future<Either<Failure, String>> scheduleDownloadSync({
    DateTime? scheduledAt,
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? metadata,
  }) async {
    final operation = SyncOperation.download(
      id: 'scheduled_download_${DateTime.now().millisecondsSinceEpoch}',
      entityTypes: entityTypes,
      priority: priority,
      filters: filters,
      metadata: metadata,
    );

    if (scheduledAt != null) {
      final updatedOperation = operation.copyWith(scheduledAt: scheduledAt);
      return await schedule(updatedOperation);
    }

    return await schedule(operation);
  }

  /// Schedule an incremental sync
  Future<Either<Failure, String>> scheduleIncrementalSync({
    DateTime? scheduledAt,
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    DateTime? lastSyncTime,
    Map<String, dynamic>? metadata,
  }) async {
    final operation = SyncOperation.incremental(
      id: 'scheduled_incremental_${DateTime.now().millisecondsSinceEpoch}',
      entityTypes: entityTypes,
      priority: priority,
      lastSyncTime: lastSyncTime,
      metadata: metadata,
    );

    if (scheduledAt != null) {
      final updatedOperation = operation.copyWith(scheduledAt: scheduledAt);
      return await schedule(updatedOperation);
    }

    return await schedule(operation);
  }

  /// Schedule a recurring sync operation
  Future<Either<Failure, List<String>>> scheduleRecurringSync({
    required SyncOperationType operationType,
    required Duration interval,
    required int occurrences,
    DateTime? startTime,
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? metadata,
  }) async {
    if (occurrences <= 0) {
      return Left(
        Failure.invalidInput(message: 'Occurrences must be greater than 0'),
      );
    }

    if (interval.inMinutes < 1) {
      return Left(
        Failure.invalidInput(message: 'Interval must be at least 1 minute'),
      );
    }

    final operationIds = <String>[];
    final baseTime = startTime ?? DateTime.now();

    for (int i = 0; i < occurrences; i++) {
      final scheduledTime = baseTime.add(interval * i);
      final operationId =
          'recurring_${operationType.name}_${scheduledTime.millisecondsSinceEpoch}';

      final operation = _createOperationByType(
        operationType: operationType,
        id: operationId,
        scheduledAt: scheduledTime,
        entityTypes: entityTypes,
        priority: priority,
        filters: filters,
        metadata: metadata,
      );

      final result = await schedule(operation);
      result.fold(
        (failure) => null, // Continue with other operations even if one fails
        (id) => operationIds.add(id),
      );
    }

    if (operationIds.isEmpty) {
      return Left(
        Failure.unexpected(
          message: 'Failed to schedule any recurring operations',
        ),
      );
    }

    return Right(operationIds);
  }

  /// Cancel a scheduled sync operation
  Future<Either<Failure, bool>> cancel(String operationId) async {
    if (operationId.isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Operation ID cannot be empty'),
      );
    }

    return await repository.cancelSync(operationId);
  }

  /// Get pending operations
  Future<Either<Failure, List<SyncOperation>>> getPendingOperations() async {
    return await repository.getPendingOperations();
  }

  /// Cancel all pending operations
  Future<Either<Failure, int>> cancelAllPending() async {
    final pendingResult = await getPendingOperations();

    return pendingResult.fold((failure) => Left(failure), (
      pendingOperations,
    ) async {
      int cancelledCount = 0;

      for (final operation in pendingOperations) {
        final cancelResult = await cancel(operation.id);
        cancelResult.fold(
          (failure) => null, // Continue with other operations
          (success) {
            if (success) cancelledCount++;
          },
        );
      }

      return Right(cancelledCount);
    });
  }

  /// Validate sync operation
  Failure? _validateOperation(SyncOperation operation) {
    if (operation.id.isEmpty) {
      return Failure.invalidInput(message: 'Operation ID cannot be empty');
    }

    if (operation.scheduledAt.isBefore(
      DateTime.now().subtract(const Duration(minutes: 1)),
    )) {
      return Failure.invalidInput(
        message: 'Cannot schedule operation in the past',
      );
    }

    return null;
  }

  /// Create operation by type
  SyncOperation _createOperationByType({
    required SyncOperationType operationType,
    required String id,
    required DateTime scheduledAt,
    List<String>? entityTypes,
    SyncPriority priority = SyncPriority.normal,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? metadata,
  }) {
    switch (operationType) {
      case SyncOperationType.upload:
        return SyncOperation.upload(
          id: id,
          entityTypes: entityTypes,
          priority: priority,
          filters: filters,
          metadata: metadata,
        ).copyWith(scheduledAt: scheduledAt);
      case SyncOperationType.download:
        return SyncOperation.download(
          id: id,
          entityTypes: entityTypes,
          priority: priority,
          filters: filters,
          metadata: metadata,
        ).copyWith(scheduledAt: scheduledAt);
      case SyncOperationType.full:
        return SyncOperation.full(
          id: id,
          entityTypes: entityTypes,
          priority: priority,
          metadata: metadata,
        ).copyWith(scheduledAt: scheduledAt);
      case SyncOperationType.incremental:
        return SyncOperation.incremental(
          id: id,
          entityTypes: entityTypes,
          priority: priority,
          metadata: metadata,
        ).copyWith(scheduledAt: scheduledAt);
    }
  }
}

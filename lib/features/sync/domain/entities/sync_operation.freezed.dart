// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sync_operation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SyncOperation {

 String get id; SyncOperationType get type; DateTime get scheduledAt; DateTime? get startedAt; DateTime? get completedAt; SyncPriority get priority; List<String> get entityTypes; Map<String, dynamic>? get filters; Map<String, dynamic>? get metadata; String? get errorMessage; bool get isRetry; int? get retryCount; int? get maxRetries;
/// Create a copy of SyncOperation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SyncOperationCopyWith<SyncOperation> get copyWith => _$SyncOperationCopyWithImpl<SyncOperation>(this as SyncOperation, _$identity);

  /// Serializes this SyncOperation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SyncOperation&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.scheduledAt, scheduledAt) || other.scheduledAt == scheduledAt)&&(identical(other.startedAt, startedAt) || other.startedAt == startedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.priority, priority) || other.priority == priority)&&const DeepCollectionEquality().equals(other.entityTypes, entityTypes)&&const DeepCollectionEquality().equals(other.filters, filters)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isRetry, isRetry) || other.isRetry == isRetry)&&(identical(other.retryCount, retryCount) || other.retryCount == retryCount)&&(identical(other.maxRetries, maxRetries) || other.maxRetries == maxRetries));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,scheduledAt,startedAt,completedAt,priority,const DeepCollectionEquality().hash(entityTypes),const DeepCollectionEquality().hash(filters),const DeepCollectionEquality().hash(metadata),errorMessage,isRetry,retryCount,maxRetries);

@override
String toString() {
  return 'SyncOperation(id: $id, type: $type, scheduledAt: $scheduledAt, startedAt: $startedAt, completedAt: $completedAt, priority: $priority, entityTypes: $entityTypes, filters: $filters, metadata: $metadata, errorMessage: $errorMessage, isRetry: $isRetry, retryCount: $retryCount, maxRetries: $maxRetries)';
}


}

/// @nodoc
abstract mixin class $SyncOperationCopyWith<$Res>  {
  factory $SyncOperationCopyWith(SyncOperation value, $Res Function(SyncOperation) _then) = _$SyncOperationCopyWithImpl;
@useResult
$Res call({
 String id, SyncOperationType type, DateTime scheduledAt, DateTime? startedAt, DateTime? completedAt, SyncPriority priority, List<String> entityTypes, Map<String, dynamic>? filters, Map<String, dynamic>? metadata, String? errorMessage, bool isRetry, int? retryCount, int? maxRetries
});




}
/// @nodoc
class _$SyncOperationCopyWithImpl<$Res>
    implements $SyncOperationCopyWith<$Res> {
  _$SyncOperationCopyWithImpl(this._self, this._then);

  final SyncOperation _self;
  final $Res Function(SyncOperation) _then;

/// Create a copy of SyncOperation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? type = null,Object? scheduledAt = null,Object? startedAt = freezed,Object? completedAt = freezed,Object? priority = null,Object? entityTypes = null,Object? filters = freezed,Object? metadata = freezed,Object? errorMessage = freezed,Object? isRetry = null,Object? retryCount = freezed,Object? maxRetries = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SyncOperationType,scheduledAt: null == scheduledAt ? _self.scheduledAt : scheduledAt // ignore: cast_nullable_to_non_nullable
as DateTime,startedAt: freezed == startedAt ? _self.startedAt : startedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as SyncPriority,entityTypes: null == entityTypes ? _self.entityTypes : entityTypes // ignore: cast_nullable_to_non_nullable
as List<String>,filters: freezed == filters ? _self.filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isRetry: null == isRetry ? _self.isRetry : isRetry // ignore: cast_nullable_to_non_nullable
as bool,retryCount: freezed == retryCount ? _self.retryCount : retryCount // ignore: cast_nullable_to_non_nullable
as int?,maxRetries: freezed == maxRetries ? _self.maxRetries : maxRetries // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [SyncOperation].
extension SyncOperationPatterns on SyncOperation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SyncOperation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SyncOperation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SyncOperation value)  $default,){
final _that = this;
switch (_that) {
case _SyncOperation():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SyncOperation value)?  $default,){
final _that = this;
switch (_that) {
case _SyncOperation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  SyncOperationType type,  DateTime scheduledAt,  DateTime? startedAt,  DateTime? completedAt,  SyncPriority priority,  List<String> entityTypes,  Map<String, dynamic>? filters,  Map<String, dynamic>? metadata,  String? errorMessage,  bool isRetry,  int? retryCount,  int? maxRetries)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SyncOperation() when $default != null:
return $default(_that.id,_that.type,_that.scheduledAt,_that.startedAt,_that.completedAt,_that.priority,_that.entityTypes,_that.filters,_that.metadata,_that.errorMessage,_that.isRetry,_that.retryCount,_that.maxRetries);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  SyncOperationType type,  DateTime scheduledAt,  DateTime? startedAt,  DateTime? completedAt,  SyncPriority priority,  List<String> entityTypes,  Map<String, dynamic>? filters,  Map<String, dynamic>? metadata,  String? errorMessage,  bool isRetry,  int? retryCount,  int? maxRetries)  $default,) {final _that = this;
switch (_that) {
case _SyncOperation():
return $default(_that.id,_that.type,_that.scheduledAt,_that.startedAt,_that.completedAt,_that.priority,_that.entityTypes,_that.filters,_that.metadata,_that.errorMessage,_that.isRetry,_that.retryCount,_that.maxRetries);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  SyncOperationType type,  DateTime scheduledAt,  DateTime? startedAt,  DateTime? completedAt,  SyncPriority priority,  List<String> entityTypes,  Map<String, dynamic>? filters,  Map<String, dynamic>? metadata,  String? errorMessage,  bool isRetry,  int? retryCount,  int? maxRetries)?  $default,) {final _that = this;
switch (_that) {
case _SyncOperation() when $default != null:
return $default(_that.id,_that.type,_that.scheduledAt,_that.startedAt,_that.completedAt,_that.priority,_that.entityTypes,_that.filters,_that.metadata,_that.errorMessage,_that.isRetry,_that.retryCount,_that.maxRetries);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SyncOperation extends SyncOperation {
  const _SyncOperation({required this.id, required this.type, required this.scheduledAt, this.startedAt, this.completedAt, this.priority = SyncPriority.normal, final  List<String> entityTypes = const [], final  Map<String, dynamic>? filters, final  Map<String, dynamic>? metadata, this.errorMessage, this.isRetry = false, this.retryCount, this.maxRetries}): _entityTypes = entityTypes,_filters = filters,_metadata = metadata,super._();
  factory _SyncOperation.fromJson(Map<String, dynamic> json) => _$SyncOperationFromJson(json);

@override final  String id;
@override final  SyncOperationType type;
@override final  DateTime scheduledAt;
@override final  DateTime? startedAt;
@override final  DateTime? completedAt;
@override@JsonKey() final  SyncPriority priority;
 final  List<String> _entityTypes;
@override@JsonKey() List<String> get entityTypes {
  if (_entityTypes is EqualUnmodifiableListView) return _entityTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_entityTypes);
}

 final  Map<String, dynamic>? _filters;
@override Map<String, dynamic>? get filters {
  final value = _filters;
  if (value == null) return null;
  if (_filters is EqualUnmodifiableMapView) return _filters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? errorMessage;
@override@JsonKey() final  bool isRetry;
@override final  int? retryCount;
@override final  int? maxRetries;

/// Create a copy of SyncOperation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SyncOperationCopyWith<_SyncOperation> get copyWith => __$SyncOperationCopyWithImpl<_SyncOperation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SyncOperationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SyncOperation&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.scheduledAt, scheduledAt) || other.scheduledAt == scheduledAt)&&(identical(other.startedAt, startedAt) || other.startedAt == startedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.priority, priority) || other.priority == priority)&&const DeepCollectionEquality().equals(other._entityTypes, _entityTypes)&&const DeepCollectionEquality().equals(other._filters, _filters)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isRetry, isRetry) || other.isRetry == isRetry)&&(identical(other.retryCount, retryCount) || other.retryCount == retryCount)&&(identical(other.maxRetries, maxRetries) || other.maxRetries == maxRetries));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,scheduledAt,startedAt,completedAt,priority,const DeepCollectionEquality().hash(_entityTypes),const DeepCollectionEquality().hash(_filters),const DeepCollectionEquality().hash(_metadata),errorMessage,isRetry,retryCount,maxRetries);

@override
String toString() {
  return 'SyncOperation(id: $id, type: $type, scheduledAt: $scheduledAt, startedAt: $startedAt, completedAt: $completedAt, priority: $priority, entityTypes: $entityTypes, filters: $filters, metadata: $metadata, errorMessage: $errorMessage, isRetry: $isRetry, retryCount: $retryCount, maxRetries: $maxRetries)';
}


}

/// @nodoc
abstract mixin class _$SyncOperationCopyWith<$Res> implements $SyncOperationCopyWith<$Res> {
  factory _$SyncOperationCopyWith(_SyncOperation value, $Res Function(_SyncOperation) _then) = __$SyncOperationCopyWithImpl;
@override @useResult
$Res call({
 String id, SyncOperationType type, DateTime scheduledAt, DateTime? startedAt, DateTime? completedAt, SyncPriority priority, List<String> entityTypes, Map<String, dynamic>? filters, Map<String, dynamic>? metadata, String? errorMessage, bool isRetry, int? retryCount, int? maxRetries
});




}
/// @nodoc
class __$SyncOperationCopyWithImpl<$Res>
    implements _$SyncOperationCopyWith<$Res> {
  __$SyncOperationCopyWithImpl(this._self, this._then);

  final _SyncOperation _self;
  final $Res Function(_SyncOperation) _then;

/// Create a copy of SyncOperation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? type = null,Object? scheduledAt = null,Object? startedAt = freezed,Object? completedAt = freezed,Object? priority = null,Object? entityTypes = null,Object? filters = freezed,Object? metadata = freezed,Object? errorMessage = freezed,Object? isRetry = null,Object? retryCount = freezed,Object? maxRetries = freezed,}) {
  return _then(_SyncOperation(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SyncOperationType,scheduledAt: null == scheduledAt ? _self.scheduledAt : scheduledAt // ignore: cast_nullable_to_non_nullable
as DateTime,startedAt: freezed == startedAt ? _self.startedAt : startedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as SyncPriority,entityTypes: null == entityTypes ? _self._entityTypes : entityTypes // ignore: cast_nullable_to_non_nullable
as List<String>,filters: freezed == filters ? _self._filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isRetry: null == isRetry ? _self.isRetry : isRetry // ignore: cast_nullable_to_non_nullable
as bool,retryCount: freezed == retryCount ? _self.retryCount : retryCount // ignore: cast_nullable_to_non_nullable
as int?,maxRetries: freezed == maxRetries ? _self.maxRetries : maxRetries // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sync_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SyncResult {

 String get operationId; SyncOperationType get operationType; bool get isSuccess; DateTime get startedAt; DateTime get completedAt; String? get errorMessage; int? get totalItems; int? get processedItems; int? get failedItems; int? get uploadedItems; int? get downloadedItems; int? get conflictItems; Map<String, int>? get itemsByEntity; Map<String, dynamic>? get metadata; List<String>? get warnings;
/// Create a copy of SyncResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SyncResultCopyWith<SyncResult> get copyWith => _$SyncResultCopyWithImpl<SyncResult>(this as SyncResult, _$identity);

  /// Serializes this SyncResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SyncResult&&(identical(other.operationId, operationId) || other.operationId == operationId)&&(identical(other.operationType, operationType) || other.operationType == operationType)&&(identical(other.isSuccess, isSuccess) || other.isSuccess == isSuccess)&&(identical(other.startedAt, startedAt) || other.startedAt == startedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems)&&(identical(other.processedItems, processedItems) || other.processedItems == processedItems)&&(identical(other.failedItems, failedItems) || other.failedItems == failedItems)&&(identical(other.uploadedItems, uploadedItems) || other.uploadedItems == uploadedItems)&&(identical(other.downloadedItems, downloadedItems) || other.downloadedItems == downloadedItems)&&(identical(other.conflictItems, conflictItems) || other.conflictItems == conflictItems)&&const DeepCollectionEquality().equals(other.itemsByEntity, itemsByEntity)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&const DeepCollectionEquality().equals(other.warnings, warnings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,operationId,operationType,isSuccess,startedAt,completedAt,errorMessage,totalItems,processedItems,failedItems,uploadedItems,downloadedItems,conflictItems,const DeepCollectionEquality().hash(itemsByEntity),const DeepCollectionEquality().hash(metadata),const DeepCollectionEquality().hash(warnings));

@override
String toString() {
  return 'SyncResult(operationId: $operationId, operationType: $operationType, isSuccess: $isSuccess, startedAt: $startedAt, completedAt: $completedAt, errorMessage: $errorMessage, totalItems: $totalItems, processedItems: $processedItems, failedItems: $failedItems, uploadedItems: $uploadedItems, downloadedItems: $downloadedItems, conflictItems: $conflictItems, itemsByEntity: $itemsByEntity, metadata: $metadata, warnings: $warnings)';
}


}

/// @nodoc
abstract mixin class $SyncResultCopyWith<$Res>  {
  factory $SyncResultCopyWith(SyncResult value, $Res Function(SyncResult) _then) = _$SyncResultCopyWithImpl;
@useResult
$Res call({
 String operationId, SyncOperationType operationType, bool isSuccess, DateTime startedAt, DateTime completedAt, String? errorMessage, int? totalItems, int? processedItems, int? failedItems, int? uploadedItems, int? downloadedItems, int? conflictItems, Map<String, int>? itemsByEntity, Map<String, dynamic>? metadata, List<String>? warnings
});




}
/// @nodoc
class _$SyncResultCopyWithImpl<$Res>
    implements $SyncResultCopyWith<$Res> {
  _$SyncResultCopyWithImpl(this._self, this._then);

  final SyncResult _self;
  final $Res Function(SyncResult) _then;

/// Create a copy of SyncResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? operationId = null,Object? operationType = null,Object? isSuccess = null,Object? startedAt = null,Object? completedAt = null,Object? errorMessage = freezed,Object? totalItems = freezed,Object? processedItems = freezed,Object? failedItems = freezed,Object? uploadedItems = freezed,Object? downloadedItems = freezed,Object? conflictItems = freezed,Object? itemsByEntity = freezed,Object? metadata = freezed,Object? warnings = freezed,}) {
  return _then(_self.copyWith(
operationId: null == operationId ? _self.operationId : operationId // ignore: cast_nullable_to_non_nullable
as String,operationType: null == operationType ? _self.operationType : operationType // ignore: cast_nullable_to_non_nullable
as SyncOperationType,isSuccess: null == isSuccess ? _self.isSuccess : isSuccess // ignore: cast_nullable_to_non_nullable
as bool,startedAt: null == startedAt ? _self.startedAt : startedAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: null == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,processedItems: freezed == processedItems ? _self.processedItems : processedItems // ignore: cast_nullable_to_non_nullable
as int?,failedItems: freezed == failedItems ? _self.failedItems : failedItems // ignore: cast_nullable_to_non_nullable
as int?,uploadedItems: freezed == uploadedItems ? _self.uploadedItems : uploadedItems // ignore: cast_nullable_to_non_nullable
as int?,downloadedItems: freezed == downloadedItems ? _self.downloadedItems : downloadedItems // ignore: cast_nullable_to_non_nullable
as int?,conflictItems: freezed == conflictItems ? _self.conflictItems : conflictItems // ignore: cast_nullable_to_non_nullable
as int?,itemsByEntity: freezed == itemsByEntity ? _self.itemsByEntity : itemsByEntity // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,warnings: freezed == warnings ? _self.warnings : warnings // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

}


/// Adds pattern-matching-related methods to [SyncResult].
extension SyncResultPatterns on SyncResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SyncResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SyncResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SyncResult value)  $default,){
final _that = this;
switch (_that) {
case _SyncResult():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SyncResult value)?  $default,){
final _that = this;
switch (_that) {
case _SyncResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String operationId,  SyncOperationType operationType,  bool isSuccess,  DateTime startedAt,  DateTime completedAt,  String? errorMessage,  int? totalItems,  int? processedItems,  int? failedItems,  int? uploadedItems,  int? downloadedItems,  int? conflictItems,  Map<String, int>? itemsByEntity,  Map<String, dynamic>? metadata,  List<String>? warnings)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SyncResult() when $default != null:
return $default(_that.operationId,_that.operationType,_that.isSuccess,_that.startedAt,_that.completedAt,_that.errorMessage,_that.totalItems,_that.processedItems,_that.failedItems,_that.uploadedItems,_that.downloadedItems,_that.conflictItems,_that.itemsByEntity,_that.metadata,_that.warnings);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String operationId,  SyncOperationType operationType,  bool isSuccess,  DateTime startedAt,  DateTime completedAt,  String? errorMessage,  int? totalItems,  int? processedItems,  int? failedItems,  int? uploadedItems,  int? downloadedItems,  int? conflictItems,  Map<String, int>? itemsByEntity,  Map<String, dynamic>? metadata,  List<String>? warnings)  $default,) {final _that = this;
switch (_that) {
case _SyncResult():
return $default(_that.operationId,_that.operationType,_that.isSuccess,_that.startedAt,_that.completedAt,_that.errorMessage,_that.totalItems,_that.processedItems,_that.failedItems,_that.uploadedItems,_that.downloadedItems,_that.conflictItems,_that.itemsByEntity,_that.metadata,_that.warnings);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String operationId,  SyncOperationType operationType,  bool isSuccess,  DateTime startedAt,  DateTime completedAt,  String? errorMessage,  int? totalItems,  int? processedItems,  int? failedItems,  int? uploadedItems,  int? downloadedItems,  int? conflictItems,  Map<String, int>? itemsByEntity,  Map<String, dynamic>? metadata,  List<String>? warnings)?  $default,) {final _that = this;
switch (_that) {
case _SyncResult() when $default != null:
return $default(_that.operationId,_that.operationType,_that.isSuccess,_that.startedAt,_that.completedAt,_that.errorMessage,_that.totalItems,_that.processedItems,_that.failedItems,_that.uploadedItems,_that.downloadedItems,_that.conflictItems,_that.itemsByEntity,_that.metadata,_that.warnings);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SyncResult extends SyncResult {
  const _SyncResult({required this.operationId, required this.operationType, required this.isSuccess, required this.startedAt, required this.completedAt, this.errorMessage, this.totalItems, this.processedItems, this.failedItems, this.uploadedItems, this.downloadedItems, this.conflictItems, final  Map<String, int>? itemsByEntity, final  Map<String, dynamic>? metadata, final  List<String>? warnings}): _itemsByEntity = itemsByEntity,_metadata = metadata,_warnings = warnings,super._();
  factory _SyncResult.fromJson(Map<String, dynamic> json) => _$SyncResultFromJson(json);

@override final  String operationId;
@override final  SyncOperationType operationType;
@override final  bool isSuccess;
@override final  DateTime startedAt;
@override final  DateTime completedAt;
@override final  String? errorMessage;
@override final  int? totalItems;
@override final  int? processedItems;
@override final  int? failedItems;
@override final  int? uploadedItems;
@override final  int? downloadedItems;
@override final  int? conflictItems;
 final  Map<String, int>? _itemsByEntity;
@override Map<String, int>? get itemsByEntity {
  final value = _itemsByEntity;
  if (value == null) return null;
  if (_itemsByEntity is EqualUnmodifiableMapView) return _itemsByEntity;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<String>? _warnings;
@override List<String>? get warnings {
  final value = _warnings;
  if (value == null) return null;
  if (_warnings is EqualUnmodifiableListView) return _warnings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of SyncResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SyncResultCopyWith<_SyncResult> get copyWith => __$SyncResultCopyWithImpl<_SyncResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SyncResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SyncResult&&(identical(other.operationId, operationId) || other.operationId == operationId)&&(identical(other.operationType, operationType) || other.operationType == operationType)&&(identical(other.isSuccess, isSuccess) || other.isSuccess == isSuccess)&&(identical(other.startedAt, startedAt) || other.startedAt == startedAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.totalItems, totalItems) || other.totalItems == totalItems)&&(identical(other.processedItems, processedItems) || other.processedItems == processedItems)&&(identical(other.failedItems, failedItems) || other.failedItems == failedItems)&&(identical(other.uploadedItems, uploadedItems) || other.uploadedItems == uploadedItems)&&(identical(other.downloadedItems, downloadedItems) || other.downloadedItems == downloadedItems)&&(identical(other.conflictItems, conflictItems) || other.conflictItems == conflictItems)&&const DeepCollectionEquality().equals(other._itemsByEntity, _itemsByEntity)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&const DeepCollectionEquality().equals(other._warnings, _warnings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,operationId,operationType,isSuccess,startedAt,completedAt,errorMessage,totalItems,processedItems,failedItems,uploadedItems,downloadedItems,conflictItems,const DeepCollectionEquality().hash(_itemsByEntity),const DeepCollectionEquality().hash(_metadata),const DeepCollectionEquality().hash(_warnings));

@override
String toString() {
  return 'SyncResult(operationId: $operationId, operationType: $operationType, isSuccess: $isSuccess, startedAt: $startedAt, completedAt: $completedAt, errorMessage: $errorMessage, totalItems: $totalItems, processedItems: $processedItems, failedItems: $failedItems, uploadedItems: $uploadedItems, downloadedItems: $downloadedItems, conflictItems: $conflictItems, itemsByEntity: $itemsByEntity, metadata: $metadata, warnings: $warnings)';
}


}

/// @nodoc
abstract mixin class _$SyncResultCopyWith<$Res> implements $SyncResultCopyWith<$Res> {
  factory _$SyncResultCopyWith(_SyncResult value, $Res Function(_SyncResult) _then) = __$SyncResultCopyWithImpl;
@override @useResult
$Res call({
 String operationId, SyncOperationType operationType, bool isSuccess, DateTime startedAt, DateTime completedAt, String? errorMessage, int? totalItems, int? processedItems, int? failedItems, int? uploadedItems, int? downloadedItems, int? conflictItems, Map<String, int>? itemsByEntity, Map<String, dynamic>? metadata, List<String>? warnings
});




}
/// @nodoc
class __$SyncResultCopyWithImpl<$Res>
    implements _$SyncResultCopyWith<$Res> {
  __$SyncResultCopyWithImpl(this._self, this._then);

  final _SyncResult _self;
  final $Res Function(_SyncResult) _then;

/// Create a copy of SyncResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? operationId = null,Object? operationType = null,Object? isSuccess = null,Object? startedAt = null,Object? completedAt = null,Object? errorMessage = freezed,Object? totalItems = freezed,Object? processedItems = freezed,Object? failedItems = freezed,Object? uploadedItems = freezed,Object? downloadedItems = freezed,Object? conflictItems = freezed,Object? itemsByEntity = freezed,Object? metadata = freezed,Object? warnings = freezed,}) {
  return _then(_SyncResult(
operationId: null == operationId ? _self.operationId : operationId // ignore: cast_nullable_to_non_nullable
as String,operationType: null == operationType ? _self.operationType : operationType // ignore: cast_nullable_to_non_nullable
as SyncOperationType,isSuccess: null == isSuccess ? _self.isSuccess : isSuccess // ignore: cast_nullable_to_non_nullable
as bool,startedAt: null == startedAt ? _self.startedAt : startedAt // ignore: cast_nullable_to_non_nullable
as DateTime,completedAt: null == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,totalItems: freezed == totalItems ? _self.totalItems : totalItems // ignore: cast_nullable_to_non_nullable
as int?,processedItems: freezed == processedItems ? _self.processedItems : processedItems // ignore: cast_nullable_to_non_nullable
as int?,failedItems: freezed == failedItems ? _self.failedItems : failedItems // ignore: cast_nullable_to_non_nullable
as int?,uploadedItems: freezed == uploadedItems ? _self.uploadedItems : uploadedItems // ignore: cast_nullable_to_non_nullable
as int?,downloadedItems: freezed == downloadedItems ? _self.downloadedItems : downloadedItems // ignore: cast_nullable_to_non_nullable
as int?,conflictItems: freezed == conflictItems ? _self.conflictItems : conflictItems // ignore: cast_nullable_to_non_nullable
as int?,itemsByEntity: freezed == itemsByEntity ? _self._itemsByEntity : itemsByEntity // ignore: cast_nullable_to_non_nullable
as Map<String, int>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,warnings: freezed == warnings ? _self._warnings : warnings // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}


}

// dart format on

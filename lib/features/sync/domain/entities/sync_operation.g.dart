// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_operation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SyncOperation _$SyncOperationFromJson(Map<String, dynamic> json) =>
    _SyncOperation(
      id: json['id'] as String,
      type: $enumDecode(_$SyncOperationTypeEnumMap, json['type']),
      scheduledAt: DateTime.parse(json['scheduledAt'] as String),
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      priority:
          $enumDecodeNullable(_$SyncPriorityEnumMap, json['priority']) ??
          SyncPriority.normal,
      entityTypes:
          (json['entityTypes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      filters: json['filters'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      errorMessage: json['errorMessage'] as String?,
      isRetry: json['isRetry'] as bool? ?? false,
      retryCount: (json['retryCount'] as num?)?.toInt(),
      maxRetries: (json['maxRetries'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SyncOperationToJson(_SyncOperation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$SyncOperationTypeEnumMap[instance.type]!,
      'scheduledAt': instance.scheduledAt.toIso8601String(),
      'startedAt': instance.startedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'priority': _$SyncPriorityEnumMap[instance.priority]!,
      'entityTypes': instance.entityTypes,
      'filters': instance.filters,
      'metadata': instance.metadata,
      'errorMessage': instance.errorMessage,
      'isRetry': instance.isRetry,
      'retryCount': instance.retryCount,
      'maxRetries': instance.maxRetries,
    };

const _$SyncOperationTypeEnumMap = {
  SyncOperationType.upload: 'upload',
  SyncOperationType.download: 'download',
  SyncOperationType.full: 'full',
  SyncOperationType.incremental: 'incremental',
};

const _$SyncPriorityEnumMap = {
  SyncPriority.low: 'low',
  SyncPriority.normal: 'normal',
  SyncPriority.high: 'high',
  SyncPriority.critical: 'critical',
};

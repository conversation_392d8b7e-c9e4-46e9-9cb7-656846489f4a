// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SyncResult _$SyncResultFromJson(Map<String, dynamic> json) => _SyncResult(
  operationId: json['operationId'] as String,
  operationType: $enumDecode(_$SyncOperationTypeEnumMap, json['operationType']),
  isSuccess: json['isSuccess'] as bool,
  startedAt: DateTime.parse(json['startedAt'] as String),
  completedAt: DateTime.parse(json['completedAt'] as String),
  errorMessage: json['errorMessage'] as String?,
  totalItems: (json['totalItems'] as num?)?.toInt(),
  processedItems: (json['processedItems'] as num?)?.toInt(),
  failedItems: (json['failedItems'] as num?)?.toInt(),
  uploadedItems: (json['uploadedItems'] as num?)?.toInt(),
  downloadedItems: (json['downloadedItems'] as num?)?.toInt(),
  conflictItems: (json['conflictItems'] as num?)?.toInt(),
  itemsByEntity: (json['itemsByEntity'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, (e as num).toInt()),
  ),
  metadata: json['metadata'] as Map<String, dynamic>?,
  warnings: (json['warnings'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$SyncResultToJson(_SyncResult instance) =>
    <String, dynamic>{
      'operationId': instance.operationId,
      'operationType': _$SyncOperationTypeEnumMap[instance.operationType]!,
      'isSuccess': instance.isSuccess,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt.toIso8601String(),
      'errorMessage': instance.errorMessage,
      'totalItems': instance.totalItems,
      'processedItems': instance.processedItems,
      'failedItems': instance.failedItems,
      'uploadedItems': instance.uploadedItems,
      'downloadedItems': instance.downloadedItems,
      'conflictItems': instance.conflictItems,
      'itemsByEntity': instance.itemsByEntity,
      'metadata': instance.metadata,
      'warnings': instance.warnings,
    };

const _$SyncOperationTypeEnumMap = {
  SyncOperationType.upload: 'upload',
  SyncOperationType.download: 'download',
  SyncOperationType.full: 'full',
  SyncOperationType.incremental: 'incremental',
};

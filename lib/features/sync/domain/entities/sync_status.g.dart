// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SyncStatus _$SyncStatusFromJson(Map<String, dynamic> json) => _SyncStatus(
  id: json['id'] as String,
  status: $enumDecode(_$SyncStatusTypeEnumMap, json['status']),
  startedAt: DateTime.parse(json['startedAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  errorMessage: json['errorMessage'] as String?,
  totalItems: (json['totalItems'] as num?)?.toInt(),
  processedItems: (json['processedItems'] as num?)?.toInt(),
  failedItems: (json['failedItems'] as num?)?.toInt(),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$SyncStatusToJson(_SyncStatus instance) =>
    <String, dynamic>{
      'id': instance.id,
      'status': _$SyncStatusTypeEnumMap[instance.status]!,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'errorMessage': instance.errorMessage,
      'totalItems': instance.totalItems,
      'processedItems': instance.processedItems,
      'failedItems': instance.failedItems,
      'metadata': instance.metadata,
    };

const _$SyncStatusTypeEnumMap = {
  SyncStatusType.pending: 'pending',
  SyncStatusType.inProgress: 'inProgress',
  SyncStatusType.completed: 'completed',
  SyncStatusType.failed: 'failed',
  SyncStatusType.cancelled: 'cancelled',
};

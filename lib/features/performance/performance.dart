/// Performance feature barrel export file
///
/// This file provides a single import point for all performance functionality:
/// ```dart
/// import 'package:bidtrakr/features/performance/performance.dart';
/// ```
library;

// Data layer
export 'data/repositories/performance_repository_impl.dart';

// Domain layer
export 'domain/entities/performance.dart';
export 'domain/repositories/performance_repository.dart';
export 'domain/use_cases/calculate_performance_metrics.dart';

// Presentation layer - constants
export 'presentation/constants/performance_constants.dart';

// Presentation layer - providers
export 'presentation/providers/performance_providers.dart';

// Presentation layer - screens
export 'presentation/screens/performance_form_screen.dart';
export 'presentation/screens/performance_screen.dart';

// Presentation layer - widgets
export 'presentation/widgets/empty_performance_view.dart';
export 'presentation/widgets/performance_card.dart';
export 'presentation/widgets/performance_details_sheet.dart';
export 'presentation/widgets/performance_list_item.dart';
export 'presentation/widgets/performance_shimmer_loading.dart';
export 'presentation/widgets/performance_unified_shimmer_loading.dart'
    hide PerformanceDateRangeSelectorShimmer;

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$get14DayMetricsHash() => r'3848b069bf184aeaf7fb0ea25d905ca135e28fa4';

/// See also [get14DayMetrics].
@ProviderFor(get14DayMetrics)
final get14DayMetricsProvider = AutoDisposeProvider<Get14DayMetrics>.internal(
  get14DayMetrics,
  name: r'get14DayMetricsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$get14DayMetricsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef Get14DayMetricsRef = AutoDisposeProviderRef<Get14DayMetrics>;
String _$calculateRetentionHash() =>
    r'9756ad1e7f8a14a081f36f6e70cbdf683e583352';

/// See also [calculateRetention].
@ProviderFor(calculateRetention)
final calculateRetentionProvider =
    AutoDisposeProvider<CalculateRetention>.internal(
      calculateRetention,
      name: r'calculateRetentionProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$calculateRetentionHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CalculateRetentionRef = AutoDisposeProviderRef<CalculateRetention>;
String _$calculatePerformanceMetricsHash() =>
    r'414463701e503bc816a2f906a6d9072ff98e7635';

/// See also [calculatePerformanceMetrics].
@ProviderFor(calculatePerformanceMetrics)
final calculatePerformanceMetricsProvider =
    AutoDisposeProvider<CalculatePerformanceMetrics>.internal(
      calculatePerformanceMetrics,
      name: r'calculatePerformanceMetricsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$calculatePerformanceMetricsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CalculatePerformanceMetricsRef =
    AutoDisposeProviderRef<CalculatePerformanceMetrics>;
String _$getPerformanceListHash() =>
    r'1be0ed6f264930bd0d4b4e70497d68306b38ab02';

/// See also [getPerformanceList].
@ProviderFor(getPerformanceList)
final getPerformanceListProvider =
    AutoDisposeProvider<GetPerformanceList>.internal(
      getPerformanceList,
      name: r'getPerformanceListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$getPerformanceListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GetPerformanceListRef = AutoDisposeProviderRef<GetPerformanceList>;
String _$performanceRepositoryHash() =>
    r'809e7359a0c32eb9b2868c68a068430c51ebcc70';

/// See also [performanceRepository].
@ProviderFor(performanceRepository)
final performanceRepositoryProvider =
    AutoDisposeProvider<PerformanceRepository>.internal(
      performanceRepository,
      name: r'performanceRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PerformanceRepositoryRef =
    AutoDisposeProviderRef<PerformanceRepository>;
String _$performanceListHash() => r'5356865a68cc8494567f6740e39081419e8a63f5';

/// See also [performanceList].
@ProviderFor(performanceList)
final performanceListProvider =
    AutoDisposeFutureProvider<List<Performance>>.internal(
      performanceList,
      name: r'performanceListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PerformanceListRef = AutoDisposeFutureProviderRef<List<Performance>>;
String _$retentionHash() => r'1af43524d0384d49ba3a6e831694f0baedef3dcf';

/// See also [retention].
@ProviderFor(retention)
final retentionProvider = AutoDisposeFutureProvider<double>.internal(
  retention,
  name: r'retentionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$retentionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RetentionRef = AutoDisposeFutureProviderRef<double>;
String _$performanceOperationsHash() =>
    r'574e0f2831e2ee8e5e71ea68e27da115b38b201e';

/// See also [PerformanceOperations].
@ProviderFor(PerformanceOperations)
final performanceOperationsProvider =
    AutoDisposeAsyncNotifierProvider<PerformanceOperations, void>.internal(
      PerformanceOperations.new,
      name: r'performanceOperationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceOperationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PerformanceOperations = AutoDisposeAsyncNotifier<void>;
String _$performanceMetricsDataHash() =>
    r'49ce7d514a9286e2fc27552df930fff87e70c9d7';

/// See also [PerformanceMetricsData].
@ProviderFor(PerformanceMetricsData)
final performanceMetricsDataProvider =
    AutoDisposeAsyncNotifierProvider<
      PerformanceMetricsData,
      PerformanceMetrics
    >.internal(
      PerformanceMetricsData.new,
      name: r'performanceMetricsDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceMetricsDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PerformanceMetricsData = AutoDisposeAsyncNotifier<PerformanceMetrics>;
String _$onlineHoursHash() => r'465dbaae1ae5e824f569de1bc6d244dba8a5b038';

/// See also [OnlineHours].
@ProviderFor(OnlineHours)
final onlineHoursProvider =
    AutoDisposeNotifierProvider<OnlineHours, double>.internal(
      OnlineHours.new,
      name: r'onlineHoursProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$onlineHoursHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OnlineHours = AutoDisposeNotifier<double>;
String _$paginatedPerformanceListHash() =>
    r'eaf2902b187f2149bb372c5ae4515ecd15720110';

/// See also [PaginatedPerformanceList].
@ProviderFor(PaginatedPerformanceList)
final paginatedPerformanceListProvider =
    AutoDisposeAsyncNotifierProvider<
      PaginatedPerformanceList,
      PaginatedResult<Performance>
    >.internal(
      PaginatedPerformanceList.new,
      name: r'paginatedPerformanceListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$paginatedPerformanceListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PaginatedPerformanceList =
    AutoDisposeAsyncNotifier<PaginatedResult<Performance>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../entities/performance.dart';
import '../repositories/performance_repository.dart';

/// Use case to retrieve performance data
class GetPerformanceList {
  final PerformanceRepository repository;

  GetPerformanceList(this.repository);

  /// Get all performance records
  Future<Either<Failure, List<Performance>>> getAll() async {
    return await repository.getAllPerformance();
  }

  /// Get performance records for a specific date range
  Future<Either<Failure, List<Performance>>> getForDateRange(DateTime start, DateTime end) async {
    return await repository.getPerformanceForDateRange(start, end);
  }

  /// Get paginated performance records
  /// [page] is the page number (1-based)
  /// [pageSize] is the number of records per page
  Future<Either<Failure, List<Performance>>> getPaginated({
    required int page,
    required int pageSize,
    DateTime? start,
    DateTime? end,
  }) async {
    return await repository.getPaginated(
      page: page,
      pageSize: pageSize,
      start: start,
      end: end,
    );
  }

  /// Get a specific performance record by id
  Future<Either<Failure, Performance>> getById(int id) async {
    return await repository.getPerformanceById(id);
  }

  /// Check if a performance record already exists for a specific date
  Future<Either<Failure, bool>> checkDateExists(DateTime date, {int? excludeId}) async {
    return await repository.checkDateExists(date, excludeId: excludeId);
  }
}

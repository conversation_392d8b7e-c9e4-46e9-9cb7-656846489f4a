import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/performance.dart';

abstract class PerformanceRepository {
  /// Get all performance records
  Future<Either<Failure, List<Performance>>> getAllPerformance();

  /// Get performance record by id
  Future<Either<Failure, Performance>> getPerformanceById(int id);

  /// Save a new performance record
  Future<Either<Failure, Performance>> savePerformance(Performance performance);

  /// Update an existing performance record
  Future<Either<Failure, Performance>> updatePerformance(
    Performance performance,
  );

  /// Delete a performance record
  Future<Either<Failure, bool>> deletePerformance(int id);

  /// Get performance records for a specific date range
  Future<Either<Failure, List<Performance>>> getPerformanceForDateRange(
    DateTime start,
    DateTime end,
  );

  /// Get paginated performance records
  /// [page] is the page number (1-based)
  /// [pageSize] is the number of records per page
  Future<Either<Failure, List<Performance>>> getPaginated({
    required int page,
    required int pageSize,
    DateTime? start,
    DateTime? end,
  });

  /// Check if a performance record already exists for a specific date
  Future<Either<Failure, bool>> checkDateExists(
    DateTime date, {
    int? excludeId,
  });

  /// Get total completed orders for the last 14 days before a given date
  Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(
    DateTime endDate,
  );
}

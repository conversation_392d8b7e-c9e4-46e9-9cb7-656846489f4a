import 'package:flutter/material.dart';
import '../../../../core/theme/app_dimensions.dart';

/// A card widget for grouping menu items
class MenuCard extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool showBorder;

  const MenuCard({
    super.key,
    required this.children,
    this.margin = const EdgeInsets.only(bottom: 12.0),
    this.borderRadius = 12.0,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin,
      elevation: 0,
      shape: showBorder
          ? RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              side: BorderSide(
                color: Colors.grey.withAlpha(30),
                width: AppDimensions.borderWidth,
              ),
            )
          : RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              side: BorderSide.none,
            ),
      child: <PERSON><PERSON>n(children: children),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/services/auth/auth_logger.dart';
import '../../../../core/services/sync/sync_logger.dart';

/// Enum for log types
enum LogType {
  /// Sync logs
  sync,

  /// Authentication logs
  auth,
}

/// Widget for displaying log filter controls
class LogFilterBar extends ConsumerWidget {
  /// The currently selected log type
  final LogType selectedLogType;

  /// Border color for the filter bar
  final Color? borderColor;
  
  /// Background color for the filter bar
  final Color? backgroundColor;

  /// Constructor
  const LogFilterBar({
    required this.selectedLogType,
    this.borderColor,
    this.backgroundColor,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (selectedLogType == LogType.sync) {
      final logLevelFilter = ref.watch(logLevelFilterProvider);
      return _buildSyncLogLevelSelector(
        context,
        ref,
        logLevelFilter,
        borderColor: borderColor,
      );
    } else {
      final importanceFilter = ref.watch(authLogImportanceFilterProvider);
      return _buildAuthLogImportanceSelector(
        context,
        ref,
        importanceFilter,
        borderColor: borderColor,
      );
    }
  }

  /// Builds the sync log level selector
  Widget _buildSyncLogLevelSelector(
    BuildContext context,
    WidgetRef ref,
    LogLevel logLevelFilter, {
    Color? borderColor,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return InkWell(
      onTap: () => _showSyncLogLevelDialog(context, ref, logLevelFilter),
      borderRadius: BorderRadius.circular(8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: backgroundColor ?? (isDark ? theme.cardColor : Colors.white),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(
            color: borderColor ?? (isDark ? theme.dividerColor : AppColors.primary.withAlpha(77)),
            width: 1.0,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Icon(
              Icons.filter_alt_outlined,  // Using filter icon instead of calendar
              size: 20.0,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Text(
              logLevelFilter.name.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14.0,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the auth log importance selector
  Widget _buildAuthLogImportanceSelector(
    BuildContext context,
    WidgetRef ref,
    AuthLogImportance importanceFilter, {
    Color? borderColor,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return InkWell(
      onTap: () => _showAuthLogImportanceDialog(context, ref, importanceFilter),
      borderRadius: BorderRadius.circular(8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: backgroundColor ?? (isDark ? theme.cardColor : Colors.white),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(
            color: borderColor ?? (isDark ? theme.dividerColor : AppColors.primary.withAlpha(77)),
            width: 1.0,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Icon(
              Icons.filter_alt_outlined,  // Using filter icon instead of calendar
              size: 20.0,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Text(
              importanceFilter.name.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14.0,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shows the sync log level filter dialog
  void _showSyncLogLevelDialog(
    BuildContext context,
    WidgetRef ref,
    LogLevel currentLevel,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Log Level'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: LogLevel.values.map((level) {
            return RadioListTile<LogLevel>(
              title: Text(level.name),
              value: level,
              groupValue: currentLevel,
              onChanged: (LogLevel? value) {
                if (value != null) {
                  ref.read(logLevelFilterProvider.notifier).state = value;
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Shows the auth log importance filter dialog
  void _showAuthLogImportanceDialog(
    BuildContext context,
    WidgetRef ref,
    AuthLogImportance currentImportance,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Importance'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AuthLogImportance.values.map((importance) {
            return RadioListTile<AuthLogImportance>(
              title: Text(importance.name),
              value: importance,
              groupValue: currentImportance,
              onChanged: (AuthLogImportance? value) {
                if (value != null) {
                  ref.read(authLogImportanceFilterProvider.notifier).state =
                      value;
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

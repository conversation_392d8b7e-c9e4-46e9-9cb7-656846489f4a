import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for search query state
final logSearchQueryProvider = StateProvider<String>((ref) => '');

/// Widget for searching through logs
/// Currently a placeholder for future search functionality
class LogSearchBar extends ConsumerWidget {
  /// Constructor
  const LogSearchBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchQuery = ref.watch(logSearchQueryProvider);

    return Container(
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: TextField(
        onChanged: (value) {
          ref.read(logSearchQueryProvider.notifier).state = value;
        },
        decoration: InputDecoration(
          hintText: 'Search logs... (coming soon)',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    ref.read(logSearchQueryProvider.notifier).state = '';
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 12.0,
          ),
        ),
        enabled: false, // Disabled until search functionality is implemented
      ),
    );
  }
}

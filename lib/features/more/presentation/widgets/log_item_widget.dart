import 'package:flutter/material.dart';

import '../../../../core/services/auth/auth_logger.dart';
import '../../../../core/services/sync/sync_logger.dart';
import '../../../../core/theme/app_colors.dart';

/// Widget for displaying individual sync log items
class SyncLogItemWidget extends StatelessWidget {
  /// The log entry to display
  final LogEntry logEntry;

  /// Constructor
  const SyncLogItemWidget({required this.logEntry, super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with timestamp and level
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatTimestamp(logEntry.timestamp),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 2.0,
                  ),
                  decoration: BoxDecoration(
                    color: _getLogLevelColor(logEntry.level),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Text(
                    logEntry.level.name.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            // Log message
            Text(
              logEntry.message,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// Formats the timestamp for display
  String _formatTimestamp(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
        '${timestamp.minute.toString().padLeft(2, '0')}:'
        '${timestamp.second.toString().padLeft(2, '0')}';
  }

  /// Gets the color for the log level badge
  Color _getLogLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return Colors.grey;
      case LogLevel.info:
        return AppColors.primary;
      case LogLevel.warning:
        return Colors.orange;
      case LogLevel.error:
        return Colors.red;
    }
  }
}

/// Widget for displaying individual auth log items
class AuthLogItemWidget extends StatelessWidget {
  /// The auth log entry to display
  final AuthLogEntry logEntry;

  /// Constructor
  const AuthLogItemWidget({required this.logEntry, super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with timestamp and importance
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatTimestamp(logEntry.timestamp),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 2.0,
                  ),
                  decoration: BoxDecoration(
                    color: _getImportanceColor(logEntry.importance),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Text(
                    logEntry.importance.name.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            // Log message
            Text(
              logEntry.message,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// Formats the timestamp for display
  String _formatTimestamp(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
        '${timestamp.minute.toString().padLeft(2, '0')}:'
        '${timestamp.second.toString().padLeft(2, '0')}';
  }

  /// Gets the color for the importance badge
  Color _getImportanceColor(AuthLogImportance importance) {
    switch (importance) {
      case AuthLogImportance.verbose:
        return Colors.grey;
      case AuthLogImportance.important:
        return AppColors.primary;
      case AuthLogImportance.critical:
        return Colors.red;
    }
  }
}

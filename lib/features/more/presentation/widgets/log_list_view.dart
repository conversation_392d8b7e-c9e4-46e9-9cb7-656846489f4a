import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/auth/auth_logger.dart';
import '../../../../core/services/sync/sync_logger.dart';
import 'log_item_widget.dart';

/// Widget for displaying a list of sync logs
class SyncLogListView extends ConsumerWidget {
  /// Constructor
  const SyncLogListView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the new filtered pure sync logs provider that only shows sync logs
    final filteredLogs = ref.watch(filteredPureSyncLogsProvider);

    // Show empty state if no logs
    if (filteredLogs.isEmpty) {
      return const SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.sync_alt,
                size: 64.0,
                color: Colors.grey,
              ),
              Sized<PERSON><PERSON>(height: 16.0),
              Text(
                'No sync logs available',
                style: TextStyle(
                  fontSize: 18.0,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 8.0),
              Text(
                'Sync logs will appear here when sync operations occur',
                style: TextStyle(
                  fontSize: 14.0,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Reverse the logs to show newest first
    final reversedLogs = filteredLogs.reversed.toList();

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final logEntry = reversedLogs[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: SyncLogItemWidget(logEntry: logEntry),
          );
        },
        childCount: reversedLogs.length,
      ),
    );
  }
}

/// Widget for displaying a list of auth logs
class AuthLogListView extends ConsumerWidget {
  /// Constructor
  const AuthLogListView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the new filtered auth logs provider that handles importance filtering
    final filteredLogs = ref.watch(filteredAuthLogsProvider);

    // Show empty state if no logs
    if (filteredLogs.isEmpty) {
      return const SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.security,
                size: 64.0,
                color: Colors.grey,
              ),
              SizedBox(height: 16.0),
              Text(
                'No authentication logs available',
                style: TextStyle(
                  fontSize: 18.0,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 8.0),
              Text(
                'Authentication logs will appear here when auth operations occur',
                style: TextStyle(
                  fontSize: 14.0,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Reverse the logs to show newest first
    final reversedLogs = filteredLogs.reversed.toList();

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final logEntry = reversedLogs[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: AuthLogItemWidget(logEntry: logEntry),
          );
        },
        childCount: reversedLogs.length,
      ),
    );
  }
}

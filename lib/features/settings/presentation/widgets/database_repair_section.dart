import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/datasources/app_database.dart';
import '../../../../core/utils/database_repair_utility.dart';

class DatabaseRepairSection extends ConsumerStatefulWidget {
  final AppDatabase database;

  const DatabaseRepairSection({super.key, required this.database});

  @override
  ConsumerState<DatabaseRepairSection> createState() =>
      _DatabaseRepairSectionState();
}

class _DatabaseRepairSectionState extends ConsumerState<DatabaseRepairSection> {
  bool _isChecking = false;
  bool _isRepairing = false;
  String? _statusMessage;
  bool? _needsRepair;

  late final DatabaseRepairUtility _repairUtility;

  @override
  void initState() {
    super.initState();
    _repairUtility = DatabaseRepairUtility(widget.database);
  }

  Future<void> _checkDatabaseIntegrity() async {
    setState(() {
      _isChecking = true;
      _statusMessage = null;
    });

    try {
      final needsRepair = await _repairUtility.needsRepair();
      final report = await _repairUtility.getDatabaseIssuesReport();

      setState(() {
        _needsRepair = needsRepair;
        _statusMessage = report;
        _isChecking = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking database: $e';
        _isChecking = false;
      });
    }
  }

  Future<void> _repairDatabase() async {
    setState(() {
      _isRepairing = true;
      _statusMessage = null;
    });

    try {
      final success = await _repairUtility.repairDatabase();

      if (success) {
        setState(() {
          _needsRepair = false;
          _statusMessage = 'Database repair completed successfully!';
          _isRepairing = false;
        });

        // Show success snackbar
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Database repaired successfully. You can now sync your data.',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _statusMessage =
              'Database repair failed. Please try again or contact support.';
          _isRepairing = false;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error during repair: $e';
        _isRepairing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.build, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Database Repair',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Check and repair database integrity issues that may cause sync failures.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isChecking || _isRepairing
                      ? null
                      : _checkDatabaseIntegrity,
                  icon: _isChecking
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search),
                  label: Text(_isChecking ? 'Checking...' : 'Check Database'),
                ),
                const SizedBox(width: 12),
                if (_needsRepair == true)
                  ElevatedButton.icon(
                    onPressed: _isRepairing ? null : _repairDatabase,
                    icon: _isRepairing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.build),
                    label: Text(
                      _isRepairing ? 'Repairing...' : 'Repair Database',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),

            // Status message
            if (_statusMessage != null) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _needsRepair == true
                      ? Colors.orange.withValues(alpha: 0.1)
                      : _needsRepair == false
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _needsRepair == true
                        ? Colors.orange
                        : _needsRepair == false
                        ? Colors.green
                        : Colors.grey,
                  ),
                ),
                child: Text(
                  _statusMessage!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _needsRepair == true
                        ? Colors.orange.shade800
                        : _needsRepair == false
                        ? Colors.green.shade800
                        : Colors.grey.shade800,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

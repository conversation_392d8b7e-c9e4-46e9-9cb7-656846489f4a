import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/providers/provider_templates.dart';
import '../../../../core/repositories/app_settings_repository.dart';
import '../../data/repositories/settings_repository_impl.dart';
import '../../domain/repositories/settings_repository.dart';
import '../../domain/use_cases/get_app_settings.dart';
import '../../domain/use_cases/reset_settings.dart';
import '../../domain/use_cases/update_app_settings.dart';

/// Provider for the core app settings repository
final coreAppSettingsRepositoryProvider = Provider<AppSettingsRepository>((
  ref,
) {
  // This would typically be injected from the core layer
  throw UnimplementedError('Core app settings repository should be provided');
});

/// Provider for the settings repository
final settingsRepositoryProvider = Provider<SettingsRepository>((ref) {
  final coreRepository = ref.watch(coreAppSettingsRepositoryProvider);
  return SettingsRepositoryImpl(coreRepository);
});

/// Provider for the get app settings use case
final getAppSettingsProvider = Provider<GetAppSettings>((ref) {
  return createUseCaseProvider<GetAppSettings, SettingsRepository>(
    ref,
    settingsRepositoryProvider,
    (repository) => GetAppSettings(repository),
  );
});

/// Provider for the update app settings use case
final updateAppSettingsProvider = Provider<UpdateAppSettings>((ref) {
  return createUseCaseProvider<UpdateAppSettings, SettingsRepository>(
    ref,
    settingsRepositoryProvider,
    (repository) => UpdateAppSettings(repository),
  );
});

/// Provider for the reset settings use case
final resetSettingsProvider = Provider<ResetSettings>((ref) {
  return createUseCaseProvider<ResetSettings, SettingsRepository>(
    ref,
    settingsRepositoryProvider,
    (repository) => ResetSettings(repository),
  );
});

/// Provider for current app settings (async)
final appSettingsProvider = FutureProvider((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getOrCreate();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (settings) => settings,
  );
});

/// Provider for date range settings (async)
final dateRangeSettingsProvider = FutureProvider((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getDateRangeSettings();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (settings) => settings,
  );
});

/// Provider for backup settings (async)
final backupSettingsProvider = FutureProvider((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getBackupSettings();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (settings) => settings,
  );
});

/// Provider for sync enabled status (async)
final syncEnabledProvider = FutureProvider<bool>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.isSyncEnabled();
  return result.fold((failure) => false, (enabled) => enabled);
});

/// Provider for notifications enabled status (async)
final notificationsEnabledProvider = FutureProvider<bool>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.areNotificationsEnabled();
  return result.fold((failure) => true, (enabled) => enabled);
});

/// Provider for dark mode enabled status (async)
final darkModeEnabledProvider = FutureProvider<bool>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.isDarkModeEnabled();
  return result.fold((failure) => false, (enabled) => enabled);
});

/// Provider for current language (async)
final currentLanguageProvider = FutureProvider<String>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getLanguage();
  return result.fold((failure) => 'en', (language) => language);
});

/// Provider for current currency (async)
final currentCurrencyProvider = FutureProvider<String>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getCurrency();
  return result.fold((failure) => 'USD', (currency) => currency);
});

/// Provider for backup directory path (async)
final backupDirectoryPathProvider = FutureProvider<String?>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getBackupDirectoryPath();
  return result.fold((failure) => null, (path) => path);
});

/// Provider for last sync time (async)
final lastSyncTimeProvider = FutureProvider<DateTime?>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getLastSyncTime();
  return result.fold((failure) => null, (time) => time);
});

/// Provider for settings status (async)
final settingsStatusProvider = FutureProvider((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getStatus();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (status) => status,
  );
});

/// Provider for settings attention status (async)
final settingsNeedsAttentionProvider = FutureProvider<bool>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.needsAttention();
  return result.fold((failure) => false, (needsAttention) => needsAttention);
});

/// Provider for settings attention issues (async)
final settingsAttentionIssuesProvider = FutureProvider<List<String>>((
  ref,
) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getAttentionIssues();
  return result.fold((failure) => [], (issues) => issues);
});

/// Provider for specific additional setting (family)
final additionalSettingProvider = FutureProvider.family<dynamic, String>((
  ref,
  key,
) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.getAdditionalSetting(key);
  return result.fold((failure) => null, (value) => value);
});

/// Provider for settings validation status (async)
final settingsValidationProvider = FutureProvider<bool>((ref) async {
  final getAppSettings = ref.watch(getAppSettingsProvider);
  final result = await getAppSettings.validateSettings();
  return result.fold((failure) => false, (isValid) => isValid);
});

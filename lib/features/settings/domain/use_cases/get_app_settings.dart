import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/app_settings.dart';
import '../entities/backup_settings.dart';
import '../entities/date_range_settings.dart';
import '../repositories/settings_repository.dart';

/// Use case for getting application settings
class GetAppSettings {
  final SettingsRepository repository;

  GetAppSettings(this.repository);

  /// Get current app settings
  Future<Either<Failure, AppSettings>> execute() async {
    return await repository.getAppSettings();
  }

  /// Get or create default app settings
  Future<Either<Failure, AppSettings>> getOrCreate() async {
    return await repository.getOrCreateAppSettings();
  }

  /// Get date range settings
  Future<Either<Failure, DateRangeSettings>> getDateRangeSettings() async {
    return await repository.getDateRangeSettings();
  }

  /// Get backup settings
  Future<Either<Failure, BackupSettings>> getBackupSettings() async {
    return await repository.getBackupSettings();
  }

  /// Get specific setting value by key
  Future<Either<Failure, T?>> getAdditionalSetting<T>(String key) async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) {
        final value = settings.getAdditionalSetting<T>(key);
        return Right(value);
      },
    );
  }

  /// Check if sync is enabled
  Future<Either<Failure, bool>> isSyncEnabled() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.syncEnabled),
    );
  }

  /// Check if notifications are enabled
  Future<Either<Failure, bool>> areNotificationsEnabled() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.notificationsEnabled),
    );
  }

  /// Check if dark mode is enabled
  Future<Either<Failure, bool>> isDarkModeEnabled() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.darkModeEnabled),
    );
  }

  /// Get current language
  Future<Either<Failure, String>> getLanguage() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.language),
    );
  }

  /// Get current currency
  Future<Either<Failure, String>> getCurrency() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.currency),
    );
  }

  /// Get backup directory path
  Future<Either<Failure, String?>> getBackupDirectoryPath() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.backupDirectoryPath),
    );
  }

  /// Get last sync time
  Future<Either<Failure, DateTime?>> getLastSyncTime() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.lastSyncTime),
    );
  }

  /// Check if settings need attention
  Future<Either<Failure, bool>> needsAttention() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.needsAttention),
    );
  }

  /// Get list of attention issues
  Future<Either<Failure, List<String>>> getAttentionIssues() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => Right(settings.attentionIssues),
    );
  }

  /// Export settings to JSON
  Future<Either<Failure, Map<String, dynamic>>> exportSettings() async {
    return await repository.exportSettings();
  }

  /// Validate current settings
  Future<Either<Failure, bool>> validateSettings() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) => repository.validateSettings(settings),
    );
  }

  /// Get comprehensive settings status
  Future<Either<Failure, SettingsStatus>> getStatus() async {
    final settingsResult = await repository.getAppSettings();
    
    return settingsResult.fold(
      (failure) => Left(failure),
      (settings) async {
        final dateRangeResult = await getDateRangeSettings();
        final backupResult = await getBackupSettings();
        
        return dateRangeResult.fold(
          (failure) => Left(failure),
          (dateRange) {
            return backupResult.fold(
              (failure) => Left(failure),
              (backup) {
                return Right(SettingsStatus(
                  appSettings: settings,
                  dateRangeSettings: dateRange,
                  backupSettings: backup,
                  needsAttention: settings.needsAttention || backup.needsAttention,
                  attentionIssues: [
                    ...settings.attentionIssues,
                    ...backup.attentionIssues,
                  ],
                ));
              },
            );
          },
        );
      },
    );
  }
}

/// Comprehensive settings status
class SettingsStatus {
  final AppSettings appSettings;
  final DateRangeSettings dateRangeSettings;
  final BackupSettings backupSettings;
  final bool needsAttention;
  final List<String> attentionIssues;

  const SettingsStatus({
    required this.appSettings,
    required this.dateRangeSettings,
    required this.backupSettings,
    required this.needsAttention,
    required this.attentionIssues,
  });

  /// Check if any critical issues exist
  bool get hasCriticalIssues {
    return attentionIssues.any((issue) => 
      issue.contains('disabled') || 
      issue.contains('not configured') ||
      issue.contains('Never')
    );
  }

  /// Get count of attention issues
  int get attentionIssueCount => attentionIssues.length;

  /// Check if settings are properly configured
  bool get isProperlyConfigured {
    return !needsAttention && 
           appSettings.hasBackupDirectory && 
           appSettings.syncEnabled &&
           backupSettings.enabled;
  }
}

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import '../../../../core/errors/failures.dart';
import '../entities/app_settings.dart';
import '../entities/backup_settings.dart';
import '../entities/date_range_settings.dart';
import '../repositories/settings_repository.dart';

/// Use case for updating application settings
class UpdateAppSettings {
  final SettingsRepository repository;

  UpdateAppSettings(this.repository);

  /// Update complete app settings
  Future<Either<Failure, AppSettings>> execute(AppSettings settings) async {
    // Validate settings before updating
    final validationResult = await repository.validateSettings(settings);

    return validationResult.fold((failure) => Left(failure), (isValid) async {
      if (!isValid) {
        return Left(Failure.invalidInput(message: 'Invalid settings provided'));
      }

      return await repository.updateAppSettings(settings);
    });
  }

  /// Update date range
  Future<Either<Failure, AppSettings>> updateDateRange(
    DateTimeRange dateRange,
  ) async {
    // Validate date range
    if (dateRange.start.isAfter(dateRange.end)) {
      return Left(
        Failure.invalidInput(message: 'Start date cannot be after end date'),
      );
    }

    final now = DateTime.now();
    if (dateRange.end.isAfter(now.add(const Duration(days: 365)))) {
      return Left(
        Failure.invalidInput(
          message: 'End date cannot be more than a year in the future',
        ),
      );
    }

    return await repository.updateDateRange(dateRange);
  }

  /// Update date range settings
  Future<Either<Failure, DateRangeSettings>> updateDateRangeSettings(
    DateRangeSettings settings,
  ) async {
    // Validate date range settings
    if (!settings.isValid) {
      return Left(Failure.invalidInput(message: 'Invalid date range settings'));
    }

    return await repository.updateDateRangeSettings(settings);
  }

  /// Update backup settings
  Future<Either<Failure, BackupSettings>> updateBackupSettings(
    BackupSettings settings,
  ) async {
    // Validate backup settings
    if (settings.enabled && !settings.hasDirectory) {
      return Left(
        Failure.invalidInput(
          message: 'Backup directory must be configured when backup is enabled',
        ),
      );
    }

    if (settings.maxBackupCount < 1) {
      return Left(
        Failure.invalidInput(message: 'Maximum backup count must be at least 1'),
      );
    }

    return await repository.updateBackupSettings(settings);
  }

  /// Update backup directory path
  Future<Either<Failure, AppSettings>> updateBackupDirectoryPath(
    String? path,
  ) async {
    // Validate path if provided
    if (path != null && path.trim().isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Backup directory path cannot be empty'),
      );
    }

    return await repository.updateBackupDirectoryPath(path?.trim());
  }

  /// Update last sync time
  Future<Either<Failure, AppSettings>> updateLastSyncTime(
    DateTime syncTime,
  ) async {
    // Validate sync time is not in the future
    if (syncTime.isAfter(DateTime.now().add(const Duration(minutes: 5)))) {
      return Left(
        Failure.invalidInput(message: 'Sync time cannot be in the future'),
      );
    }

    return await repository.updateLastSyncTime(syncTime);
  }

  /// Enable or disable sync
  Future<Either<Failure, AppSettings>> setSyncEnabled(bool enabled) async {
    return await repository.setSyncEnabled(enabled);
  }

  /// Enable or disable notifications
  Future<Either<Failure, AppSettings>> setNotificationsEnabled(
    bool enabled,
  ) async {
    return await repository.setNotificationsEnabled(enabled);
  }

  /// Enable or disable dark mode
  Future<Either<Failure, AppSettings>> setDarkModeEnabled(bool enabled) async {
    return await repository.setDarkModeEnabled(enabled);
  }

  /// Update language setting
  Future<Either<Failure, AppSettings>> updateLanguage(String language) async {
    // Validate language code
    if (language.trim().isEmpty) {
      return Left(Failure.invalidInput(message: 'Language code cannot be empty'));
    }

    // Basic validation for language code format
    final languageCode = language.trim().toLowerCase();
    if (!RegExp(r'^[a-z]{2}(-[A-Z]{2})?$').hasMatch(languageCode)) {
      return Left(Failure.invalidInput(message: 'Invalid language code format'));
    }

    return await repository.updateLanguage(languageCode);
  }

  /// Update currency setting
  Future<Either<Failure, AppSettings>> updateCurrency(String currency) async {
    // Validate currency code
    if (currency.trim().isEmpty) {
      return Left(Failure.invalidInput(message: 'Currency code cannot be empty'));
    }

    // Basic validation for currency code format (3 letter ISO code)
    final currencyCode = currency.trim().toUpperCase();
    if (!RegExp(r'^[A-Z]{3}$').hasMatch(currencyCode)) {
      return Left(Failure.invalidInput(message: 'Invalid currency code format'));
    }

    return await repository.updateCurrency(currencyCode);
  }

  /// Update additional setting
  Future<Either<Failure, AppSettings>> updateAdditionalSetting(
    String key,
    dynamic value,
  ) async {
    // Validate key
    if (key.trim().isEmpty) {
      return Left(Failure.invalidInput(message: 'Setting key cannot be empty'));
    }

    return await repository.updateAdditionalSetting(key.trim(), value);
  }

  /// Remove additional setting
  Future<Either<Failure, AppSettings>> removeAdditionalSetting(
    String key,
  ) async {
    // Validate key
    if (key.trim().isEmpty) {
      return Left(Failure.invalidInput(message: 'Setting key cannot be empty'));
    }

    return await repository.removeAdditionalSetting(key.trim());
  }

  /// Reset settings to defaults
  Future<Either<Failure, AppSettings>> resetToDefaults() async {
    return await repository.resetToDefaults();
  }

  /// Import settings from JSON
  Future<Either<Failure, AppSettings>> importSettings(
    Map<String, dynamic> settingsJson,
  ) async {
    // Validate JSON structure
    if (settingsJson.isEmpty) {
      return Left(Failure.invalidInput(message: 'Settings JSON cannot be empty'));
    }

    // Check for required fields
    final requiredFields = ['dateRangeStart', 'dateRangeEnd', 'updatedAt'];
    for (final field in requiredFields) {
      if (!settingsJson.containsKey(field)) {
        return Left(
          Failure.invalidInput(message: 'Missing required field: $field'),
        );
      }
    }

    return await repository.importSettings(settingsJson);
  }

  /// Update multiple settings at once
  Future<Either<Failure, AppSettings>> updateMultipleSettings({
    DateTimeRange? dateRange,
    String? backupDirectoryPath,
    bool? syncEnabled,
    bool? notificationsEnabled,
    bool? darkModeEnabled,
    String? language,
    String? currency,
    Map<String, dynamic>? additionalSettings,
  }) async {
    // Get current settings
    final currentSettingsResult = await repository.getAppSettings();

    return currentSettingsResult.fold((failure) => Left(failure), (
      currentSettings,
    ) async {
      var updatedSettings = currentSettings;

      // Update date range if provided
      if (dateRange != null) {
        final dateRangeResult = await updateDateRange(dateRange);
        if (dateRangeResult.isLeft()) {
          return dateRangeResult;
        }
        updatedSettings = dateRangeResult.getOrElse(() => updatedSettings);
      }

      // Update backup directory if provided
      if (backupDirectoryPath != null) {
        final backupResult = await updateBackupDirectoryPath(
          backupDirectoryPath,
        );
        if (backupResult.isLeft()) {
          return backupResult;
        }
        updatedSettings = backupResult.getOrElse(() => updatedSettings);
      }

      // Update other settings
      if (syncEnabled != null) {
        updatedSettings = updatedSettings.copyWith(syncEnabled: syncEnabled);
      }

      if (notificationsEnabled != null) {
        updatedSettings = updatedSettings.copyWith(
          notificationsEnabled: notificationsEnabled,
        );
      }

      if (darkModeEnabled != null) {
        updatedSettings = updatedSettings.copyWith(
          darkModeEnabled: darkModeEnabled,
        );
      }

      if (language != null) {
        final languageResult = await updateLanguage(language);
        if (languageResult.isLeft()) {
          return languageResult;
        }
        updatedSettings = languageResult.getOrElse(() => updatedSettings);
      }

      if (currency != null) {
        final currencyResult = await updateCurrency(currency);
        if (currencyResult.isLeft()) {
          return currencyResult;
        }
        updatedSettings = currencyResult.getOrElse(() => updatedSettings);
      }

      // Update additional settings if provided
      if (additionalSettings != null) {
        final currentAdditional = updatedSettings.additionalSettings ?? {};
        final mergedAdditional = {...currentAdditional, ...additionalSettings};
        updatedSettings = updatedSettings.copyWith(
          additionalSettings: mergedAdditional,
        );
      }

      // Update timestamp
      updatedSettings = updatedSettings.copyWith(
        updatedAt: DateTime.now().toUtc(),
      );

      // Save the updated settings
      return await repository.updateAppSettings(updatedSettings);
    });
  }
}

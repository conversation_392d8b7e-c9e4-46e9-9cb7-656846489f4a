import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/app_settings.dart';
import '../entities/backup_settings.dart';
import '../entities/date_range_settings.dart';
import '../repositories/settings_repository.dart';

/// Use case for resetting application settings
class ResetSettings {
  final SettingsRepository repository;

  ResetSettings(this.repository);

  /// Reset all settings to defaults
  Future<Either<Failure, AppSettings>> resetAll() async {
    return await repository.resetToDefaults();
  }

  /// Reset only date range settings to current month
  Future<Either<Failure, DateRangeSettings>> resetDateRange() async {
    final defaultDateRange = DateRangeSettings.currentMonth();
    return await repository.updateDateRangeSettings(defaultDateRange);
  }

  /// Reset only backup settings to defaults
  Future<Either<Failure, BackupSettings>> resetBackupSettings() async {
    final defaultBackupSettings = BackupSettings.defaultSettings();
    return await repository.updateBackupSettings(defaultBackupSettings);
  }

  /// Reset sync settings to defaults
  Future<Either<Failure, AppSettings>> resetSyncSettings() async {
    final currentSettingsResult = await repository.getAppSettings();

    return currentSettingsResult.fold((failure) => Left(failure), (
      currentSettings,
    ) async {
      final resetSettings = currentSettings.copyWith(
        syncEnabled: true,
        lastSyncTime: null,
        updatedAt: DateTime.now().toUtc(),
      );

      return await repository.updateAppSettings(resetSettings);
    });
  }

  /// Reset UI preferences to defaults
  Future<Either<Failure, AppSettings>> resetUIPreferences() async {
    final currentSettingsResult = await repository.getAppSettings();

    return currentSettingsResult.fold((failure) => Left(failure), (
      currentSettings,
    ) async {
      final resetSettings = currentSettings.copyWith(
        darkModeEnabled: false,
        notificationsEnabled: true,
        language: 'en',
        updatedAt: DateTime.now().toUtc(),
      );

      return await repository.updateAppSettings(resetSettings);
    });
  }

  /// Reset localization settings to defaults
  Future<Either<Failure, AppSettings>> resetLocalizationSettings() async {
    final currentSettingsResult = await repository.getAppSettings();

    return currentSettingsResult.fold((failure) => Left(failure), (
      currentSettings,
    ) async {
      final resetSettings = currentSettings.copyWith(
        language: 'en',
        currency: 'USD',
        updatedAt: DateTime.now().toUtc(),
      );

      return await repository.updateAppSettings(resetSettings);
    });
  }

  /// Clear all additional settings
  Future<Either<Failure, AppSettings>> clearAdditionalSettings() async {
    final currentSettingsResult = await repository.getAppSettings();

    return currentSettingsResult.fold((failure) => Left(failure), (
      currentSettings,
    ) async {
      final resetSettings = currentSettings.copyWith(
        additionalSettings: <String, dynamic>{},
        updatedAt: DateTime.now().toUtc(),
      );

      return await repository.updateAppSettings(resetSettings);
    });
  }

  /// Reset specific setting category
  Future<Either<Failure, AppSettings>> resetCategory(
    SettingsCategory category,
  ) async {
    switch (category) {
      case SettingsCategory.dateRange:
        final dateRangeResult = await resetDateRange();
        return dateRangeResult.fold(
          (failure) => Left(failure),
          (dateRange) => repository.getAppSettings(),
        );

      case SettingsCategory.backup:
        final backupResult = await resetBackupSettings();
        return backupResult.fold(
          (failure) => Left(failure),
          (backup) => repository.getAppSettings(),
        );

      case SettingsCategory.sync:
        return await resetSyncSettings();

      case SettingsCategory.ui:
        return await resetUIPreferences();

      case SettingsCategory.localization:
        return await resetLocalizationSettings();

      case SettingsCategory.additional:
        return await clearAdditionalSettings();

      case SettingsCategory.all:
        return await resetAll();
    }
  }

  /// Reset settings with confirmation
  Future<Either<Failure, ResetResult>> resetWithConfirmation({
    required SettingsCategory category,
    bool requireConfirmation = true,
  }) async {
    if (requireConfirmation) {
      // In a real implementation, this would show a confirmation dialog
      // For now, we'll assume confirmation is given
    }

    final resetResult = await resetCategory(category);

    return resetResult.fold((failure) => Left(failure), (settings) {
      return Right(
        ResetResult(
          category: category,
          success: true,
          settings: settings,
          resetAt: DateTime.now(),
        ),
      );
    });
  }

  /// Get reset preview (what would be reset)
  Future<Either<Failure, ResetPreview>> getResetPreview(
    SettingsCategory category,
  ) async {
    final currentSettingsResult = await repository.getAppSettings();

    return currentSettingsResult.fold((failure) => Left(failure), (
      currentSettings,
    ) async {
      final changes = <String, ResetChange>{};

      switch (category) {
        case SettingsCategory.dateRange:
          final defaultDateRange = DateRangeSettings.currentMonth();
          changes['dateRangeStart'] = ResetChange(
            field: 'dateRangeStart',
            currentValue: currentSettings.dateRangeStart,
            newValue: defaultDateRange.startDate,
          );
          changes['dateRangeEnd'] = ResetChange(
            field: 'dateRangeEnd',
            currentValue: currentSettings.dateRangeEnd,
            newValue: defaultDateRange.endDate,
          );
          break;

        case SettingsCategory.backup:
          changes['backupDirectoryPath'] = ResetChange(
            field: 'backupDirectoryPath',
            currentValue: currentSettings.backupDirectoryPath,
            newValue: null,
          );
          break;

        case SettingsCategory.sync:
          changes['syncEnabled'] = ResetChange(
            field: 'syncEnabled',
            currentValue: currentSettings.syncEnabled,
            newValue: true,
          );
          changes['lastSyncTime'] = ResetChange(
            field: 'lastSyncTime',
            currentValue: currentSettings.lastSyncTime,
            newValue: null,
          );
          break;

        case SettingsCategory.ui:
          changes['darkModeEnabled'] = ResetChange(
            field: 'darkModeEnabled',
            currentValue: currentSettings.darkModeEnabled,
            newValue: false,
          );
          changes['notificationsEnabled'] = ResetChange(
            field: 'notificationsEnabled',
            currentValue: currentSettings.notificationsEnabled,
            newValue: true,
          );
          break;

        case SettingsCategory.localization:
          changes['language'] = ResetChange(
            field: 'language',
            currentValue: currentSettings.language,
            newValue: 'en',
          );
          changes['currency'] = ResetChange(
            field: 'currency',
            currentValue: currentSettings.currency,
            newValue: 'USD',
          );
          break;

        case SettingsCategory.additional:
          changes['additionalSettings'] = ResetChange(
            field: 'additionalSettings',
            currentValue: currentSettings.additionalSettings,
            newValue: <String, dynamic>{},
          );
          break;

        case SettingsCategory.all:
          final defaultSettings = AppSettings.defaultSettings();
          // Add all changes for all categories
          changes.addAll({
            'dateRangeStart': ResetChange(
              field: 'dateRangeStart',
              currentValue: currentSettings.dateRangeStart,
              newValue: defaultSettings.dateRangeStart,
            ),
            'dateRangeEnd': ResetChange(
              field: 'dateRangeEnd',
              currentValue: currentSettings.dateRangeEnd,
              newValue: defaultSettings.dateRangeEnd,
            ),
            'syncEnabled': ResetChange(
              field: 'syncEnabled',
              currentValue: currentSettings.syncEnabled,
              newValue: defaultSettings.syncEnabled,
            ),
            'darkModeEnabled': ResetChange(
              field: 'darkModeEnabled',
              currentValue: currentSettings.darkModeEnabled,
              newValue: defaultSettings.darkModeEnabled,
            ),
            'language': ResetChange(
              field: 'language',
              currentValue: currentSettings.language,
              newValue: defaultSettings.language,
            ),
            'currency': ResetChange(
              field: 'currency',
              currentValue: currentSettings.currency,
              newValue: defaultSettings.currency,
            ),
          });
          break;
      }

      return Right(
        ResetPreview(
          category: category,
          changes: changes,
          affectedFieldCount: changes.length,
        ),
      );
    });
  }
}

/// Settings categories that can be reset
enum SettingsCategory {
  dateRange,
  backup,
  sync,
  ui,
  localization,
  additional,
  all,
}

/// Result of a reset operation
class ResetResult {
  final SettingsCategory category;
  final bool success;
  final AppSettings settings;
  final DateTime resetAt;

  const ResetResult({
    required this.category,
    required this.success,
    required this.settings,
    required this.resetAt,
  });
}

/// Preview of what would be reset
class ResetPreview {
  final SettingsCategory category;
  final Map<String, ResetChange> changes;
  final int affectedFieldCount;

  const ResetPreview({
    required this.category,
    required this.changes,
    required this.affectedFieldCount,
  });

  /// Check if any changes would be made
  bool get hasChanges => changes.isNotEmpty;

  /// Get list of fields that would change
  List<String> get changedFields => changes.keys.toList();
}

/// Individual field change in a reset operation
class ResetChange {
  final String field;
  final dynamic currentValue;
  final dynamic newValue;

  const ResetChange({
    required this.field,
    required this.currentValue,
    required this.newValue,
  });

  /// Check if the value would actually change
  bool get willChange => currentValue != newValue;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backup_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BackupSettings _$BackupSettingsFromJson(Map<String, dynamic> json) =>
    _BackupSettings(
      directoryPath: json['directoryPath'] as String?,
      frequency:
          $enumDecodeNullable(_$BackupFrequencyEnumMap, json['frequency']) ??
          BackupFrequency.manual,
      format:
          $enumDecodeNullable(_$BackupFormatEnumMap, json['format']) ??
          BackupFormat.json,
      includeImages: json['includeImages'] as bool? ?? true,
      compressBackups: json['compressBackups'] as bool? ?? true,
      maxBackupCount: (json['maxBackupCount'] as num?)?.toInt() ?? 5,
      lastBackupTime: json['lastBackupTime'] == null
          ? null
          : DateTime.parse(json['lastBackupTime'] as String),
      nextScheduledBackup: json['nextScheduledBackup'] == null
          ? null
          : DateTime.parse(json['nextScheduledBackup'] as String),
      enabled: json['enabled'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$BackupSettingsToJson(_BackupSettings instance) =>
    <String, dynamic>{
      'directoryPath': instance.directoryPath,
      'frequency': _$BackupFrequencyEnumMap[instance.frequency]!,
      'format': _$BackupFormatEnumMap[instance.format]!,
      'includeImages': instance.includeImages,
      'compressBackups': instance.compressBackups,
      'maxBackupCount': instance.maxBackupCount,
      'lastBackupTime': instance.lastBackupTime?.toIso8601String(),
      'nextScheduledBackup': instance.nextScheduledBackup?.toIso8601String(),
      'enabled': instance.enabled,
      'metadata': instance.metadata,
    };

const _$BackupFrequencyEnumMap = {
  BackupFrequency.manual: 'manual',
  BackupFrequency.daily: 'daily',
  BackupFrequency.weekly: 'weekly',
  BackupFrequency.monthly: 'monthly',
};

const _$BackupFormatEnumMap = {
  BackupFormat.json: 'json',
  BackupFormat.csv: 'csv',
  BackupFormat.sqlite: 'sqlite',
};

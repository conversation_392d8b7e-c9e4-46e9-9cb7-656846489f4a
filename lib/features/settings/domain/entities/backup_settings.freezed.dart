// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'backup_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BackupSettings {

 String? get directoryPath; BackupFrequency get frequency; BackupFormat get format; bool get includeImages; bool get compressBackups; int get maxBackupCount; DateTime? get lastBackupTime; DateTime? get nextScheduledBackup; bool get enabled; Map<String, dynamic>? get metadata;
/// Create a copy of BackupSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BackupSettingsCopyWith<BackupSettings> get copyWith => _$BackupSettingsCopyWithImpl<BackupSettings>(this as BackupSettings, _$identity);

  /// Serializes this BackupSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BackupSettings&&(identical(other.directoryPath, directoryPath) || other.directoryPath == directoryPath)&&(identical(other.frequency, frequency) || other.frequency == frequency)&&(identical(other.format, format) || other.format == format)&&(identical(other.includeImages, includeImages) || other.includeImages == includeImages)&&(identical(other.compressBackups, compressBackups) || other.compressBackups == compressBackups)&&(identical(other.maxBackupCount, maxBackupCount) || other.maxBackupCount == maxBackupCount)&&(identical(other.lastBackupTime, lastBackupTime) || other.lastBackupTime == lastBackupTime)&&(identical(other.nextScheduledBackup, nextScheduledBackup) || other.nextScheduledBackup == nextScheduledBackup)&&(identical(other.enabled, enabled) || other.enabled == enabled)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,directoryPath,frequency,format,includeImages,compressBackups,maxBackupCount,lastBackupTime,nextScheduledBackup,enabled,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'BackupSettings(directoryPath: $directoryPath, frequency: $frequency, format: $format, includeImages: $includeImages, compressBackups: $compressBackups, maxBackupCount: $maxBackupCount, lastBackupTime: $lastBackupTime, nextScheduledBackup: $nextScheduledBackup, enabled: $enabled, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $BackupSettingsCopyWith<$Res>  {
  factory $BackupSettingsCopyWith(BackupSettings value, $Res Function(BackupSettings) _then) = _$BackupSettingsCopyWithImpl;
@useResult
$Res call({
 String? directoryPath, BackupFrequency frequency, BackupFormat format, bool includeImages, bool compressBackups, int maxBackupCount, DateTime? lastBackupTime, DateTime? nextScheduledBackup, bool enabled, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$BackupSettingsCopyWithImpl<$Res>
    implements $BackupSettingsCopyWith<$Res> {
  _$BackupSettingsCopyWithImpl(this._self, this._then);

  final BackupSettings _self;
  final $Res Function(BackupSettings) _then;

/// Create a copy of BackupSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? directoryPath = freezed,Object? frequency = null,Object? format = null,Object? includeImages = null,Object? compressBackups = null,Object? maxBackupCount = null,Object? lastBackupTime = freezed,Object? nextScheduledBackup = freezed,Object? enabled = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
directoryPath: freezed == directoryPath ? _self.directoryPath : directoryPath // ignore: cast_nullable_to_non_nullable
as String?,frequency: null == frequency ? _self.frequency : frequency // ignore: cast_nullable_to_non_nullable
as BackupFrequency,format: null == format ? _self.format : format // ignore: cast_nullable_to_non_nullable
as BackupFormat,includeImages: null == includeImages ? _self.includeImages : includeImages // ignore: cast_nullable_to_non_nullable
as bool,compressBackups: null == compressBackups ? _self.compressBackups : compressBackups // ignore: cast_nullable_to_non_nullable
as bool,maxBackupCount: null == maxBackupCount ? _self.maxBackupCount : maxBackupCount // ignore: cast_nullable_to_non_nullable
as int,lastBackupTime: freezed == lastBackupTime ? _self.lastBackupTime : lastBackupTime // ignore: cast_nullable_to_non_nullable
as DateTime?,nextScheduledBackup: freezed == nextScheduledBackup ? _self.nextScheduledBackup : nextScheduledBackup // ignore: cast_nullable_to_non_nullable
as DateTime?,enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [BackupSettings].
extension BackupSettingsPatterns on BackupSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BackupSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BackupSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BackupSettings value)  $default,){
final _that = this;
switch (_that) {
case _BackupSettings():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BackupSettings value)?  $default,){
final _that = this;
switch (_that) {
case _BackupSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? directoryPath,  BackupFrequency frequency,  BackupFormat format,  bool includeImages,  bool compressBackups,  int maxBackupCount,  DateTime? lastBackupTime,  DateTime? nextScheduledBackup,  bool enabled,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BackupSettings() when $default != null:
return $default(_that.directoryPath,_that.frequency,_that.format,_that.includeImages,_that.compressBackups,_that.maxBackupCount,_that.lastBackupTime,_that.nextScheduledBackup,_that.enabled,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? directoryPath,  BackupFrequency frequency,  BackupFormat format,  bool includeImages,  bool compressBackups,  int maxBackupCount,  DateTime? lastBackupTime,  DateTime? nextScheduledBackup,  bool enabled,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _BackupSettings():
return $default(_that.directoryPath,_that.frequency,_that.format,_that.includeImages,_that.compressBackups,_that.maxBackupCount,_that.lastBackupTime,_that.nextScheduledBackup,_that.enabled,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? directoryPath,  BackupFrequency frequency,  BackupFormat format,  bool includeImages,  bool compressBackups,  int maxBackupCount,  DateTime? lastBackupTime,  DateTime? nextScheduledBackup,  bool enabled,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _BackupSettings() when $default != null:
return $default(_that.directoryPath,_that.frequency,_that.format,_that.includeImages,_that.compressBackups,_that.maxBackupCount,_that.lastBackupTime,_that.nextScheduledBackup,_that.enabled,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BackupSettings extends BackupSettings {
  const _BackupSettings({this.directoryPath, this.frequency = BackupFrequency.manual, this.format = BackupFormat.json, this.includeImages = true, this.compressBackups = true, this.maxBackupCount = 5, this.lastBackupTime, this.nextScheduledBackup, this.enabled = true, final  Map<String, dynamic>? metadata}): _metadata = metadata,super._();
  factory _BackupSettings.fromJson(Map<String, dynamic> json) => _$BackupSettingsFromJson(json);

@override final  String? directoryPath;
@override@JsonKey() final  BackupFrequency frequency;
@override@JsonKey() final  BackupFormat format;
@override@JsonKey() final  bool includeImages;
@override@JsonKey() final  bool compressBackups;
@override@JsonKey() final  int maxBackupCount;
@override final  DateTime? lastBackupTime;
@override final  DateTime? nextScheduledBackup;
@override@JsonKey() final  bool enabled;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of BackupSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BackupSettingsCopyWith<_BackupSettings> get copyWith => __$BackupSettingsCopyWithImpl<_BackupSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BackupSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BackupSettings&&(identical(other.directoryPath, directoryPath) || other.directoryPath == directoryPath)&&(identical(other.frequency, frequency) || other.frequency == frequency)&&(identical(other.format, format) || other.format == format)&&(identical(other.includeImages, includeImages) || other.includeImages == includeImages)&&(identical(other.compressBackups, compressBackups) || other.compressBackups == compressBackups)&&(identical(other.maxBackupCount, maxBackupCount) || other.maxBackupCount == maxBackupCount)&&(identical(other.lastBackupTime, lastBackupTime) || other.lastBackupTime == lastBackupTime)&&(identical(other.nextScheduledBackup, nextScheduledBackup) || other.nextScheduledBackup == nextScheduledBackup)&&(identical(other.enabled, enabled) || other.enabled == enabled)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,directoryPath,frequency,format,includeImages,compressBackups,maxBackupCount,lastBackupTime,nextScheduledBackup,enabled,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'BackupSettings(directoryPath: $directoryPath, frequency: $frequency, format: $format, includeImages: $includeImages, compressBackups: $compressBackups, maxBackupCount: $maxBackupCount, lastBackupTime: $lastBackupTime, nextScheduledBackup: $nextScheduledBackup, enabled: $enabled, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$BackupSettingsCopyWith<$Res> implements $BackupSettingsCopyWith<$Res> {
  factory _$BackupSettingsCopyWith(_BackupSettings value, $Res Function(_BackupSettings) _then) = __$BackupSettingsCopyWithImpl;
@override @useResult
$Res call({
 String? directoryPath, BackupFrequency frequency, BackupFormat format, bool includeImages, bool compressBackups, int maxBackupCount, DateTime? lastBackupTime, DateTime? nextScheduledBackup, bool enabled, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$BackupSettingsCopyWithImpl<$Res>
    implements _$BackupSettingsCopyWith<$Res> {
  __$BackupSettingsCopyWithImpl(this._self, this._then);

  final _BackupSettings _self;
  final $Res Function(_BackupSettings) _then;

/// Create a copy of BackupSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? directoryPath = freezed,Object? frequency = null,Object? format = null,Object? includeImages = null,Object? compressBackups = null,Object? maxBackupCount = null,Object? lastBackupTime = freezed,Object? nextScheduledBackup = freezed,Object? enabled = null,Object? metadata = freezed,}) {
  return _then(_BackupSettings(
directoryPath: freezed == directoryPath ? _self.directoryPath : directoryPath // ignore: cast_nullable_to_non_nullable
as String?,frequency: null == frequency ? _self.frequency : frequency // ignore: cast_nullable_to_non_nullable
as BackupFrequency,format: null == format ? _self.format : format // ignore: cast_nullable_to_non_nullable
as BackupFormat,includeImages: null == includeImages ? _self.includeImages : includeImages // ignore: cast_nullable_to_non_nullable
as bool,compressBackups: null == compressBackups ? _self.compressBackups : compressBackups // ignore: cast_nullable_to_non_nullable
as bool,maxBackupCount: null == maxBackupCount ? _self.maxBackupCount : maxBackupCount // ignore: cast_nullable_to_non_nullable
as int,lastBackupTime: freezed == lastBackupTime ? _self.lastBackupTime : lastBackupTime // ignore: cast_nullable_to_non_nullable
as DateTime?,nextScheduledBackup: freezed == nextScheduledBackup ? _self.nextScheduledBackup : nextScheduledBackup // ignore: cast_nullable_to_non_nullable
as DateTime?,enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

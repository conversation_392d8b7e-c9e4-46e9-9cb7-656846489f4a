import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_settings.freezed.dart';
part 'app_settings.g.dart';

/// Domain entity representing application settings
@freezed
sealed class AppSettings with _$AppSettings {
  const AppSettings._();

  const factory AppSettings({
    int? id,
    required DateTime dateRangeStart,
    required DateTime dateRangeEnd,
    String? backupDirectoryPath,
    DateTime? lastSyncTime,
    required DateTime updatedAt,
    @Default(true) bool syncEnabled,
    @Default(true) bool notificationsEnabled,
    @Default(false) bool darkModeEnabled,
    @Default('en') String language,
    @Default('USD') String currency,
    Map<String, dynamic>? additionalSettings,
  }) = _AppSettings;

  factory AppSettings.fromJson(Map<String, dynamic> json) => _$AppSettingsFromJson(json);

  /// Create default app settings
  factory AppSettings.defaultSettings() {
    final now = DateTime.now().toUtc();
    final firstDayOfMonth = DateTime.utc(now.year, now.month, 1);
    final lastDayOfMonth = DateTime.utc(now.year, now.month + 1, 0);

    return AppSettings(
      dateRangeStart: firstDayOfMonth,
      dateRangeEnd: lastDayOfMonth,
      updatedAt: now,
      syncEnabled: true,
      notificationsEnabled: true,
      darkModeEnabled: false,
      language: 'en',
      currency: 'USD',
    );
  }

  /// Create from database AppSetting
  factory AppSettings.fromDatabase(dynamic dbSetting) {
    return AppSettings(
      id: dbSetting.id,
      dateRangeStart: dbSetting.dateRangeStart,
      dateRangeEnd: dbSetting.dateRangeEnd,
      backupDirectoryPath: dbSetting.backupDirectoryPath,
      lastSyncTime: dbSetting.lastSyncTime,
      updatedAt: dbSetting.updatedAt,
    );
  }

  /// Get date range as DateTimeRange
  DateTimeRange get dateRange => DateTimeRange(
        start: dateRangeStart,
        end: dateRangeEnd,
      );

  /// Check if backup is configured
  bool get hasBackupDirectory => 
      backupDirectoryPath != null && backupDirectoryPath!.isNotEmpty;

  /// Check if sync was performed recently (within last 24 hours)
  bool get hasRecentSync {
    if (lastSyncTime == null) return false;
    final now = DateTime.now();
    return now.difference(lastSyncTime!).inHours < 24;
  }

  /// Get time since last sync
  Duration? get timeSinceLastSync {
    if (lastSyncTime == null) return null;
    return DateTime.now().difference(lastSyncTime!);
  }

  /// Get formatted last sync time
  String get formattedLastSyncTime {
    if (lastSyncTime == null) return 'Never';
    
    final now = DateTime.now();
    final difference = now.difference(lastSyncTime!);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  /// Get date range duration in days
  int get dateRangeDays => dateRangeEnd.difference(dateRangeStart).inDays + 1;

  /// Check if date range is current month
  bool get isCurrentMonth {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    
    return dateRangeStart.year == firstDayOfMonth.year &&
           dateRangeStart.month == firstDayOfMonth.month &&
           dateRangeStart.day == firstDayOfMonth.day &&
           dateRangeEnd.year == lastDayOfMonth.year &&
           dateRangeEnd.month == lastDayOfMonth.month &&
           dateRangeEnd.day == lastDayOfMonth.day;
  }

  /// Check if date range is current year
  bool get isCurrentYear {
    final now = DateTime.now();
    return dateRangeStart.year == now.year && dateRangeEnd.year == now.year;
  }

  /// Get formatted date range
  String get formattedDateRange {
    final start = '${dateRangeStart.day}/${dateRangeStart.month}/${dateRangeStart.year}';
    final end = '${dateRangeEnd.day}/${dateRangeEnd.month}/${dateRangeEnd.year}';
    return '$start - $end';
  }

  /// Check if settings need attention (no backup directory, sync disabled, etc.)
  bool get needsAttention {
    return !hasBackupDirectory || !syncEnabled || !hasRecentSync;
  }

  /// Get list of issues that need attention
  List<String> get attentionIssues {
    final issues = <String>[];
    
    if (!hasBackupDirectory) {
      issues.add('Backup directory not configured');
    }
    
    if (!syncEnabled) {
      issues.add('Sync is disabled');
    }
    
    if (!hasRecentSync) {
      issues.add('No recent sync');
    }
    
    return issues;
  }

  /// Get additional setting value by key
  T? getAdditionalSetting<T>(String key) {
    if (additionalSettings == null) return null;
    final value = additionalSettings![key];
    return value is T ? value : null;
  }
}

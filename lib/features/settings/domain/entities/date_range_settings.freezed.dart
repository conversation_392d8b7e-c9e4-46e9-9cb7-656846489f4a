// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'date_range_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DateRangeSettings {

 DateTime get startDate; DateTime get endDate; DateRangeType get type; bool get autoUpdate; Map<String, dynamic>? get metadata;
/// Create a copy of DateRangeSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DateRangeSettingsCopyWith<DateRangeSettings> get copyWith => _$DateRangeSettingsCopyWithImpl<DateRangeSettings>(this as DateRangeSettings, _$identity);

  /// Serializes this DateRangeSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DateRangeSettings&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.type, type) || other.type == type)&&(identical(other.autoUpdate, autoUpdate) || other.autoUpdate == autoUpdate)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,type,autoUpdate,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'DateRangeSettings(startDate: $startDate, endDate: $endDate, type: $type, autoUpdate: $autoUpdate, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $DateRangeSettingsCopyWith<$Res>  {
  factory $DateRangeSettingsCopyWith(DateRangeSettings value, $Res Function(DateRangeSettings) _then) = _$DateRangeSettingsCopyWithImpl;
@useResult
$Res call({
 DateTime startDate, DateTime endDate, DateRangeType type, bool autoUpdate, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$DateRangeSettingsCopyWithImpl<$Res>
    implements $DateRangeSettingsCopyWith<$Res> {
  _$DateRangeSettingsCopyWithImpl(this._self, this._then);

  final DateRangeSettings _self;
  final $Res Function(DateRangeSettings) _then;

/// Create a copy of DateRangeSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? startDate = null,Object? endDate = null,Object? type = null,Object? autoUpdate = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as DateRangeType,autoUpdate: null == autoUpdate ? _self.autoUpdate : autoUpdate // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [DateRangeSettings].
extension DateRangeSettingsPatterns on DateRangeSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DateRangeSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DateRangeSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DateRangeSettings value)  $default,){
final _that = this;
switch (_that) {
case _DateRangeSettings():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DateRangeSettings value)?  $default,){
final _that = this;
switch (_that) {
case _DateRangeSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime startDate,  DateTime endDate,  DateRangeType type,  bool autoUpdate,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DateRangeSettings() when $default != null:
return $default(_that.startDate,_that.endDate,_that.type,_that.autoUpdate,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime startDate,  DateTime endDate,  DateRangeType type,  bool autoUpdate,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _DateRangeSettings():
return $default(_that.startDate,_that.endDate,_that.type,_that.autoUpdate,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime startDate,  DateTime endDate,  DateRangeType type,  bool autoUpdate,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _DateRangeSettings() when $default != null:
return $default(_that.startDate,_that.endDate,_that.type,_that.autoUpdate,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DateRangeSettings extends DateRangeSettings {
  const _DateRangeSettings({required this.startDate, required this.endDate, this.type = DateRangeType.currentMonth, this.autoUpdate = true, final  Map<String, dynamic>? metadata}): _metadata = metadata,super._();
  factory _DateRangeSettings.fromJson(Map<String, dynamic> json) => _$DateRangeSettingsFromJson(json);

@override final  DateTime startDate;
@override final  DateTime endDate;
@override@JsonKey() final  DateRangeType type;
@override@JsonKey() final  bool autoUpdate;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of DateRangeSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DateRangeSettingsCopyWith<_DateRangeSettings> get copyWith => __$DateRangeSettingsCopyWithImpl<_DateRangeSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DateRangeSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DateRangeSettings&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.type, type) || other.type == type)&&(identical(other.autoUpdate, autoUpdate) || other.autoUpdate == autoUpdate)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,startDate,endDate,type,autoUpdate,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'DateRangeSettings(startDate: $startDate, endDate: $endDate, type: $type, autoUpdate: $autoUpdate, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$DateRangeSettingsCopyWith<$Res> implements $DateRangeSettingsCopyWith<$Res> {
  factory _$DateRangeSettingsCopyWith(_DateRangeSettings value, $Res Function(_DateRangeSettings) _then) = __$DateRangeSettingsCopyWithImpl;
@override @useResult
$Res call({
 DateTime startDate, DateTime endDate, DateRangeType type, bool autoUpdate, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$DateRangeSettingsCopyWithImpl<$Res>
    implements _$DateRangeSettingsCopyWith<$Res> {
  __$DateRangeSettingsCopyWithImpl(this._self, this._then);

  final _DateRangeSettings _self;
  final $Res Function(_DateRangeSettings) _then;

/// Create a copy of DateRangeSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? startDate = null,Object? endDate = null,Object? type = null,Object? autoUpdate = null,Object? metadata = freezed,}) {
  return _then(_DateRangeSettings(
startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as DateRangeType,autoUpdate: null == autoUpdate ? _self.autoUpdate : autoUpdate // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

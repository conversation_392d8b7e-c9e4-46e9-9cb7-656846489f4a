// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppSettings {

 int? get id; DateTime get dateRangeStart; DateTime get dateRangeEnd; String? get backupDirectoryPath; DateTime? get lastSyncTime; DateTime get updatedAt; bool get syncEnabled; bool get notificationsEnabled; bool get darkModeEnabled; String get language; String get currency; Map<String, dynamic>? get additionalSettings;
/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppSettingsCopyWith<AppSettings> get copyWith => _$AppSettingsCopyWithImpl<AppSettings>(this as AppSettings, _$identity);

  /// Serializes this AppSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppSettings&&(identical(other.id, id) || other.id == id)&&(identical(other.dateRangeStart, dateRangeStart) || other.dateRangeStart == dateRangeStart)&&(identical(other.dateRangeEnd, dateRangeEnd) || other.dateRangeEnd == dateRangeEnd)&&(identical(other.backupDirectoryPath, backupDirectoryPath) || other.backupDirectoryPath == backupDirectoryPath)&&(identical(other.lastSyncTime, lastSyncTime) || other.lastSyncTime == lastSyncTime)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncEnabled, syncEnabled) || other.syncEnabled == syncEnabled)&&(identical(other.notificationsEnabled, notificationsEnabled) || other.notificationsEnabled == notificationsEnabled)&&(identical(other.darkModeEnabled, darkModeEnabled) || other.darkModeEnabled == darkModeEnabled)&&(identical(other.language, language) || other.language == language)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other.additionalSettings, additionalSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,dateRangeStart,dateRangeEnd,backupDirectoryPath,lastSyncTime,updatedAt,syncEnabled,notificationsEnabled,darkModeEnabled,language,currency,const DeepCollectionEquality().hash(additionalSettings));

@override
String toString() {
  return 'AppSettings(id: $id, dateRangeStart: $dateRangeStart, dateRangeEnd: $dateRangeEnd, backupDirectoryPath: $backupDirectoryPath, lastSyncTime: $lastSyncTime, updatedAt: $updatedAt, syncEnabled: $syncEnabled, notificationsEnabled: $notificationsEnabled, darkModeEnabled: $darkModeEnabled, language: $language, currency: $currency, additionalSettings: $additionalSettings)';
}


}

/// @nodoc
abstract mixin class $AppSettingsCopyWith<$Res>  {
  factory $AppSettingsCopyWith(AppSettings value, $Res Function(AppSettings) _then) = _$AppSettingsCopyWithImpl;
@useResult
$Res call({
 int? id, DateTime dateRangeStart, DateTime dateRangeEnd, String? backupDirectoryPath, DateTime? lastSyncTime, DateTime updatedAt, bool syncEnabled, bool notificationsEnabled, bool darkModeEnabled, String language, String currency, Map<String, dynamic>? additionalSettings
});




}
/// @nodoc
class _$AppSettingsCopyWithImpl<$Res>
    implements $AppSettingsCopyWith<$Res> {
  _$AppSettingsCopyWithImpl(this._self, this._then);

  final AppSettings _self;
  final $Res Function(AppSettings) _then;

/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? dateRangeStart = null,Object? dateRangeEnd = null,Object? backupDirectoryPath = freezed,Object? lastSyncTime = freezed,Object? updatedAt = null,Object? syncEnabled = null,Object? notificationsEnabled = null,Object? darkModeEnabled = null,Object? language = null,Object? currency = null,Object? additionalSettings = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,dateRangeStart: null == dateRangeStart ? _self.dateRangeStart : dateRangeStart // ignore: cast_nullable_to_non_nullable
as DateTime,dateRangeEnd: null == dateRangeEnd ? _self.dateRangeEnd : dateRangeEnd // ignore: cast_nullable_to_non_nullable
as DateTime,backupDirectoryPath: freezed == backupDirectoryPath ? _self.backupDirectoryPath : backupDirectoryPath // ignore: cast_nullable_to_non_nullable
as String?,lastSyncTime: freezed == lastSyncTime ? _self.lastSyncTime : lastSyncTime // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,syncEnabled: null == syncEnabled ? _self.syncEnabled : syncEnabled // ignore: cast_nullable_to_non_nullable
as bool,notificationsEnabled: null == notificationsEnabled ? _self.notificationsEnabled : notificationsEnabled // ignore: cast_nullable_to_non_nullable
as bool,darkModeEnabled: null == darkModeEnabled ? _self.darkModeEnabled : darkModeEnabled // ignore: cast_nullable_to_non_nullable
as bool,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,additionalSettings: freezed == additionalSettings ? _self.additionalSettings : additionalSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [AppSettings].
extension AppSettingsPatterns on AppSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AppSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AppSettings value)  $default,){
final _that = this;
switch (_that) {
case _AppSettings():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AppSettings value)?  $default,){
final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  DateTime dateRangeStart,  DateTime dateRangeEnd,  String? backupDirectoryPath,  DateTime? lastSyncTime,  DateTime updatedAt,  bool syncEnabled,  bool notificationsEnabled,  bool darkModeEnabled,  String language,  String currency,  Map<String, dynamic>? additionalSettings)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that.id,_that.dateRangeStart,_that.dateRangeEnd,_that.backupDirectoryPath,_that.lastSyncTime,_that.updatedAt,_that.syncEnabled,_that.notificationsEnabled,_that.darkModeEnabled,_that.language,_that.currency,_that.additionalSettings);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  DateTime dateRangeStart,  DateTime dateRangeEnd,  String? backupDirectoryPath,  DateTime? lastSyncTime,  DateTime updatedAt,  bool syncEnabled,  bool notificationsEnabled,  bool darkModeEnabled,  String language,  String currency,  Map<String, dynamic>? additionalSettings)  $default,) {final _that = this;
switch (_that) {
case _AppSettings():
return $default(_that.id,_that.dateRangeStart,_that.dateRangeEnd,_that.backupDirectoryPath,_that.lastSyncTime,_that.updatedAt,_that.syncEnabled,_that.notificationsEnabled,_that.darkModeEnabled,_that.language,_that.currency,_that.additionalSettings);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  DateTime dateRangeStart,  DateTime dateRangeEnd,  String? backupDirectoryPath,  DateTime? lastSyncTime,  DateTime updatedAt,  bool syncEnabled,  bool notificationsEnabled,  bool darkModeEnabled,  String language,  String currency,  Map<String, dynamic>? additionalSettings)?  $default,) {final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that.id,_that.dateRangeStart,_that.dateRangeEnd,_that.backupDirectoryPath,_that.lastSyncTime,_that.updatedAt,_that.syncEnabled,_that.notificationsEnabled,_that.darkModeEnabled,_that.language,_that.currency,_that.additionalSettings);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AppSettings extends AppSettings {
  const _AppSettings({this.id, required this.dateRangeStart, required this.dateRangeEnd, this.backupDirectoryPath, this.lastSyncTime, required this.updatedAt, this.syncEnabled = true, this.notificationsEnabled = true, this.darkModeEnabled = false, this.language = 'en', this.currency = 'USD', final  Map<String, dynamic>? additionalSettings}): _additionalSettings = additionalSettings,super._();
  factory _AppSettings.fromJson(Map<String, dynamic> json) => _$AppSettingsFromJson(json);

@override final  int? id;
@override final  DateTime dateRangeStart;
@override final  DateTime dateRangeEnd;
@override final  String? backupDirectoryPath;
@override final  DateTime? lastSyncTime;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool syncEnabled;
@override@JsonKey() final  bool notificationsEnabled;
@override@JsonKey() final  bool darkModeEnabled;
@override@JsonKey() final  String language;
@override@JsonKey() final  String currency;
 final  Map<String, dynamic>? _additionalSettings;
@override Map<String, dynamic>? get additionalSettings {
  final value = _additionalSettings;
  if (value == null) return null;
  if (_additionalSettings is EqualUnmodifiableMapView) return _additionalSettings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppSettingsCopyWith<_AppSettings> get copyWith => __$AppSettingsCopyWithImpl<_AppSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppSettings&&(identical(other.id, id) || other.id == id)&&(identical(other.dateRangeStart, dateRangeStart) || other.dateRangeStart == dateRangeStart)&&(identical(other.dateRangeEnd, dateRangeEnd) || other.dateRangeEnd == dateRangeEnd)&&(identical(other.backupDirectoryPath, backupDirectoryPath) || other.backupDirectoryPath == backupDirectoryPath)&&(identical(other.lastSyncTime, lastSyncTime) || other.lastSyncTime == lastSyncTime)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncEnabled, syncEnabled) || other.syncEnabled == syncEnabled)&&(identical(other.notificationsEnabled, notificationsEnabled) || other.notificationsEnabled == notificationsEnabled)&&(identical(other.darkModeEnabled, darkModeEnabled) || other.darkModeEnabled == darkModeEnabled)&&(identical(other.language, language) || other.language == language)&&(identical(other.currency, currency) || other.currency == currency)&&const DeepCollectionEquality().equals(other._additionalSettings, _additionalSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,dateRangeStart,dateRangeEnd,backupDirectoryPath,lastSyncTime,updatedAt,syncEnabled,notificationsEnabled,darkModeEnabled,language,currency,const DeepCollectionEquality().hash(_additionalSettings));

@override
String toString() {
  return 'AppSettings(id: $id, dateRangeStart: $dateRangeStart, dateRangeEnd: $dateRangeEnd, backupDirectoryPath: $backupDirectoryPath, lastSyncTime: $lastSyncTime, updatedAt: $updatedAt, syncEnabled: $syncEnabled, notificationsEnabled: $notificationsEnabled, darkModeEnabled: $darkModeEnabled, language: $language, currency: $currency, additionalSettings: $additionalSettings)';
}


}

/// @nodoc
abstract mixin class _$AppSettingsCopyWith<$Res> implements $AppSettingsCopyWith<$Res> {
  factory _$AppSettingsCopyWith(_AppSettings value, $Res Function(_AppSettings) _then) = __$AppSettingsCopyWithImpl;
@override @useResult
$Res call({
 int? id, DateTime dateRangeStart, DateTime dateRangeEnd, String? backupDirectoryPath, DateTime? lastSyncTime, DateTime updatedAt, bool syncEnabled, bool notificationsEnabled, bool darkModeEnabled, String language, String currency, Map<String, dynamic>? additionalSettings
});




}
/// @nodoc
class __$AppSettingsCopyWithImpl<$Res>
    implements _$AppSettingsCopyWith<$Res> {
  __$AppSettingsCopyWithImpl(this._self, this._then);

  final _AppSettings _self;
  final $Res Function(_AppSettings) _then;

/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? dateRangeStart = null,Object? dateRangeEnd = null,Object? backupDirectoryPath = freezed,Object? lastSyncTime = freezed,Object? updatedAt = null,Object? syncEnabled = null,Object? notificationsEnabled = null,Object? darkModeEnabled = null,Object? language = null,Object? currency = null,Object? additionalSettings = freezed,}) {
  return _then(_AppSettings(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,dateRangeStart: null == dateRangeStart ? _self.dateRangeStart : dateRangeStart // ignore: cast_nullable_to_non_nullable
as DateTime,dateRangeEnd: null == dateRangeEnd ? _self.dateRangeEnd : dateRangeEnd // ignore: cast_nullable_to_non_nullable
as DateTime,backupDirectoryPath: freezed == backupDirectoryPath ? _self.backupDirectoryPath : backupDirectoryPath // ignore: cast_nullable_to_non_nullable
as String?,lastSyncTime: freezed == lastSyncTime ? _self.lastSyncTime : lastSyncTime // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,syncEnabled: null == syncEnabled ? _self.syncEnabled : syncEnabled // ignore: cast_nullable_to_non_nullable
as bool,notificationsEnabled: null == notificationsEnabled ? _self.notificationsEnabled : notificationsEnabled // ignore: cast_nullable_to_non_nullable
as bool,darkModeEnabled: null == darkModeEnabled ? _self.darkModeEnabled : darkModeEnabled // ignore: cast_nullable_to_non_nullable
as bool,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,additionalSettings: freezed == additionalSettings ? _self._additionalSettings : additionalSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

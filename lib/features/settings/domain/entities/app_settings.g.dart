// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppSettings _$AppSettingsFromJson(Map<String, dynamic> json) => _AppSettings(
  id: (json['id'] as num?)?.toInt(),
  dateRangeStart: DateTime.parse(json['dateRangeStart'] as String),
  dateRangeEnd: DateTime.parse(json['dateRangeEnd'] as String),
  backupDirectoryPath: json['backupDirectoryPath'] as String?,
  lastSyncTime: json['lastSyncTime'] == null
      ? null
      : DateTime.parse(json['lastSyncTime'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  syncEnabled: json['syncEnabled'] as bool? ?? true,
  notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
  darkModeEnabled: json['darkModeEnabled'] as bool? ?? false,
  language: json['language'] as String? ?? 'en',
  currency: json['currency'] as String? ?? 'USD',
  additionalSettings: json['additionalSettings'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$AppSettingsToJson(_AppSettings instance) =>
    <String, dynamic>{
      'id': instance.id,
      'dateRangeStart': instance.dateRangeStart.toIso8601String(),
      'dateRangeEnd': instance.dateRangeEnd.toIso8601String(),
      'backupDirectoryPath': instance.backupDirectoryPath,
      'lastSyncTime': instance.lastSyncTime?.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'syncEnabled': instance.syncEnabled,
      'notificationsEnabled': instance.notificationsEnabled,
      'darkModeEnabled': instance.darkModeEnabled,
      'language': instance.language,
      'currency': instance.currency,
      'additionalSettings': instance.additionalSettings,
    };

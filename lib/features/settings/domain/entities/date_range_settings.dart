import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'date_range_settings.freezed.dart';
part 'date_range_settings.g.dart';

/// Enum for predefined date range types
enum DateRangeType {
  custom,
  currentMonth,
  lastMonth,
  currentYear,
  lastYear,
  last30Days,
  last90Days,
  last6Months,
}

/// Domain entity representing date range settings
@freezed
sealed class DateRangeSettings with _$DateRangeSettings {
  const DateRangeSettings._();

  const factory DateRangeSettings({
    required DateTime startDate,
    required DateTime endDate,
    @Default(DateRangeType.currentMonth) DateRangeType type,
    @Default(true) bool autoUpdate,
    Map<String, dynamic>? metadata,
  }) = _DateRangeSettings;

  factory DateRangeSettings.fromJson(Map<String, dynamic> json) => _$DateRangeSettingsFromJson(json);

  /// Create current month date range
  factory DateRangeSettings.currentMonth() {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, now.month, 1);
    final lastDay = DateTime(now.year, now.month + 1, 0);
    
    return DateRangeSettings(
      startDate: firstDay,
      endDate: lastDay,
      type: DateRangeType.currentMonth,
      autoUpdate: true,
    );
  }

  /// Create last month date range
  factory DateRangeSettings.lastMonth() {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, now.month - 1, 1);
    final lastDay = DateTime(now.year, now.month, 0);
    
    return DateRangeSettings(
      startDate: firstDay,
      endDate: lastDay,
      type: DateRangeType.lastMonth,
      autoUpdate: false,
    );
  }

  /// Create current year date range
  factory DateRangeSettings.currentYear() {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, 1, 1);
    final lastDay = DateTime(now.year, 12, 31);
    
    return DateRangeSettings(
      startDate: firstDay,
      endDate: lastDay,
      type: DateRangeType.currentYear,
      autoUpdate: true,
    );
  }

  /// Create last N days date range
  factory DateRangeSettings.lastNDays(int days) {
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: days - 1));
    
    return DateRangeSettings(
      startDate: DateTime(startDate.year, startDate.month, startDate.day),
      endDate: DateTime(now.year, now.month, now.day),
      type: days == 30 ? DateRangeType.last30Days : 
            days == 90 ? DateRangeType.last90Days : DateRangeType.custom,
      autoUpdate: true,
    );
  }

  /// Create custom date range
  factory DateRangeSettings.custom({
    required DateTime startDate,
    required DateTime endDate,
    bool autoUpdate = false,
  }) {
    return DateRangeSettings(
      startDate: startDate,
      endDate: endDate,
      type: DateRangeType.custom,
      autoUpdate: autoUpdate,
    );
  }

  /// Create from DateTimeRange
  factory DateRangeSettings.fromDateTimeRange(
    DateTimeRange range, {
    DateRangeType type = DateRangeType.custom,
    bool autoUpdate = false,
  }) {
    return DateRangeSettings(
      startDate: range.start,
      endDate: range.end,
      type: type,
      autoUpdate: autoUpdate,
    );
  }

  /// Convert to DateTimeRange
  DateTimeRange get dateTimeRange => DateTimeRange(start: startDate, end: endDate);

  /// Get duration in days
  int get durationInDays => endDate.difference(startDate).inDays + 1;

  /// Check if date range is valid
  bool get isValid => startDate.isBefore(endDate) || startDate.isAtSameMomentAs(endDate);

  /// Check if date range includes today
  bool get includesCurrentDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return (startDate.isBefore(today) || startDate.isAtSameMomentAs(today)) &&
           (endDate.isAfter(today) || endDate.isAtSameMomentAs(today));
  }

  /// Check if date range is in the past
  bool get isInPast {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return endDate.isBefore(today);
  }

  /// Check if date range is in the future
  bool get isInFuture {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return startDate.isAfter(today);
  }

  /// Get formatted date range string
  String get formattedRange {
    final start = '${startDate.day}/${startDate.month}/${startDate.year}';
    final end = '${endDate.day}/${endDate.month}/${endDate.year}';
    return '$start - $end';
  }

  /// Get short formatted date range string
  String get shortFormattedRange {
    if (startDate.year == endDate.year) {
      if (startDate.month == endDate.month) {
        return '${startDate.day}-${endDate.day}/${startDate.month}/${startDate.year}';
      } else {
        return '${startDate.day}/${startDate.month} - ${endDate.day}/${endDate.month}/${startDate.year}';
      }
    }
    return formattedRange;
  }

  /// Get type display name
  String get typeDisplayName {
    switch (type) {
      case DateRangeType.custom:
        return 'Custom';
      case DateRangeType.currentMonth:
        return 'Current Month';
      case DateRangeType.lastMonth:
        return 'Last Month';
      case DateRangeType.currentYear:
        return 'Current Year';
      case DateRangeType.lastYear:
        return 'Last Year';
      case DateRangeType.last30Days:
        return 'Last 30 Days';
      case DateRangeType.last90Days:
        return 'Last 90 Days';
      case DateRangeType.last6Months:
        return 'Last 6 Months';
    }
  }

  /// Check if this date range needs auto-update
  bool get shouldAutoUpdate {
    if (!autoUpdate) return false;
    
    switch (type) {
      case DateRangeType.currentMonth:
      case DateRangeType.currentYear:
      case DateRangeType.last30Days:
      case DateRangeType.last90Days:
        return true;
      default:
        return false;
    }
  }

  /// Get updated date range if auto-update is enabled
  DateRangeSettings? getUpdatedRange() {
    if (!shouldAutoUpdate) return null;
    
    switch (type) {
      case DateRangeType.currentMonth:
        return DateRangeSettings.currentMonth();
      case DateRangeType.currentYear:
        return DateRangeSettings.currentYear();
      case DateRangeType.last30Days:
        return DateRangeSettings.lastNDays(30);
      case DateRangeType.last90Days:
        return DateRangeSettings.lastNDays(90);
      default:
        return null;
    }
  }

  /// Check if date is within this range
  bool containsDate(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    return (dateOnly.isAfter(startDate) || dateOnly.isAtSameMomentAs(startDate)) &&
           (dateOnly.isBefore(endDate) || dateOnly.isAtSameMomentAs(endDate));
  }
}

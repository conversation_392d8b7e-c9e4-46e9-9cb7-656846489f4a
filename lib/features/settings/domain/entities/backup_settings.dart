import 'package:freezed_annotation/freezed_annotation.dart';

part 'backup_settings.freezed.dart';
part 'backup_settings.g.dart';

/// Enum for backup frequency options
enum BackupFrequency {
  manual,
  daily,
  weekly,
  monthly,
}

/// Enum for backup format options
enum BackupFormat {
  json,
  csv,
  sqlite,
}

/// Domain entity representing backup settings
@freezed
sealed class BackupSettings with _$BackupSettings {
  const BackupSettings._();

  const factory BackupSettings({
    String? directoryPath,
    @Default(BackupFrequency.manual) BackupFrequency frequency,
    @Default(BackupFormat.json) BackupFormat format,
    @Default(true) bool includeImages,
    @Default(true) bool compressBackups,
    @Default(5) int maxBackupCount,
    DateTime? lastBackupTime,
    DateTime? nextScheduledBackup,
    @Default(true) bool enabled,
    Map<String, dynamic>? metadata,
  }) = _BackupSettings;

  factory BackupSettings.fromJson(Map<String, dynamic> json) => _$BackupSettingsFromJson(json);

  /// Create default backup settings
  factory BackupSettings.defaultSettings() {
    return const BackupSettings(
      frequency: BackupFrequency.manual,
      format: BackupFormat.json,
      includeImages: true,
      compressBackups: true,
      maxBackupCount: 5,
      enabled: true,
    );
  }

  /// Check if backup directory is configured
  bool get hasDirectory => directoryPath != null && directoryPath!.isNotEmpty;

  /// Check if automatic backups are enabled
  bool get isAutomaticBackupEnabled => enabled && frequency != BackupFrequency.manual;

  /// Check if backup was performed recently
  bool get hasRecentBackup {
    if (lastBackupTime == null) return false;
    
    final now = DateTime.now();
    final timeSinceBackup = now.difference(lastBackupTime!);
    
    switch (frequency) {
      case BackupFrequency.manual:
        return timeSinceBackup.inDays < 7; // Consider recent if within a week
      case BackupFrequency.daily:
        return timeSinceBackup.inHours < 25; // Allow some buffer
      case BackupFrequency.weekly:
        return timeSinceBackup.inDays < 8; // Allow some buffer
      case BackupFrequency.monthly:
        return timeSinceBackup.inDays < 32; // Allow some buffer
    }
  }

  /// Check if backup is overdue
  bool get isBackupOverdue {
    if (!isAutomaticBackupEnabled || lastBackupTime == null) return false;
    
    final now = DateTime.now();
    final timeSinceBackup = now.difference(lastBackupTime!);
    
    switch (frequency) {
      case BackupFrequency.manual:
        return false; // Manual backups can't be overdue
      case BackupFrequency.daily:
        return timeSinceBackup.inHours > 26;
      case BackupFrequency.weekly:
        return timeSinceBackup.inDays > 8;
      case BackupFrequency.monthly:
        return timeSinceBackup.inDays > 32;
    }
  }

  /// Get time since last backup
  Duration? get timeSinceLastBackup {
    if (lastBackupTime == null) return null;
    return DateTime.now().difference(lastBackupTime!);
  }

  /// Get formatted last backup time
  String get formattedLastBackupTime {
    if (lastBackupTime == null) return 'Never';
    
    final now = DateTime.now();
    final difference = now.difference(lastBackupTime!);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  /// Get frequency display name
  String get frequencyDisplayName {
    switch (frequency) {
      case BackupFrequency.manual:
        return 'Manual';
      case BackupFrequency.daily:
        return 'Daily';
      case BackupFrequency.weekly:
        return 'Weekly';
      case BackupFrequency.monthly:
        return 'Monthly';
    }
  }

  /// Get format display name
  String get formatDisplayName {
    switch (format) {
      case BackupFormat.json:
        return 'JSON';
      case BackupFormat.csv:
        return 'CSV';
      case BackupFormat.sqlite:
        return 'SQLite';
    }
  }

  /// Get file extension for the backup format
  String get fileExtension {
    switch (format) {
      case BackupFormat.json:
        return '.json';
      case BackupFormat.csv:
        return '.csv';
      case BackupFormat.sqlite:
        return '.db';
    }
  }

  /// Calculate next backup time based on frequency
  DateTime? get calculatedNextBackupTime {
    if (!isAutomaticBackupEnabled || lastBackupTime == null) return null;
    
    switch (frequency) {
      case BackupFrequency.manual:
        return null;
      case BackupFrequency.daily:
        return lastBackupTime!.add(const Duration(days: 1));
      case BackupFrequency.weekly:
        return lastBackupTime!.add(const Duration(days: 7));
      case BackupFrequency.monthly:
        return DateTime(
          lastBackupTime!.year,
          lastBackupTime!.month + 1,
          lastBackupTime!.day,
        );
    }
  }

  /// Check if backup is ready to be performed
  bool get isReadyForBackup {
    if (!enabled || !hasDirectory) return false;
    
    if (frequency == BackupFrequency.manual) return true;
    
    final nextBackup = calculatedNextBackupTime;
    if (nextBackup == null) return true;
    
    return DateTime.now().isAfter(nextBackup);
  }

  /// Get backup status message
  String get statusMessage {
    if (!enabled) return 'Backup disabled';
    if (!hasDirectory) return 'No backup directory configured';
    if (isBackupOverdue) return 'Backup overdue';
    if (hasRecentBackup) return 'Backup up to date';
    if (lastBackupTime == null) return 'No backup performed yet';
    return 'Backup needed';
  }

  /// Check if settings need attention
  bool get needsAttention {
    return !enabled || !hasDirectory || isBackupOverdue || lastBackupTime == null;
  }

  /// Get list of issues that need attention
  List<String> get attentionIssues {
    final issues = <String>[];
    
    if (!enabled) {
      issues.add('Backup is disabled');
    }
    
    if (!hasDirectory) {
      issues.add('No backup directory configured');
    }
    
    if (isBackupOverdue) {
      issues.add('Backup is overdue');
    }
    
    if (lastBackupTime == null) {
      issues.add('No backup performed yet');
    }
    
    return issues;
  }

  /// Generate backup filename
  String generateBackupFilename({String prefix = 'bidtrakr_backup'}) {
    final now = DateTime.now();
    final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
    return '$prefix$timestamp$fileExtension';
  }
}

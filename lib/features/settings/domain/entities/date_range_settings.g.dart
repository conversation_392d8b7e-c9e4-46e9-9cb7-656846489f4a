// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'date_range_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DateRangeSettings _$DateRangeSettingsFromJson(Map<String, dynamic> json) =>
    _DateRangeSettings(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      type:
          $enumDecodeNullable(_$DateRangeTypeEnumMap, json['type']) ??
          DateRangeType.currentMonth,
      autoUpdate: json['autoUpdate'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$DateRangeSettingsToJson(_DateRangeSettings instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'type': _$DateRangeTypeEnumMap[instance.type]!,
      'autoUpdate': instance.autoUpdate,
      'metadata': instance.metadata,
    };

const _$DateRangeTypeEnumMap = {
  DateRangeType.custom: 'custom',
  DateRangeType.currentMonth: 'currentMonth',
  DateRangeType.lastMonth: 'lastMonth',
  DateRangeType.currentYear: 'currentYear',
  DateRangeType.lastYear: 'lastYear',
  DateRangeType.last30Days: 'last30Days',
  DateRangeType.last90Days: 'last90Days',
  DateRangeType.last6Months: 'last6Months',
};

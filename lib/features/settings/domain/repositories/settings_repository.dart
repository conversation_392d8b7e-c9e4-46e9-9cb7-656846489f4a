import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import '../../../../core/errors/failures.dart';
import '../entities/app_settings.dart';
import '../entities/backup_settings.dart';
import '../entities/date_range_settings.dart';

/// Repository interface for settings operations
abstract class SettingsRepository {
  /// Get current app settings
  Future<Either<Failure, AppSettings>> getAppSettings();

  /// Update app settings
  Future<Either<Failure, AppSettings>> updateAppSettings(AppSettings settings);

  /// Get or create default app settings
  Future<Either<Failure, AppSettings>> getOrCreateAppSettings();

  /// Update date range settings
  Future<Either<Failure, AppSettings>> updateDateRange(DateTimeRange dateRange);

  /// Get date range settings
  Future<Either<Failure, DateRangeSettings>> getDateRangeSettings();

  /// Update date range settings
  Future<Either<Failure, DateRangeSettings>> updateDateRangeSettings(DateRangeSettings settings);

  /// Get backup settings
  Future<Either<Failure, BackupSettings>> getBackupSettings();

  /// Update backup settings
  Future<Either<Failure, BackupSettings>> updateBackupSettings(BackupSettings settings);

  /// Update backup directory path
  Future<Either<Failure, AppSettings>> updateBackupDirectoryPath(String? path);

  /// Update last sync time
  Future<Either<Failure, AppSettings>> updateLastSyncTime(DateTime syncTime);

  /// Enable or disable sync
  Future<Either<Failure, AppSettings>> setSyncEnabled(bool enabled);

  /// Enable or disable notifications
  Future<Either<Failure, AppSettings>> setNotificationsEnabled(bool enabled);

  /// Enable or disable dark mode
  Future<Either<Failure, AppSettings>> setDarkModeEnabled(bool enabled);

  /// Update language setting
  Future<Either<Failure, AppSettings>> updateLanguage(String language);

  /// Update currency setting
  Future<Either<Failure, AppSettings>> updateCurrency(String currency);

  /// Update additional setting
  Future<Either<Failure, AppSettings>> updateAdditionalSetting(String key, dynamic value);

  /// Remove additional setting
  Future<Either<Failure, AppSettings>> removeAdditionalSetting(String key);

  /// Reset settings to defaults
  Future<Either<Failure, AppSettings>> resetToDefaults();

  /// Export settings to JSON
  Future<Either<Failure, Map<String, dynamic>>> exportSettings();

  /// Import settings from JSON
  Future<Either<Failure, AppSettings>> importSettings(Map<String, dynamic> settingsJson);

  /// Validate settings
  Future<Either<Failure, bool>> validateSettings(AppSettings settings);

  /// Listen to settings changes
  Stream<Either<Failure, AppSettings>> get settingsStream;

  /// Initialize the settings repository
  Future<Either<Failure, bool>> initialize();

  /// Check if the repository is initialized
  bool get isInitialized;
}

/// Settings feature barrel export file
///
/// This file provides a single import point for all settings functionality:
/// ```dart
/// import 'package:bidtrakr/features/settings/settings.dart';
/// ```
library;

// Data layer
export 'data/repositories/settings_repository_impl.dart';

// Domain layer
export 'domain/entities/app_settings.dart';
export 'domain/entities/backup_settings.dart';
export 'domain/entities/date_range_settings.dart';
export 'domain/repositories/settings_repository.dart';
export 'domain/use_cases/get_app_settings.dart';
export 'domain/use_cases/reset_settings.dart';
export 'domain/use_cases/update_app_settings.dart';

// Presentation layer - providers
export 'presentation/providers/settings_providers.dart';

// Presentation layer - screens
export 'presentation/screens/app_settings_screen.dart';

// Presentation layer - widgets
export 'presentation/widgets/app_info_card.dart';
export 'presentation/widgets/backup_directory_settings_card.dart';
export 'presentation/widgets/database_repair_section.dart';
export 'presentation/widgets/date_range_settings_card.dart';
export 'presentation/widgets/error_card.dart';
export 'presentation/widgets/section_header.dart';
export 'presentation/widgets/settings_card.dart';

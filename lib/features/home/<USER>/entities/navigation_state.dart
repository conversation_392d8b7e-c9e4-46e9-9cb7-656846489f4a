import 'package:freezed_annotation/freezed_annotation.dart';

part 'navigation_state.freezed.dart';
part 'navigation_state.g.dart';

/// Enum representing the available navigation tabs
enum NavigationTab {
  income,
  orders,
  performance,
  more,
}

/// Domain entity representing the navigation state of the home screen
@freezed
sealed class NavigationState with _$NavigationState {
  const NavigationState._();

  const factory NavigationState({
    @Default(NavigationTab.income) NavigationTab selectedTab,
    @Default(true) bool isNavigationEnabled,
    Map<NavigationTab, bool>? tabVisibility,
    Map<NavigationTab, int>? badgeCounts,
    Map<String, dynamic>? metadata,
  }) = _NavigationState;

  factory NavigationState.fromJson(Map<String, dynamic> json) => _$NavigationStateFromJson(json);

  /// Create initial navigation state
  factory NavigationState.initial() {
    return const NavigationState(
      selectedTab: NavigationTab.income,
      isNavigationEnabled: true,
      tabVisibility: {
        NavigationTab.income: true,
        NavigationTab.orders: true,
        NavigationTab.performance: true,
        NavigationTab.more: true,
      },
    );
  }

  /// Get the index of the selected tab
  int get selectedIndex => selectedTab.index;

  /// Get the total number of visible tabs
  int get visibleTabCount {
    if (tabVisibility == null) return NavigationTab.values.length;
    return tabVisibility!.values.where((isVisible) => isVisible).length;
  }

  /// Check if a specific tab is visible
  bool isTabVisible(NavigationTab tab) {
    return tabVisibility?[tab] ?? true;
  }

  /// Get the total badge count across all tabs
  int get totalBadgeCount {
    if (badgeCounts == null) return 0;
    return badgeCounts!.values.fold(0, (sum, count) => sum + count);
  }

  /// Get badge count for a specific tab
  int getBadgeCount(NavigationTab tab) {
    return badgeCounts?[tab] ?? 0;
  }

  /// Check if any tab has badges
  bool get hasBadges => totalBadgeCount > 0;

  /// Get list of visible tabs
  List<NavigationTab> get visibleTabs {
    return NavigationTab.values.where((tab) => isTabVisible(tab)).toList();
  }

  /// Get tab label
  String getTabLabel(NavigationTab tab) {
    switch (tab) {
      case NavigationTab.income:
        return 'Income';
      case NavigationTab.orders:
        return 'Orders';
      case NavigationTab.performance:
        return 'Performance';
      case NavigationTab.more:
        return 'More';
    }
  }

  /// Get tab icon name
  String getTabIcon(NavigationTab tab) {
    switch (tab) {
      case NavigationTab.income:
        return 'attach_money';
      case NavigationTab.orders:
        return 'receipt_long';
      case NavigationTab.performance:
        return 'bar_chart';
      case NavigationTab.more:
        return 'more_horiz';
    }
  }

  /// Check if navigation can be changed
  bool canNavigateTo(NavigationTab tab) {
    return isNavigationEnabled && isTabVisible(tab);
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DashboardData _$DashboardDataFromJson(Map<String, dynamic> json) =>
    _DashboardData(
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      summary: json['summary'] == null
          ? null
          : DashboardSummary.fromJson(json['summary'] as Map<String, dynamic>),
      alerts: (json['alerts'] as List<dynamic>?)
          ?.map((e) => DashboardAlert.fromJson(e as Map<String, dynamic>))
          .toList(),
      quickStats: json['quickStats'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$DashboardDataToJson(_DashboardData instance) =>
    <String, dynamic>{
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'summary': instance.summary,
      'alerts': instance.alerts,
      'quickStats': instance.quickStats,
      'metadata': instance.metadata,
    };

_DashboardSummary _$DashboardSummaryFromJson(Map<String, dynamic> json) =>
    _DashboardSummary(
      totalIncome: (json['totalIncome'] as num?)?.toDouble(),
      totalOrders: (json['totalOrders'] as num?)?.toInt(),
      bidAcceptance: (json['bidAcceptance'] as num?)?.toDouble(),
      tripCompletion: (json['tripCompletion'] as num?)?.toDouble(),
      totalPoints: (json['totalPoints'] as num?)?.toInt(),
      currentLevel: json['currentLevel'] as String?,
      additionalMetrics: json['additionalMetrics'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$DashboardSummaryToJson(_DashboardSummary instance) =>
    <String, dynamic>{
      'totalIncome': instance.totalIncome,
      'totalOrders': instance.totalOrders,
      'bidAcceptance': instance.bidAcceptance,
      'tripCompletion': instance.tripCompletion,
      'totalPoints': instance.totalPoints,
      'currentLevel': instance.currentLevel,
      'additionalMetrics': instance.additionalMetrics,
    };

_DashboardAlert _$DashboardAlertFromJson(Map<String, dynamic> json) =>
    _DashboardAlert(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      priority: $enumDecode(_$AlertPriorityEnumMap, json['priority']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      actionLabel: json['actionLabel'] as String?,
      actionRoute: json['actionRoute'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      isDismissed: json['isDismissed'] as bool? ?? false,
    );

Map<String, dynamic> _$DashboardAlertToJson(_DashboardAlert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'priority': _$AlertPriorityEnumMap[instance.priority]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'actionLabel': instance.actionLabel,
      'actionRoute': instance.actionRoute,
      'metadata': instance.metadata,
      'isDismissed': instance.isDismissed,
    };

const _$AlertPriorityEnumMap = {
  AlertPriority.low: 'low',
  AlertPriority.medium: 'medium',
  AlertPriority.high: 'high',
  AlertPriority.critical: 'critical',
};

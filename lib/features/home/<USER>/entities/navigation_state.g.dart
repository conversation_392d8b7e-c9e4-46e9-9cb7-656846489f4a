// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NavigationState _$NavigationStateFromJson(Map<String, dynamic> json) =>
    _NavigationState(
      selectedTab:
          $enumDecodeNullable(_$NavigationTabEnumMap, json['selectedTab']) ??
          NavigationTab.income,
      isNavigationEnabled: json['isNavigationEnabled'] as bool? ?? true,
      tabVisibility: (json['tabVisibility'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry($enumDecode(_$NavigationTabEnumMap, k), e as bool),
      ),
      badgeCounts: (json['badgeCounts'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
          $enumDecode(_$NavigationTabEnumMap, k),
          (e as num).toInt(),
        ),
      ),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$NavigationStateToJson(_NavigationState instance) =>
    <String, dynamic>{
      'selectedTab': _$NavigationTabEnumMap[instance.selectedTab]!,
      'isNavigationEnabled': instance.isNavigationEnabled,
      'tabVisibility': instance.tabVisibility?.map(
        (k, e) => MapEntry(_$NavigationTabEnumMap[k]!, e),
      ),
      'badgeCounts': instance.badgeCounts?.map(
        (k, e) => MapEntry(_$NavigationTabEnumMap[k]!, e),
      ),
      'metadata': instance.metadata,
    };

const _$NavigationTabEnumMap = {
  NavigationTab.income: 'income',
  NavigationTab.orders: 'orders',
  NavigationTab.performance: 'performance',
  NavigationTab.more: 'more',
};

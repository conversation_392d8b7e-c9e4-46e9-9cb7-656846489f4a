// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'navigation_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$NavigationState {

 NavigationTab get selectedTab; bool get isNavigationEnabled; Map<NavigationTab, bool>? get tabVisibility; Map<NavigationTab, int>? get badgeCounts; Map<String, dynamic>? get metadata;
/// Create a copy of NavigationState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NavigationStateCopyWith<NavigationState> get copyWith => _$NavigationStateCopyWithImpl<NavigationState>(this as NavigationState, _$identity);

  /// Serializes this NavigationState to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigationState&&(identical(other.selectedTab, selectedTab) || other.selectedTab == selectedTab)&&(identical(other.isNavigationEnabled, isNavigationEnabled) || other.isNavigationEnabled == isNavigationEnabled)&&const DeepCollectionEquality().equals(other.tabVisibility, tabVisibility)&&const DeepCollectionEquality().equals(other.badgeCounts, badgeCounts)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,selectedTab,isNavigationEnabled,const DeepCollectionEquality().hash(tabVisibility),const DeepCollectionEquality().hash(badgeCounts),const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'NavigationState(selectedTab: $selectedTab, isNavigationEnabled: $isNavigationEnabled, tabVisibility: $tabVisibility, badgeCounts: $badgeCounts, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $NavigationStateCopyWith<$Res>  {
  factory $NavigationStateCopyWith(NavigationState value, $Res Function(NavigationState) _then) = _$NavigationStateCopyWithImpl;
@useResult
$Res call({
 NavigationTab selectedTab, bool isNavigationEnabled, Map<NavigationTab, bool>? tabVisibility, Map<NavigationTab, int>? badgeCounts, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$NavigationStateCopyWithImpl<$Res>
    implements $NavigationStateCopyWith<$Res> {
  _$NavigationStateCopyWithImpl(this._self, this._then);

  final NavigationState _self;
  final $Res Function(NavigationState) _then;

/// Create a copy of NavigationState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? selectedTab = null,Object? isNavigationEnabled = null,Object? tabVisibility = freezed,Object? badgeCounts = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
selectedTab: null == selectedTab ? _self.selectedTab : selectedTab // ignore: cast_nullable_to_non_nullable
as NavigationTab,isNavigationEnabled: null == isNavigationEnabled ? _self.isNavigationEnabled : isNavigationEnabled // ignore: cast_nullable_to_non_nullable
as bool,tabVisibility: freezed == tabVisibility ? _self.tabVisibility : tabVisibility // ignore: cast_nullable_to_non_nullable
as Map<NavigationTab, bool>?,badgeCounts: freezed == badgeCounts ? _self.badgeCounts : badgeCounts // ignore: cast_nullable_to_non_nullable
as Map<NavigationTab, int>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [NavigationState].
extension NavigationStatePatterns on NavigationState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NavigationState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NavigationState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NavigationState value)  $default,){
final _that = this;
switch (_that) {
case _NavigationState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NavigationState value)?  $default,){
final _that = this;
switch (_that) {
case _NavigationState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( NavigationTab selectedTab,  bool isNavigationEnabled,  Map<NavigationTab, bool>? tabVisibility,  Map<NavigationTab, int>? badgeCounts,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NavigationState() when $default != null:
return $default(_that.selectedTab,_that.isNavigationEnabled,_that.tabVisibility,_that.badgeCounts,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( NavigationTab selectedTab,  bool isNavigationEnabled,  Map<NavigationTab, bool>? tabVisibility,  Map<NavigationTab, int>? badgeCounts,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _NavigationState():
return $default(_that.selectedTab,_that.isNavigationEnabled,_that.tabVisibility,_that.badgeCounts,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( NavigationTab selectedTab,  bool isNavigationEnabled,  Map<NavigationTab, bool>? tabVisibility,  Map<NavigationTab, int>? badgeCounts,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _NavigationState() when $default != null:
return $default(_that.selectedTab,_that.isNavigationEnabled,_that.tabVisibility,_that.badgeCounts,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NavigationState extends NavigationState {
  const _NavigationState({this.selectedTab = NavigationTab.income, this.isNavigationEnabled = true, final  Map<NavigationTab, bool>? tabVisibility, final  Map<NavigationTab, int>? badgeCounts, final  Map<String, dynamic>? metadata}): _tabVisibility = tabVisibility,_badgeCounts = badgeCounts,_metadata = metadata,super._();
  factory _NavigationState.fromJson(Map<String, dynamic> json) => _$NavigationStateFromJson(json);

@override@JsonKey() final  NavigationTab selectedTab;
@override@JsonKey() final  bool isNavigationEnabled;
 final  Map<NavigationTab, bool>? _tabVisibility;
@override Map<NavigationTab, bool>? get tabVisibility {
  final value = _tabVisibility;
  if (value == null) return null;
  if (_tabVisibility is EqualUnmodifiableMapView) return _tabVisibility;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<NavigationTab, int>? _badgeCounts;
@override Map<NavigationTab, int>? get badgeCounts {
  final value = _badgeCounts;
  if (value == null) return null;
  if (_badgeCounts is EqualUnmodifiableMapView) return _badgeCounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of NavigationState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NavigationStateCopyWith<_NavigationState> get copyWith => __$NavigationStateCopyWithImpl<_NavigationState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NavigationStateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigationState&&(identical(other.selectedTab, selectedTab) || other.selectedTab == selectedTab)&&(identical(other.isNavigationEnabled, isNavigationEnabled) || other.isNavigationEnabled == isNavigationEnabled)&&const DeepCollectionEquality().equals(other._tabVisibility, _tabVisibility)&&const DeepCollectionEquality().equals(other._badgeCounts, _badgeCounts)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,selectedTab,isNavigationEnabled,const DeepCollectionEquality().hash(_tabVisibility),const DeepCollectionEquality().hash(_badgeCounts),const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'NavigationState(selectedTab: $selectedTab, isNavigationEnabled: $isNavigationEnabled, tabVisibility: $tabVisibility, badgeCounts: $badgeCounts, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$NavigationStateCopyWith<$Res> implements $NavigationStateCopyWith<$Res> {
  factory _$NavigationStateCopyWith(_NavigationState value, $Res Function(_NavigationState) _then) = __$NavigationStateCopyWithImpl;
@override @useResult
$Res call({
 NavigationTab selectedTab, bool isNavigationEnabled, Map<NavigationTab, bool>? tabVisibility, Map<NavigationTab, int>? badgeCounts, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$NavigationStateCopyWithImpl<$Res>
    implements _$NavigationStateCopyWith<$Res> {
  __$NavigationStateCopyWithImpl(this._self, this._then);

  final _NavigationState _self;
  final $Res Function(_NavigationState) _then;

/// Create a copy of NavigationState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedTab = null,Object? isNavigationEnabled = null,Object? tabVisibility = freezed,Object? badgeCounts = freezed,Object? metadata = freezed,}) {
  return _then(_NavigationState(
selectedTab: null == selectedTab ? _self.selectedTab : selectedTab // ignore: cast_nullable_to_non_nullable
as NavigationTab,isNavigationEnabled: null == isNavigationEnabled ? _self.isNavigationEnabled : isNavigationEnabled // ignore: cast_nullable_to_non_nullable
as bool,tabVisibility: freezed == tabVisibility ? _self._tabVisibility : tabVisibility // ignore: cast_nullable_to_non_nullable
as Map<NavigationTab, bool>?,badgeCounts: freezed == badgeCounts ? _self._badgeCounts : badgeCounts // ignore: cast_nullable_to_non_nullable
as Map<NavigationTab, int>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

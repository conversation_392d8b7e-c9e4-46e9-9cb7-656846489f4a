import 'package:freezed_annotation/freezed_annotation.dart';

part 'dashboard_data.freezed.dart';
part 'dashboard_data.g.dart';

/// Domain entity representing aggregated dashboard data
@freezed
sealed class DashboardData with _$DashboardData {
  const DashboardData._();

  const factory DashboardData({
    required DateTime lastUpdated,
    DashboardSummary? summary,
    List<DashboardAlert>? alerts,
    Map<String, dynamic>? quickStats,
    Map<String, dynamic>? metadata,
  }) = _DashboardData;

  factory DashboardData.fromJson(Map<String, dynamic> json) => _$DashboardDataFromJson(json);

  /// Create empty dashboard data
  factory DashboardData.empty() {
    return DashboardData(
      lastUpdated: DateTime.now(),
      summary: DashboardSummary.empty(),
      alerts: [],
      quickStats: {},
    );
  }

  /// Check if dashboard has any alerts
  bool get hasAlerts => alerts != null && alerts!.isNotEmpty;

  /// Get high priority alerts
  List<DashboardAlert> get highPriorityAlerts {
    if (alerts == null) return [];
    return alerts!.where((alert) => alert.priority == AlertPriority.high).toList();
  }

  /// Check if data is stale (older than 1 hour)
  bool get isStale {
    final now = DateTime.now();
    return now.difference(lastUpdated).inHours >= 1;
  }

  /// Get formatted last updated time
  String get formattedLastUpdated {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

/// Summary data for the dashboard
@freezed
sealed class DashboardSummary with _$DashboardSummary {
  const DashboardSummary._();

  const factory DashboardSummary({
    double? totalIncome,
    int? totalOrders,
    double? bidAcceptance,
    double? tripCompletion,
    int? totalPoints,
    String? currentLevel,
    Map<String, dynamic>? additionalMetrics,
  }) = _DashboardSummary;

  factory DashboardSummary.fromJson(Map<String, dynamic> json) => _$DashboardSummaryFromJson(json);

  /// Create empty summary
  factory DashboardSummary.empty() {
    return const DashboardSummary(
      totalIncome: 0.0,
      totalOrders: 0,
      bidAcceptance: 0.0,
      tripCompletion: 0.0,
      totalPoints: 0,
      currentLevel: 'Basic',
      additionalMetrics: {},
    );
  }

  /// Check if summary has meaningful data
  bool get hasData {
    return (totalIncome ?? 0) > 0 || 
           (totalOrders ?? 0) > 0 || 
           (totalPoints ?? 0) > 0;
  }

  /// Get formatted income
  String get formattedIncome {
    if (totalIncome == null) return '\$0.00';
    return '\$${totalIncome!.toStringAsFixed(2)}';
  }

  /// Get formatted bid acceptance
  String get formattedBidAcceptance {
    if (bidAcceptance == null) return '0%';
    return '${(bidAcceptance! * 100).toStringAsFixed(1)}%';
  }

  /// Get formatted trip completion
  String get formattedTripCompletion {
    if (tripCompletion == null) return '0%';
    return '${(tripCompletion! * 100).toStringAsFixed(1)}%';
  }
}

/// Alert priority levels
enum AlertPriority {
  low,
  medium,
  high,
  critical,
}

/// Dashboard alert entity
@freezed
sealed class DashboardAlert with _$DashboardAlert {
  const DashboardAlert._();

  const factory DashboardAlert({
    required String id,
    required String title,
    required String message,
    required AlertPriority priority,
    required DateTime createdAt,
    String? actionLabel,
    String? actionRoute,
    Map<String, dynamic>? metadata,
    @Default(false) bool isDismissed,
  }) = _DashboardAlert;

  factory DashboardAlert.fromJson(Map<String, dynamic> json) => _$DashboardAlertFromJson(json);

  /// Create a low priority alert
  factory DashboardAlert.info({
    required String id,
    required String title,
    required String message,
    String? actionLabel,
    String? actionRoute,
  }) {
    return DashboardAlert(
      id: id,
      title: title,
      message: message,
      priority: AlertPriority.low,
      createdAt: DateTime.now(),
      actionLabel: actionLabel,
      actionRoute: actionRoute,
    );
  }

  /// Create a warning alert
  factory DashboardAlert.warning({
    required String id,
    required String title,
    required String message,
    String? actionLabel,
    String? actionRoute,
  }) {
    return DashboardAlert(
      id: id,
      title: title,
      message: message,
      priority: AlertPriority.medium,
      createdAt: DateTime.now(),
      actionLabel: actionLabel,
      actionRoute: actionRoute,
    );
  }

  /// Create a critical alert
  factory DashboardAlert.critical({
    required String id,
    required String title,
    required String message,
    String? actionLabel,
    String? actionRoute,
  }) {
    return DashboardAlert(
      id: id,
      title: title,
      message: message,
      priority: AlertPriority.critical,
      createdAt: DateTime.now(),
      actionLabel: actionLabel,
      actionRoute: actionRoute,
    );
  }

  /// Check if alert has an action
  bool get hasAction => actionLabel != null && actionRoute != null;

  /// Get priority color
  String get priorityColor {
    switch (priority) {
      case AlertPriority.low:
        return 'blue';
      case AlertPriority.medium:
        return 'orange';
      case AlertPriority.high:
        return 'red';
      case AlertPriority.critical:
        return 'darkred';
    }
  }

  /// Check if alert is recent (created within last 24 hours)
  bool get isRecent {
    final now = DateTime.now();
    return now.difference(createdAt).inHours < 24;
  }
}

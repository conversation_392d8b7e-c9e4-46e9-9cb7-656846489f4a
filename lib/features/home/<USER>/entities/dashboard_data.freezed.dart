// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DashboardData {

 DateTime get lastUpdated; DashboardSummary? get summary; List<DashboardAlert>? get alerts; Map<String, dynamic>? get quickStats; Map<String, dynamic>? get metadata;
/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DashboardDataCopyWith<DashboardData> get copyWith => _$DashboardDataCopyWithImpl<DashboardData>(this as DashboardData, _$identity);

  /// Serializes this DashboardData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DashboardData&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.summary, summary) || other.summary == summary)&&const DeepCollectionEquality().equals(other.alerts, alerts)&&const DeepCollectionEquality().equals(other.quickStats, quickStats)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lastUpdated,summary,const DeepCollectionEquality().hash(alerts),const DeepCollectionEquality().hash(quickStats),const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'DashboardData(lastUpdated: $lastUpdated, summary: $summary, alerts: $alerts, quickStats: $quickStats, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $DashboardDataCopyWith<$Res>  {
  factory $DashboardDataCopyWith(DashboardData value, $Res Function(DashboardData) _then) = _$DashboardDataCopyWithImpl;
@useResult
$Res call({
 DateTime lastUpdated, DashboardSummary? summary, List<DashboardAlert>? alerts, Map<String, dynamic>? quickStats, Map<String, dynamic>? metadata
});


$DashboardSummaryCopyWith<$Res>? get summary;

}
/// @nodoc
class _$DashboardDataCopyWithImpl<$Res>
    implements $DashboardDataCopyWith<$Res> {
  _$DashboardDataCopyWithImpl(this._self, this._then);

  final DashboardData _self;
  final $Res Function(DashboardData) _then;

/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? lastUpdated = null,Object? summary = freezed,Object? alerts = freezed,Object? quickStats = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,summary: freezed == summary ? _self.summary : summary // ignore: cast_nullable_to_non_nullable
as DashboardSummary?,alerts: freezed == alerts ? _self.alerts : alerts // ignore: cast_nullable_to_non_nullable
as List<DashboardAlert>?,quickStats: freezed == quickStats ? _self.quickStats : quickStats // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}
/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DashboardSummaryCopyWith<$Res>? get summary {
    if (_self.summary == null) {
    return null;
  }

  return $DashboardSummaryCopyWith<$Res>(_self.summary!, (value) {
    return _then(_self.copyWith(summary: value));
  });
}
}


/// Adds pattern-matching-related methods to [DashboardData].
extension DashboardDataPatterns on DashboardData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DashboardData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DashboardData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DashboardData value)  $default,){
final _that = this;
switch (_that) {
case _DashboardData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DashboardData value)?  $default,){
final _that = this;
switch (_that) {
case _DashboardData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime lastUpdated,  DashboardSummary? summary,  List<DashboardAlert>? alerts,  Map<String, dynamic>? quickStats,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DashboardData() when $default != null:
return $default(_that.lastUpdated,_that.summary,_that.alerts,_that.quickStats,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime lastUpdated,  DashboardSummary? summary,  List<DashboardAlert>? alerts,  Map<String, dynamic>? quickStats,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _DashboardData():
return $default(_that.lastUpdated,_that.summary,_that.alerts,_that.quickStats,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime lastUpdated,  DashboardSummary? summary,  List<DashboardAlert>? alerts,  Map<String, dynamic>? quickStats,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _DashboardData() when $default != null:
return $default(_that.lastUpdated,_that.summary,_that.alerts,_that.quickStats,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DashboardData extends DashboardData {
  const _DashboardData({required this.lastUpdated, this.summary, final  List<DashboardAlert>? alerts, final  Map<String, dynamic>? quickStats, final  Map<String, dynamic>? metadata}): _alerts = alerts,_quickStats = quickStats,_metadata = metadata,super._();
  factory _DashboardData.fromJson(Map<String, dynamic> json) => _$DashboardDataFromJson(json);

@override final  DateTime lastUpdated;
@override final  DashboardSummary? summary;
 final  List<DashboardAlert>? _alerts;
@override List<DashboardAlert>? get alerts {
  final value = _alerts;
  if (value == null) return null;
  if (_alerts is EqualUnmodifiableListView) return _alerts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, dynamic>? _quickStats;
@override Map<String, dynamic>? get quickStats {
  final value = _quickStats;
  if (value == null) return null;
  if (_quickStats is EqualUnmodifiableMapView) return _quickStats;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DashboardDataCopyWith<_DashboardData> get copyWith => __$DashboardDataCopyWithImpl<_DashboardData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DashboardDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DashboardData&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.summary, summary) || other.summary == summary)&&const DeepCollectionEquality().equals(other._alerts, _alerts)&&const DeepCollectionEquality().equals(other._quickStats, _quickStats)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lastUpdated,summary,const DeepCollectionEquality().hash(_alerts),const DeepCollectionEquality().hash(_quickStats),const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'DashboardData(lastUpdated: $lastUpdated, summary: $summary, alerts: $alerts, quickStats: $quickStats, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$DashboardDataCopyWith<$Res> implements $DashboardDataCopyWith<$Res> {
  factory _$DashboardDataCopyWith(_DashboardData value, $Res Function(_DashboardData) _then) = __$DashboardDataCopyWithImpl;
@override @useResult
$Res call({
 DateTime lastUpdated, DashboardSummary? summary, List<DashboardAlert>? alerts, Map<String, dynamic>? quickStats, Map<String, dynamic>? metadata
});


@override $DashboardSummaryCopyWith<$Res>? get summary;

}
/// @nodoc
class __$DashboardDataCopyWithImpl<$Res>
    implements _$DashboardDataCopyWith<$Res> {
  __$DashboardDataCopyWithImpl(this._self, this._then);

  final _DashboardData _self;
  final $Res Function(_DashboardData) _then;

/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? lastUpdated = null,Object? summary = freezed,Object? alerts = freezed,Object? quickStats = freezed,Object? metadata = freezed,}) {
  return _then(_DashboardData(
lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,summary: freezed == summary ? _self.summary : summary // ignore: cast_nullable_to_non_nullable
as DashboardSummary?,alerts: freezed == alerts ? _self._alerts : alerts // ignore: cast_nullable_to_non_nullable
as List<DashboardAlert>?,quickStats: freezed == quickStats ? _self._quickStats : quickStats // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DashboardSummaryCopyWith<$Res>? get summary {
    if (_self.summary == null) {
    return null;
  }

  return $DashboardSummaryCopyWith<$Res>(_self.summary!, (value) {
    return _then(_self.copyWith(summary: value));
  });
}
}


/// @nodoc
mixin _$DashboardSummary {

 double? get totalIncome; int? get totalOrders; double? get bidAcceptance; double? get tripCompletion; int? get totalPoints; String? get currentLevel; Map<String, dynamic>? get additionalMetrics;
/// Create a copy of DashboardSummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DashboardSummaryCopyWith<DashboardSummary> get copyWith => _$DashboardSummaryCopyWithImpl<DashboardSummary>(this as DashboardSummary, _$identity);

  /// Serializes this DashboardSummary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DashboardSummary&&(identical(other.totalIncome, totalIncome) || other.totalIncome == totalIncome)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.bidAcceptance, bidAcceptance) || other.bidAcceptance == bidAcceptance)&&(identical(other.tripCompletion, tripCompletion) || other.tripCompletion == tripCompletion)&&(identical(other.totalPoints, totalPoints) || other.totalPoints == totalPoints)&&(identical(other.currentLevel, currentLevel) || other.currentLevel == currentLevel)&&const DeepCollectionEquality().equals(other.additionalMetrics, additionalMetrics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalIncome,totalOrders,bidAcceptance,tripCompletion,totalPoints,currentLevel,const DeepCollectionEquality().hash(additionalMetrics));

@override
String toString() {
  return 'DashboardSummary(totalIncome: $totalIncome, totalOrders: $totalOrders, bidAcceptance: $bidAcceptance, tripCompletion: $tripCompletion, totalPoints: $totalPoints, currentLevel: $currentLevel, additionalMetrics: $additionalMetrics)';
}


}

/// @nodoc
abstract mixin class $DashboardSummaryCopyWith<$Res>  {
  factory $DashboardSummaryCopyWith(DashboardSummary value, $Res Function(DashboardSummary) _then) = _$DashboardSummaryCopyWithImpl;
@useResult
$Res call({
 double? totalIncome, int? totalOrders, double? bidAcceptance, double? tripCompletion, int? totalPoints, String? currentLevel, Map<String, dynamic>? additionalMetrics
});




}
/// @nodoc
class _$DashboardSummaryCopyWithImpl<$Res>
    implements $DashboardSummaryCopyWith<$Res> {
  _$DashboardSummaryCopyWithImpl(this._self, this._then);

  final DashboardSummary _self;
  final $Res Function(DashboardSummary) _then;

/// Create a copy of DashboardSummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalIncome = freezed,Object? totalOrders = freezed,Object? bidAcceptance = freezed,Object? tripCompletion = freezed,Object? totalPoints = freezed,Object? currentLevel = freezed,Object? additionalMetrics = freezed,}) {
  return _then(_self.copyWith(
totalIncome: freezed == totalIncome ? _self.totalIncome : totalIncome // ignore: cast_nullable_to_non_nullable
as double?,totalOrders: freezed == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int?,bidAcceptance: freezed == bidAcceptance ? _self.bidAcceptance : bidAcceptance // ignore: cast_nullable_to_non_nullable
as double?,tripCompletion: freezed == tripCompletion ? _self.tripCompletion : tripCompletion // ignore: cast_nullable_to_non_nullable
as double?,totalPoints: freezed == totalPoints ? _self.totalPoints : totalPoints // ignore: cast_nullable_to_non_nullable
as int?,currentLevel: freezed == currentLevel ? _self.currentLevel : currentLevel // ignore: cast_nullable_to_non_nullable
as String?,additionalMetrics: freezed == additionalMetrics ? _self.additionalMetrics : additionalMetrics // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [DashboardSummary].
extension DashboardSummaryPatterns on DashboardSummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DashboardSummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DashboardSummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DashboardSummary value)  $default,){
final _that = this;
switch (_that) {
case _DashboardSummary():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DashboardSummary value)?  $default,){
final _that = this;
switch (_that) {
case _DashboardSummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double? totalIncome,  int? totalOrders,  double? bidAcceptance,  double? tripCompletion,  int? totalPoints,  String? currentLevel,  Map<String, dynamic>? additionalMetrics)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DashboardSummary() when $default != null:
return $default(_that.totalIncome,_that.totalOrders,_that.bidAcceptance,_that.tripCompletion,_that.totalPoints,_that.currentLevel,_that.additionalMetrics);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double? totalIncome,  int? totalOrders,  double? bidAcceptance,  double? tripCompletion,  int? totalPoints,  String? currentLevel,  Map<String, dynamic>? additionalMetrics)  $default,) {final _that = this;
switch (_that) {
case _DashboardSummary():
return $default(_that.totalIncome,_that.totalOrders,_that.bidAcceptance,_that.tripCompletion,_that.totalPoints,_that.currentLevel,_that.additionalMetrics);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double? totalIncome,  int? totalOrders,  double? bidAcceptance,  double? tripCompletion,  int? totalPoints,  String? currentLevel,  Map<String, dynamic>? additionalMetrics)?  $default,) {final _that = this;
switch (_that) {
case _DashboardSummary() when $default != null:
return $default(_that.totalIncome,_that.totalOrders,_that.bidAcceptance,_that.tripCompletion,_that.totalPoints,_that.currentLevel,_that.additionalMetrics);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DashboardSummary extends DashboardSummary {
  const _DashboardSummary({this.totalIncome, this.totalOrders, this.bidAcceptance, this.tripCompletion, this.totalPoints, this.currentLevel, final  Map<String, dynamic>? additionalMetrics}): _additionalMetrics = additionalMetrics,super._();
  factory _DashboardSummary.fromJson(Map<String, dynamic> json) => _$DashboardSummaryFromJson(json);

@override final  double? totalIncome;
@override final  int? totalOrders;
@override final  double? bidAcceptance;
@override final  double? tripCompletion;
@override final  int? totalPoints;
@override final  String? currentLevel;
 final  Map<String, dynamic>? _additionalMetrics;
@override Map<String, dynamic>? get additionalMetrics {
  final value = _additionalMetrics;
  if (value == null) return null;
  if (_additionalMetrics is EqualUnmodifiableMapView) return _additionalMetrics;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of DashboardSummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DashboardSummaryCopyWith<_DashboardSummary> get copyWith => __$DashboardSummaryCopyWithImpl<_DashboardSummary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DashboardSummaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DashboardSummary&&(identical(other.totalIncome, totalIncome) || other.totalIncome == totalIncome)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.bidAcceptance, bidAcceptance) || other.bidAcceptance == bidAcceptance)&&(identical(other.tripCompletion, tripCompletion) || other.tripCompletion == tripCompletion)&&(identical(other.totalPoints, totalPoints) || other.totalPoints == totalPoints)&&(identical(other.currentLevel, currentLevel) || other.currentLevel == currentLevel)&&const DeepCollectionEquality().equals(other._additionalMetrics, _additionalMetrics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalIncome,totalOrders,bidAcceptance,tripCompletion,totalPoints,currentLevel,const DeepCollectionEquality().hash(_additionalMetrics));

@override
String toString() {
  return 'DashboardSummary(totalIncome: $totalIncome, totalOrders: $totalOrders, bidAcceptance: $bidAcceptance, tripCompletion: $tripCompletion, totalPoints: $totalPoints, currentLevel: $currentLevel, additionalMetrics: $additionalMetrics)';
}


}

/// @nodoc
abstract mixin class _$DashboardSummaryCopyWith<$Res> implements $DashboardSummaryCopyWith<$Res> {
  factory _$DashboardSummaryCopyWith(_DashboardSummary value, $Res Function(_DashboardSummary) _then) = __$DashboardSummaryCopyWithImpl;
@override @useResult
$Res call({
 double? totalIncome, int? totalOrders, double? bidAcceptance, double? tripCompletion, int? totalPoints, String? currentLevel, Map<String, dynamic>? additionalMetrics
});




}
/// @nodoc
class __$DashboardSummaryCopyWithImpl<$Res>
    implements _$DashboardSummaryCopyWith<$Res> {
  __$DashboardSummaryCopyWithImpl(this._self, this._then);

  final _DashboardSummary _self;
  final $Res Function(_DashboardSummary) _then;

/// Create a copy of DashboardSummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalIncome = freezed,Object? totalOrders = freezed,Object? bidAcceptance = freezed,Object? tripCompletion = freezed,Object? totalPoints = freezed,Object? currentLevel = freezed,Object? additionalMetrics = freezed,}) {
  return _then(_DashboardSummary(
totalIncome: freezed == totalIncome ? _self.totalIncome : totalIncome // ignore: cast_nullable_to_non_nullable
as double?,totalOrders: freezed == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int?,bidAcceptance: freezed == bidAcceptance ? _self.bidAcceptance : bidAcceptance // ignore: cast_nullable_to_non_nullable
as double?,tripCompletion: freezed == tripCompletion ? _self.tripCompletion : tripCompletion // ignore: cast_nullable_to_non_nullable
as double?,totalPoints: freezed == totalPoints ? _self.totalPoints : totalPoints // ignore: cast_nullable_to_non_nullable
as int?,currentLevel: freezed == currentLevel ? _self.currentLevel : currentLevel // ignore: cast_nullable_to_non_nullable
as String?,additionalMetrics: freezed == additionalMetrics ? _self._additionalMetrics : additionalMetrics // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$DashboardAlert {

 String get id; String get title; String get message; AlertPriority get priority; DateTime get createdAt; String? get actionLabel; String? get actionRoute; Map<String, dynamic>? get metadata; bool get isDismissed;
/// Create a copy of DashboardAlert
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DashboardAlertCopyWith<DashboardAlert> get copyWith => _$DashboardAlertCopyWithImpl<DashboardAlert>(this as DashboardAlert, _$identity);

  /// Serializes this DashboardAlert to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DashboardAlert&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.actionLabel, actionLabel) || other.actionLabel == actionLabel)&&(identical(other.actionRoute, actionRoute) || other.actionRoute == actionRoute)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.isDismissed, isDismissed) || other.isDismissed == isDismissed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,message,priority,createdAt,actionLabel,actionRoute,const DeepCollectionEquality().hash(metadata),isDismissed);

@override
String toString() {
  return 'DashboardAlert(id: $id, title: $title, message: $message, priority: $priority, createdAt: $createdAt, actionLabel: $actionLabel, actionRoute: $actionRoute, metadata: $metadata, isDismissed: $isDismissed)';
}


}

/// @nodoc
abstract mixin class $DashboardAlertCopyWith<$Res>  {
  factory $DashboardAlertCopyWith(DashboardAlert value, $Res Function(DashboardAlert) _then) = _$DashboardAlertCopyWithImpl;
@useResult
$Res call({
 String id, String title, String message, AlertPriority priority, DateTime createdAt, String? actionLabel, String? actionRoute, Map<String, dynamic>? metadata, bool isDismissed
});




}
/// @nodoc
class _$DashboardAlertCopyWithImpl<$Res>
    implements $DashboardAlertCopyWith<$Res> {
  _$DashboardAlertCopyWithImpl(this._self, this._then);

  final DashboardAlert _self;
  final $Res Function(DashboardAlert) _then;

/// Create a copy of DashboardAlert
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? title = null,Object? message = null,Object? priority = null,Object? createdAt = null,Object? actionLabel = freezed,Object? actionRoute = freezed,Object? metadata = freezed,Object? isDismissed = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as AlertPriority,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,actionLabel: freezed == actionLabel ? _self.actionLabel : actionLabel // ignore: cast_nullable_to_non_nullable
as String?,actionRoute: freezed == actionRoute ? _self.actionRoute : actionRoute // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,isDismissed: null == isDismissed ? _self.isDismissed : isDismissed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [DashboardAlert].
extension DashboardAlertPatterns on DashboardAlert {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DashboardAlert value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DashboardAlert() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DashboardAlert value)  $default,){
final _that = this;
switch (_that) {
case _DashboardAlert():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DashboardAlert value)?  $default,){
final _that = this;
switch (_that) {
case _DashboardAlert() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String title,  String message,  AlertPriority priority,  DateTime createdAt,  String? actionLabel,  String? actionRoute,  Map<String, dynamic>? metadata,  bool isDismissed)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DashboardAlert() when $default != null:
return $default(_that.id,_that.title,_that.message,_that.priority,_that.createdAt,_that.actionLabel,_that.actionRoute,_that.metadata,_that.isDismissed);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String title,  String message,  AlertPriority priority,  DateTime createdAt,  String? actionLabel,  String? actionRoute,  Map<String, dynamic>? metadata,  bool isDismissed)  $default,) {final _that = this;
switch (_that) {
case _DashboardAlert():
return $default(_that.id,_that.title,_that.message,_that.priority,_that.createdAt,_that.actionLabel,_that.actionRoute,_that.metadata,_that.isDismissed);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String title,  String message,  AlertPriority priority,  DateTime createdAt,  String? actionLabel,  String? actionRoute,  Map<String, dynamic>? metadata,  bool isDismissed)?  $default,) {final _that = this;
switch (_that) {
case _DashboardAlert() when $default != null:
return $default(_that.id,_that.title,_that.message,_that.priority,_that.createdAt,_that.actionLabel,_that.actionRoute,_that.metadata,_that.isDismissed);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DashboardAlert extends DashboardAlert {
  const _DashboardAlert({required this.id, required this.title, required this.message, required this.priority, required this.createdAt, this.actionLabel, this.actionRoute, final  Map<String, dynamic>? metadata, this.isDismissed = false}): _metadata = metadata,super._();
  factory _DashboardAlert.fromJson(Map<String, dynamic> json) => _$DashboardAlertFromJson(json);

@override final  String id;
@override final  String title;
@override final  String message;
@override final  AlertPriority priority;
@override final  DateTime createdAt;
@override final  String? actionLabel;
@override final  String? actionRoute;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey() final  bool isDismissed;

/// Create a copy of DashboardAlert
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DashboardAlertCopyWith<_DashboardAlert> get copyWith => __$DashboardAlertCopyWithImpl<_DashboardAlert>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DashboardAlertToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DashboardAlert&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.actionLabel, actionLabel) || other.actionLabel == actionLabel)&&(identical(other.actionRoute, actionRoute) || other.actionRoute == actionRoute)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.isDismissed, isDismissed) || other.isDismissed == isDismissed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,message,priority,createdAt,actionLabel,actionRoute,const DeepCollectionEquality().hash(_metadata),isDismissed);

@override
String toString() {
  return 'DashboardAlert(id: $id, title: $title, message: $message, priority: $priority, createdAt: $createdAt, actionLabel: $actionLabel, actionRoute: $actionRoute, metadata: $metadata, isDismissed: $isDismissed)';
}


}

/// @nodoc
abstract mixin class _$DashboardAlertCopyWith<$Res> implements $DashboardAlertCopyWith<$Res> {
  factory _$DashboardAlertCopyWith(_DashboardAlert value, $Res Function(_DashboardAlert) _then) = __$DashboardAlertCopyWithImpl;
@override @useResult
$Res call({
 String id, String title, String message, AlertPriority priority, DateTime createdAt, String? actionLabel, String? actionRoute, Map<String, dynamic>? metadata, bool isDismissed
});




}
/// @nodoc
class __$DashboardAlertCopyWithImpl<$Res>
    implements _$DashboardAlertCopyWith<$Res> {
  __$DashboardAlertCopyWithImpl(this._self, this._then);

  final _DashboardAlert _self;
  final $Res Function(_DashboardAlert) _then;

/// Create a copy of DashboardAlert
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? title = null,Object? message = null,Object? priority = null,Object? createdAt = null,Object? actionLabel = freezed,Object? actionRoute = freezed,Object? metadata = freezed,Object? isDismissed = null,}) {
  return _then(_DashboardAlert(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as AlertPriority,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,actionLabel: freezed == actionLabel ? _self.actionLabel : actionLabel // ignore: cast_nullable_to_non_nullable
as String?,actionRoute: freezed == actionRoute ? _self.actionRoute : actionRoute // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,isDismissed: null == isDismissed ? _self.isDismissed : isDismissed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on

/// Home feature barrel export file
///
/// This file provides a single import point for all home functionality:
/// ```dart
/// import 'package:bidtrakr/features/home/<USER>';
/// ```
library;

// Data layer
export 'data/repositories/home_repository_impl.dart';

// Domain layer
export 'domain/entities/dashboard_data.dart';
export 'domain/entities/navigation_state.dart';
export 'domain/repositories/home_repository.dart';
export 'domain/use_cases/get_dashboard_data.dart';
export 'domain/use_cases/update_navigation_state.dart';

// Presentation layer - providers
export 'presentation/providers/home_providers.dart';

// Presentation layer - screens
export 'presentation/screens/home_screen.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/providers/provider_templates.dart';
import '../../data/repositories/home_repository_impl.dart';
import '../../domain/entities/navigation_state.dart';
import '../../domain/repositories/home_repository.dart';
import '../../domain/use_cases/get_dashboard_data.dart';
import '../../domain/use_cases/update_navigation_state.dart';

/// Provider for the home repository
final homeRepositoryProvider = Provider<HomeRepository>((ref) {
  return HomeRepositoryImpl();
});

/// Provider for the get dashboard data use case
final getDashboardDataProvider = Provider<GetDashboardData>((ref) {
  return createUseCaseProvider<GetDashboardData, HomeRepository>(
    ref,
    homeRepositoryProvider,
    (repository) => GetDashboardData(repository),
  );
});

/// Provider for the update navigation state use case
final updateNavigationStateProvider = Provider<UpdateNavigationState>((ref) {
  return createUseCaseProvider<UpdateNavigationState, HomeRepository>(
    ref,
    homeRepositoryProvider,
    (repository) => UpdateNavigationState(repository),
  );
});

/// Provider for current navigation state (async)
final navigationStateProvider = FutureProvider<NavigationState>((ref) async {
  final updateNavigationState = ref.watch(updateNavigationStateProvider);
  final result = await updateNavigationState.getCurrentState();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (state) => state,
  );
});

/// Provider for selected navigation tab (async)
final selectedTabProvider = FutureProvider<NavigationTab>((ref) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.selectedTab;
});

/// Provider for dashboard data (async)
final dashboardDataProvider = FutureProvider((ref) async {
  final getDashboardData = ref.watch(getDashboardDataProvider);
  final result = await getDashboardData.execute();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (data) => data,
  );
});

/// Provider for dashboard summary (async)
final dashboardSummaryProvider = FutureProvider((ref) async {
  final getDashboardData = ref.watch(getDashboardDataProvider);
  final result = await getDashboardData.getSummary();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (summary) => summary,
  );
});

/// Provider for dashboard alerts (async)
final dashboardAlertsProvider = FutureProvider((ref) async {
  final getDashboardData = ref.watch(getDashboardDataProvider);
  final result = await getDashboardData.getAlerts();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (alerts) => alerts,
  );
});

/// Provider for active dashboard alerts (async)
final activeDashboardAlertsProvider = FutureProvider((ref) async {
  final getDashboardData = ref.watch(getDashboardDataProvider);
  final result = await getDashboardData.getActiveAlerts();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (alerts) => alerts,
  );
});

/// Provider for dashboard quick stats (async)
final dashboardQuickStatsProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final getDashboardData = ref.watch(getDashboardDataProvider);
  final result = await getDashboardData.getQuickStats();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (stats) => stats,
  );
});

/// Provider for dashboard status (async)
final dashboardStatusProvider = FutureProvider((ref) async {
  final getDashboardData = ref.watch(getDashboardDataProvider);
  final result = await getDashboardData.getStatus();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (status) => status,
  );
});

/// Provider for tab badge counts (async)
final tabBadgeCountsProvider = FutureProvider<Map<NavigationTab, int>>((
  ref,
) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.badgeCounts ?? {};
});

/// Provider for tab visibility (async)
final tabVisibilityProvider = FutureProvider<Map<NavigationTab, bool>>((
  ref,
) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.tabVisibility ??
      {
        NavigationTab.income: true,
        NavigationTab.orders: true,
        NavigationTab.performance: true,
        NavigationTab.more: true,
      };
});

/// Provider for visible tabs (async)
final visibleTabsProvider = FutureProvider<List<NavigationTab>>((ref) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.visibleTabs;
});

/// Provider for navigation enabled status (async)
final navigationEnabledProvider = FutureProvider<bool>((ref) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.isNavigationEnabled;
});

/// Provider for total badge count (async)
final totalBadgeCountProvider = FutureProvider<int>((ref) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.totalBadgeCount;
});

/// Provider for specific tab badge count (family)
final tabBadgeCountProvider = FutureProvider.family<int, NavigationTab>((
  ref,
  tab,
) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.getBadgeCount(tab);
});

/// Provider for checking if tab is visible (family)
final isTabVisibleProvider = FutureProvider.family<bool, NavigationTab>((
  ref,
  tab,
) async {
  final navigationState = await ref.watch(navigationStateProvider.future);
  return navigationState.isTabVisible(tab);
});

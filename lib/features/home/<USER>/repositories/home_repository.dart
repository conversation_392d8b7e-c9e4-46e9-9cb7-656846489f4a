import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/dashboard_data.dart';
import '../entities/navigation_state.dart';

/// Repository interface for home screen operations
abstract class HomeRepository {
  /// Get the current navigation state
  Future<Either<Failure, NavigationState>> getNavigationState();

  /// Update the navigation state
  Future<Either<Failure, NavigationState>> updateNavigationState(NavigationState state);

  /// Set the selected navigation tab
  Future<Either<Failure, NavigationState>> setSelectedTab(NavigationTab tab);

  /// Update tab visibility
  Future<Either<Failure, NavigationState>> updateTabVisibility(
    Map<NavigationTab, bool> visibility,
  );

  /// Update badge counts for tabs
  Future<Either<Failure, NavigationState>> updateBadgeCounts(
    Map<NavigationTab, int> badgeCounts,
  );

  /// Get dashboard data
  Future<Either<Failure, DashboardData>> getDashboardData();

  /// Refresh dashboard data
  Future<Either<Failure, DashboardData>> refreshDashboardData();

  /// Get dashboard summary
  Future<Either<Failure, DashboardSummary>> getDashboardSummary();

  /// Get dashboard alerts
  Future<Either<Failure, List<DashboardAlert>>> getDashboardAlerts();

  /// Dismiss a dashboard alert
  Future<Either<Failure, bool>> dismissAlert(String alertId);

  /// Add a dashboard alert
  Future<Either<Failure, DashboardAlert>> addAlert(DashboardAlert alert);

  /// Clear all dismissed alerts
  Future<Either<Failure, int>> clearDismissedAlerts();

  /// Get quick stats for dashboard
  Future<Either<Failure, Map<String, dynamic>>> getQuickStats();

  /// Listen to navigation state changes
  Stream<Either<Failure, NavigationState>> get navigationStateStream;

  /// Listen to dashboard data changes
  Stream<Either<Failure, DashboardData>> get dashboardDataStream;

  /// Initialize the home repository
  Future<Either<Failure, bool>> initialize();

  /// Check if the repository is initialized
  bool get isInitialized;
}

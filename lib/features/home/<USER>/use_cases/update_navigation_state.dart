import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/navigation_state.dart';
import '../repositories/home_repository.dart';

/// Use case for updating navigation state
class UpdateNavigationState {
  final HomeRepository repository;

  UpdateNavigationState(this.repository);

  /// Get current navigation state
  Future<Either<Failure, NavigationState>> getCurrentState() async {
    return await repository.getNavigationState();
  }

  /// Set the selected navigation tab
  Future<Either<Failure, NavigationState>> setSelectedTab(NavigationTab tab) async {
    // Get current state first to validate the tab change
    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        // Check if navigation to this tab is allowed
        if (!currentState.canNavigateTo(tab)) {
          return Left(Failure.businessLogic(
            message: 'Cannot navigate to ${currentState.getTabLabel(tab)} tab',
          ));
        }

        // Update the selected tab
        return await repository.setSelectedTab(tab);
      },
    );
  }

  /// Update tab visibility settings
  Future<Either<Failure, NavigationState>> updateTabVisibility(
    Map<NavigationTab, bool> visibility,
  ) async {
    // Validate that at least one tab remains visible
    final visibleTabs = visibility.values.where((isVisible) => isVisible).length;
    if (visibleTabs == 0) {
      return Left(Failure.invalidInput(message: 'At least one tab must remain visible'));
    }

    return await repository.updateTabVisibility(visibility);
  }

  /// Show a specific tab
  Future<Either<Failure, NavigationState>> showTab(NavigationTab tab) async {
    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final updatedVisibility = Map<NavigationTab, bool>.from(
          currentState.tabVisibility ?? {},
        );
        updatedVisibility[tab] = true;
        
        return await repository.updateTabVisibility(updatedVisibility);
      },
    );
  }

  /// Hide a specific tab
  Future<Either<Failure, NavigationState>> hideTab(NavigationTab tab) async {
    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        // Check if this would hide all tabs
        final currentVisibility = currentState.tabVisibility ?? {};
        final updatedVisibility = Map<NavigationTab, bool>.from(currentVisibility);
        updatedVisibility[tab] = false;
        
        final remainingVisibleTabs = updatedVisibility.values
            .where((isVisible) => isVisible)
            .length;
            
        if (remainingVisibleTabs == 0) {
          return Left(Failure.invalidInput(message: 'Cannot hide all tabs'));
        }

        // If hiding the currently selected tab, switch to the first visible tab
        if (currentState.selectedTab == tab) {
          final firstVisibleTab = NavigationTab.values.firstWhere(
            (t) => updatedVisibility[t] == true,
            orElse: () => NavigationTab.income,
          );
          
          // Update visibility first, then switch tab
          final visibilityResult = await repository.updateTabVisibility(updatedVisibility);
          
          return visibilityResult.fold(
            (failure) => Left(failure),
            (state) => repository.setSelectedTab(firstVisibleTab),
          );
        }
        
        return await repository.updateTabVisibility(updatedVisibility);
      },
    );
  }

  /// Update badge counts for tabs
  Future<Either<Failure, NavigationState>> updateBadgeCounts(
    Map<NavigationTab, int> badgeCounts,
  ) async {
    // Validate badge counts are non-negative
    for (final count in badgeCounts.values) {
      if (count < 0) {
        return Left(Failure.invalidInput(message: 'Badge counts cannot be negative'));
      }
    }

    return await repository.updateBadgeCounts(badgeCounts);
  }

  /// Set badge count for a specific tab
  Future<Either<Failure, NavigationState>> setBadgeCount(
    NavigationTab tab,
    int count,
  ) async {
    if (count < 0) {
      return Left(Failure.invalidInput(message: 'Badge count cannot be negative'));
    }

    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final updatedBadgeCounts = Map<NavigationTab, int>.from(
          currentState.badgeCounts ?? {},
        );
        updatedBadgeCounts[tab] = count;
        
        return await repository.updateBadgeCounts(updatedBadgeCounts);
      },
    );
  }

  /// Clear badge count for a specific tab
  Future<Either<Failure, NavigationState>> clearBadgeCount(NavigationTab tab) async {
    return await setBadgeCount(tab, 0);
  }

  /// Clear all badge counts
  Future<Either<Failure, NavigationState>> clearAllBadgeCounts() async {
    final badgeCounts = <NavigationTab, int>{};
    for (final tab in NavigationTab.values) {
      badgeCounts[tab] = 0;
    }
    
    return await repository.updateBadgeCounts(badgeCounts);
  }

  /// Enable navigation
  Future<Either<Failure, NavigationState>> enableNavigation() async {
    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final updatedState = currentState.copyWith(isNavigationEnabled: true);
        return await repository.updateNavigationState(updatedState);
      },
    );
  }

  /// Disable navigation
  Future<Either<Failure, NavigationState>> disableNavigation() async {
    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final updatedState = currentState.copyWith(isNavigationEnabled: false);
        return await repository.updateNavigationState(updatedState);
      },
    );
  }

  /// Reset navigation state to initial state
  Future<Either<Failure, NavigationState>> reset() async {
    final initialState = NavigationState.initial();
    return await repository.updateNavigationState(initialState);
  }

  /// Navigate to next tab
  Future<Either<Failure, NavigationState>> navigateToNext() async {
    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final visibleTabs = currentState.visibleTabs;
        if (visibleTabs.isEmpty) {
          return Left(Failure.businessLogic(message: 'No visible tabs available'));
        }

        final currentIndex = visibleTabs.indexOf(currentState.selectedTab);
        final nextIndex = (currentIndex + 1) % visibleTabs.length;
        final nextTab = visibleTabs[nextIndex];
        
        return await repository.setSelectedTab(nextTab);
      },
    );
  }

  /// Navigate to previous tab
  Future<Either<Failure, NavigationState>> navigateToPrevious() async {
    final currentStateResult = await repository.getNavigationState();
    
    return currentStateResult.fold(
      (failure) => Left(failure),
      (currentState) async {
        final visibleTabs = currentState.visibleTabs;
        if (visibleTabs.isEmpty) {
          return Left(Failure.businessLogic(message: 'No visible tabs available'));
        }

        final currentIndex = visibleTabs.indexOf(currentState.selectedTab);
        final previousIndex = currentIndex == 0 
            ? visibleTabs.length - 1 
            : currentIndex - 1;
        final previousTab = visibleTabs[previousIndex];
        
        return await repository.setSelectedTab(previousTab);
      },
    );
  }
}

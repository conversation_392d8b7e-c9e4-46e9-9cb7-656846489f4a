import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/dashboard_data.dart';
import '../repositories/home_repository.dart';

/// Use case for getting dashboard data
class GetDashboardData {
  final HomeRepository repository;

  GetDashboardData(this.repository);

  /// Get current dashboard data
  Future<Either<Failure, DashboardData>> execute() async {
    return await repository.getDashboardData();
  }

  /// Refresh dashboard data
  Future<Either<Failure, DashboardData>> refresh() async {
    return await repository.refreshDashboardData();
  }

  /// Get dashboard summary only
  Future<Either<Failure, DashboardSummary>> getSummary() async {
    return await repository.getDashboardSummary();
  }

  /// Get dashboard alerts only
  Future<Either<Failure, List<DashboardAlert>>> getAlerts() async {
    return await repository.getDashboardAlerts();
  }

  /// Get quick stats only
  Future<Either<Failure, Map<String, dynamic>>> getQuickStats() async {
    return await repository.getQuickStats();
  }

  /// Get filtered alerts by priority
  Future<Either<Failure, List<DashboardAlert>>> getAlertsByPriority(
    AlertPriority priority,
  ) async {
    final alertsResult = await repository.getDashboardAlerts();
    
    return alertsResult.fold(
      (failure) => Left(failure),
      (alerts) {
        final filteredAlerts = alerts
            .where((alert) => alert.priority == priority && !alert.isDismissed)
            .toList();
        return Right(filteredAlerts);
      },
    );
  }

  /// Get active (non-dismissed) alerts
  Future<Either<Failure, List<DashboardAlert>>> getActiveAlerts() async {
    final alertsResult = await repository.getDashboardAlerts();
    
    return alertsResult.fold(
      (failure) => Left(failure),
      (alerts) {
        final activeAlerts = alerts
            .where((alert) => !alert.isDismissed)
            .toList();
        return Right(activeAlerts);
      },
    );
  }

  /// Get recent alerts (created within last 24 hours)
  Future<Either<Failure, List<DashboardAlert>>> getRecentAlerts() async {
    final alertsResult = await repository.getDashboardAlerts();
    
    return alertsResult.fold(
      (failure) => Left(failure),
      (alerts) {
        final recentAlerts = alerts
            .where((alert) => alert.isRecent && !alert.isDismissed)
            .toList();
        return Right(recentAlerts);
      },
    );
  }

  /// Dismiss an alert
  Future<Either<Failure, bool>> dismissAlert(String alertId) async {
    if (alertId.isEmpty) {
      return Left(Failure.invalidInput(message: 'Alert ID cannot be empty'));
    }

    return await repository.dismissAlert(alertId);
  }

  /// Add a new alert
  Future<Either<Failure, DashboardAlert>> addAlert({
    required String title,
    required String message,
    required AlertPriority priority,
    String? actionLabel,
    String? actionRoute,
    Map<String, dynamic>? metadata,
  }) async {
    if (title.isEmpty || message.isEmpty) {
      return Left(Failure.invalidInput(message: 'Title and message are required'));
    }

    final alert = DashboardAlert(
      id: 'alert_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      message: message,
      priority: priority,
      createdAt: DateTime.now(),
      actionLabel: actionLabel,
      actionRoute: actionRoute,
      metadata: metadata,
    );

    return await repository.addAlert(alert);
  }

  /// Clear all dismissed alerts
  Future<Either<Failure, int>> clearDismissedAlerts() async {
    return await repository.clearDismissedAlerts();
  }

  /// Check if dashboard data is stale and needs refresh
  Future<Either<Failure, bool>> needsRefresh() async {
    final dashboardResult = await repository.getDashboardData();
    
    return dashboardResult.fold(
      (failure) => Left(failure),
      (dashboard) => Right(dashboard.isStale),
    );
  }

  /// Get comprehensive dashboard status
  Future<Either<Failure, DashboardStatus>> getStatus() async {
    final dashboardResult = await repository.getDashboardData();
    
    return dashboardResult.fold(
      (failure) => Left(failure),
      (dashboard) async {
        final activeAlertsResult = await getActiveAlerts();
        
        return activeAlertsResult.fold(
          (failure) => Left(failure),
          (activeAlerts) {
            return Right(DashboardStatus(
              data: dashboard,
              activeAlerts: activeAlerts,
              isStale: dashboard.isStale,
              hasHighPriorityAlerts: activeAlerts.any(
                (alert) => alert.priority == AlertPriority.high || 
                           alert.priority == AlertPriority.critical,
              ),
            ));
          },
        );
      },
    );
  }
}

/// Comprehensive dashboard status
class DashboardStatus {
  final DashboardData data;
  final List<DashboardAlert> activeAlerts;
  final bool isStale;
  final bool hasHighPriorityAlerts;

  const DashboardStatus({
    required this.data,
    required this.activeAlerts,
    required this.isStale,
    required this.hasHighPriorityAlerts,
  });

  /// Check if dashboard needs attention
  bool get needsAttention => isStale || hasHighPriorityAlerts;

  /// Get alert count by priority
  int getAlertCountByPriority(AlertPriority priority) {
    return activeAlerts.where((alert) => alert.priority == priority).length;
  }

  /// Get total active alert count
  int get totalActiveAlerts => activeAlerts.length;
}

import 'package:freezed_annotation/freezed_annotation.dart';
import 'auth_session.dart';
import 'user.dart';

part 'auth_result.freezed.dart';
part 'auth_result.g.dart';

/// Domain entity representing the result of an authentication operation
@freezed
sealed class AuthResult with _$AuthResult {
  const AuthResult._();

  const factory AuthResult({
    AuthUser? user,
    AuthSession? session,
    required bool isSuccess,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) = _AuthResult;

  factory AuthResult.fromJson(Map<String, dynamic> json) => _$AuthResultFromJson(json);

  /// Create a successful auth result
  factory AuthResult.success({
    AuthUser? user,
    AuthSession? session,
    Map<String, dynamic>? metadata,
  }) {
    return AuthResult(
      user: user,
      session: session,
      isSuccess: true,
      metadata: metadata,
    );
  }

  /// Create a failed auth result
  factory AuthResult.failure({
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return AuthResult(
      isSuccess: false,
      errorMessage: errorMessage,
      metadata: metadata,
    );
  }

  /// Create an AuthResult from Supabase AuthResponse
  factory AuthResult.fromSupabaseResponse(dynamic supabaseResponse) {
    return AuthResult(
      user: supabaseResponse.user != null 
          ? AuthUser.fromSupabaseUser(supabaseResponse.user)
          : null,
      session: supabaseResponse.session != null 
          ? AuthSession.fromSupabaseSession(supabaseResponse.session)
          : null,
      isSuccess: supabaseResponse.user != null,
    );
  }

  /// Check if authentication was successful and has valid session
  bool get hasValidSession => isSuccess && session != null && session!.isValid;

  /// Check if authentication was successful and has user
  bool get hasUser => isSuccess && user != null;
}

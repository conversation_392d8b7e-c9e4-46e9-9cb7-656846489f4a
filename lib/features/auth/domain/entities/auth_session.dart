import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_session.freezed.dart';
part 'auth_session.g.dart';

/// Domain entity representing an authentication session
@freezed
sealed class AuthSession with _$AuthSession {
  const AuthSession._();

  const factory AuthSession({
    required String accessToken,
    required String refreshToken,
    required DateTime expiresAt,
    required String tokenType,
    String? providerToken,
    String? providerRefreshToken,
    Map<String, dynamic>? metadata,
  }) = _AuthSession;

  factory AuthSession.fromJson(Map<String, dynamic> json) => _$AuthSessionFromJson(json);

  /// Create an AuthSession from Supabase Session
  factory AuthSession.fromSupabaseSession(dynamic supabaseSession) {
    return AuthSession(
      accessToken: supabaseSession.accessToken,
      refreshToken: supabaseSession.refreshToken,
      expiresAt: DateTime.fromMillisecondsSinceEpoch(supabaseSession.expiresAt * 1000),
      tokenType: supabaseSession.tokenType ?? 'bearer',
      providerToken: supabaseSession.providerToken,
      providerRefreshToken: supabaseSession.providerRefreshToken,
      metadata: supabaseSession.user?.userMetadata as Map<String, dynamic>?,
    );
  }

  /// Check if session is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if session is about to expire (within 5 minutes)
  bool get isAboutToExpire => 
      DateTime.now().isAfter(expiresAt.subtract(const Duration(minutes: 5)));

  /// Get time until expiration
  Duration get timeUntilExpiration => expiresAt.difference(DateTime.now());

  /// Check if session is valid (not expired)
  bool get isValid => !isExpired;
}

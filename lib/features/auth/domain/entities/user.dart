import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

/// Domain entity representing a user in the authentication system
@freezed
sealed class AuthUser with _$AuthUser {
  const AuthUser._();

  const factory AuthUser({
    required String id,
    required String email,
    String? name,
    String? avatarUrl,
    required DateTime createdAt,
    DateTime? lastSignInAt,
    Map<String, dynamic>? metadata,
    @Default(false) bool isEmailConfirmed,
  }) = _AuthUser;

  factory AuthUser.fromJson(Map<String, dynamic> json) => _$AuthUserFromJson(json);

  /// Create an AuthUser from Supabase User
  factory AuthUser.fromSupabaseUser(dynamic supabaseUser) {
    return AuthUser(
      id: supabaseUser.id,
      email: supabaseUser.email ?? '',
      name: supabaseUser.userMetadata?['name'] as String?,
      avatarUrl: supabaseUser.userMetadata?['avatar_url'] as String?,
      createdAt: DateTime.parse(supabaseUser.createdAt),
      lastSignInAt: supabaseUser.lastSignInAt != null 
          ? DateTime.parse(supabaseUser.lastSignInAt)
          : null,
      metadata: supabaseUser.userMetadata as Map<String, dynamic>?,
      isEmailConfirmed: supabaseUser.emailConfirmedAt != null,
    );
  }

  /// Get display name (name or email)
  String get displayName => name ?? email;

  /// Check if user has complete profile
  bool get hasCompleteProfile => name != null && name!.isNotEmpty;
}

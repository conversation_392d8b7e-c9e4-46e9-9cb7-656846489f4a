// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_credentials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
AuthCredentials _$AuthCredentialsFromJson(
  Map<String, dynamic> json
) {
        switch (json['runtimeType']) {
                  case 'emailPassword':
          return EmailPasswordCredentials.fromJson(
            json
          );
                case 'oauth':
          return OAuthCredentials.fromJson(
            json
          );
                case 'anonymous':
          return AnonymousCredentials.fromJson(
            json
          );
        
          default:
            throw CheckedFromJsonException(
  json,
  'runtimeType',
  'AuthCredentials',
  'Invalid union type "${json['runtimeType']}"!'
);
        }
      
}

/// @nodoc
mixin _$AuthCredentials {



  /// Serializes this AuthCredentials to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthCredentials);
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AuthCredentials()';
}


}

/// @nodoc
class $AuthCredentialsCopyWith<$Res>  {
$AuthCredentialsCopyWith(AuthCredentials _, $Res Function(AuthCredentials) __);
}


/// Adds pattern-matching-related methods to [AuthCredentials].
extension AuthCredentialsPatterns on AuthCredentials {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( EmailPasswordCredentials value)?  emailPassword,TResult Function( OAuthCredentials value)?  oauth,TResult Function( AnonymousCredentials value)?  anonymous,required TResult orElse(),}){
final _that = this;
switch (_that) {
case EmailPasswordCredentials() when emailPassword != null:
return emailPassword(_that);case OAuthCredentials() when oauth != null:
return oauth(_that);case AnonymousCredentials() when anonymous != null:
return anonymous(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( EmailPasswordCredentials value)  emailPassword,required TResult Function( OAuthCredentials value)  oauth,required TResult Function( AnonymousCredentials value)  anonymous,}){
final _that = this;
switch (_that) {
case EmailPasswordCredentials():
return emailPassword(_that);case OAuthCredentials():
return oauth(_that);case AnonymousCredentials():
return anonymous(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( EmailPasswordCredentials value)?  emailPassword,TResult? Function( OAuthCredentials value)?  oauth,TResult? Function( AnonymousCredentials value)?  anonymous,}){
final _that = this;
switch (_that) {
case EmailPasswordCredentials() when emailPassword != null:
return emailPassword(_that);case OAuthCredentials() when oauth != null:
return oauth(_that);case AnonymousCredentials() when anonymous != null:
return anonymous(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( String email,  String password)?  emailPassword,TResult Function( String provider,  String token,  String? refreshToken,  Map<String, dynamic>? metadata)?  oauth,TResult Function()?  anonymous,required TResult orElse(),}) {final _that = this;
switch (_that) {
case EmailPasswordCredentials() when emailPassword != null:
return emailPassword(_that.email,_that.password);case OAuthCredentials() when oauth != null:
return oauth(_that.provider,_that.token,_that.refreshToken,_that.metadata);case AnonymousCredentials() when anonymous != null:
return anonymous();case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( String email,  String password)  emailPassword,required TResult Function( String provider,  String token,  String? refreshToken,  Map<String, dynamic>? metadata)  oauth,required TResult Function()  anonymous,}) {final _that = this;
switch (_that) {
case EmailPasswordCredentials():
return emailPassword(_that.email,_that.password);case OAuthCredentials():
return oauth(_that.provider,_that.token,_that.refreshToken,_that.metadata);case AnonymousCredentials():
return anonymous();}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( String email,  String password)?  emailPassword,TResult? Function( String provider,  String token,  String? refreshToken,  Map<String, dynamic>? metadata)?  oauth,TResult? Function()?  anonymous,}) {final _that = this;
switch (_that) {
case EmailPasswordCredentials() when emailPassword != null:
return emailPassword(_that.email,_that.password);case OAuthCredentials() when oauth != null:
return oauth(_that.provider,_that.token,_that.refreshToken,_that.metadata);case AnonymousCredentials() when anonymous != null:
return anonymous();case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class EmailPasswordCredentials extends AuthCredentials {
  const EmailPasswordCredentials({required this.email, required this.password, final  String? $type}): $type = $type ?? 'emailPassword',super._();
  factory EmailPasswordCredentials.fromJson(Map<String, dynamic> json) => _$EmailPasswordCredentialsFromJson(json);

 final  String email;
 final  String password;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of AuthCredentials
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmailPasswordCredentialsCopyWith<EmailPasswordCredentials> get copyWith => _$EmailPasswordCredentialsCopyWithImpl<EmailPasswordCredentials>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmailPasswordCredentialsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmailPasswordCredentials&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,password);

@override
String toString() {
  return 'AuthCredentials.emailPassword(email: $email, password: $password)';
}


}

/// @nodoc
abstract mixin class $EmailPasswordCredentialsCopyWith<$Res> implements $AuthCredentialsCopyWith<$Res> {
  factory $EmailPasswordCredentialsCopyWith(EmailPasswordCredentials value, $Res Function(EmailPasswordCredentials) _then) = _$EmailPasswordCredentialsCopyWithImpl;
@useResult
$Res call({
 String email, String password
});




}
/// @nodoc
class _$EmailPasswordCredentialsCopyWithImpl<$Res>
    implements $EmailPasswordCredentialsCopyWith<$Res> {
  _$EmailPasswordCredentialsCopyWithImpl(this._self, this._then);

  final EmailPasswordCredentials _self;
  final $Res Function(EmailPasswordCredentials) _then;

/// Create a copy of AuthCredentials
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? email = null,Object? password = null,}) {
  return _then(EmailPasswordCredentials(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
@JsonSerializable()

class OAuthCredentials extends AuthCredentials {
  const OAuthCredentials({required this.provider, required this.token, this.refreshToken, final  Map<String, dynamic>? metadata, final  String? $type}): _metadata = metadata,$type = $type ?? 'oauth',super._();
  factory OAuthCredentials.fromJson(Map<String, dynamic> json) => _$OAuthCredentialsFromJson(json);

 final  String provider;
 final  String token;
 final  String? refreshToken;
 final  Map<String, dynamic>? _metadata;
 Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of AuthCredentials
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OAuthCredentialsCopyWith<OAuthCredentials> get copyWith => _$OAuthCredentialsCopyWithImpl<OAuthCredentials>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OAuthCredentialsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OAuthCredentials&&(identical(other.provider, provider) || other.provider == provider)&&(identical(other.token, token) || other.token == token)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,provider,token,refreshToken,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'AuthCredentials.oauth(provider: $provider, token: $token, refreshToken: $refreshToken, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $OAuthCredentialsCopyWith<$Res> implements $AuthCredentialsCopyWith<$Res> {
  factory $OAuthCredentialsCopyWith(OAuthCredentials value, $Res Function(OAuthCredentials) _then) = _$OAuthCredentialsCopyWithImpl;
@useResult
$Res call({
 String provider, String token, String? refreshToken, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$OAuthCredentialsCopyWithImpl<$Res>
    implements $OAuthCredentialsCopyWith<$Res> {
  _$OAuthCredentialsCopyWithImpl(this._self, this._then);

  final OAuthCredentials _self;
  final $Res Function(OAuthCredentials) _then;

/// Create a copy of AuthCredentials
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? provider = null,Object? token = null,Object? refreshToken = freezed,Object? metadata = freezed,}) {
  return _then(OAuthCredentials(
provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as String,token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc
@JsonSerializable()

class AnonymousCredentials extends AuthCredentials {
  const AnonymousCredentials({final  String? $type}): $type = $type ?? 'anonymous',super._();
  factory AnonymousCredentials.fromJson(Map<String, dynamic> json) => _$AnonymousCredentialsFromJson(json);



@JsonKey(name: 'runtimeType')
final String $type;



@override
Map<String, dynamic> toJson() {
  return _$AnonymousCredentialsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AnonymousCredentials);
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AuthCredentials.anonymous()';
}


}




// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_credentials.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmailPasswordCredentials _$EmailPasswordCredentialsFromJson(
  Map<String, dynamic> json,
) => EmailPasswordCredentials(
  email: json['email'] as String,
  password: json['password'] as String,
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$EmailPasswordCredentialsToJson(
  EmailPasswordCredentials instance,
) => <String, dynamic>{
  'email': instance.email,
  'password': instance.password,
  'runtimeType': instance.$type,
};

OAuthCredentials _$OAuthCredentialsFromJson(Map<String, dynamic> json) =>
    OAuthCredentials(
      provider: json['provider'] as String,
      token: json['token'] as String,
      refreshToken: json['refreshToken'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$OAuthCredentialsToJson(OAuthCredentials instance) =>
    <String, dynamic>{
      'provider': instance.provider,
      'token': instance.token,
      'refreshToken': instance.refreshToken,
      'metadata': instance.metadata,
      'runtimeType': instance.$type,
    };

AnonymousCredentials _$AnonymousCredentialsFromJson(
  Map<String, dynamic> json,
) => AnonymousCredentials($type: json['runtimeType'] as String?);

Map<String, dynamic> _$AnonymousCredentialsToJson(
  AnonymousCredentials instance,
) => <String, dynamic>{'runtimeType': instance.$type};

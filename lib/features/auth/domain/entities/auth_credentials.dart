import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_credentials.freezed.dart';
part 'auth_credentials.g.dart';

/// Domain entity representing authentication credentials
@freezed
sealed class AuthCredentials with _$AuthCredentials {
  const AuthCredentials._();

  /// Email and password credentials
  const factory AuthCredentials.emailPassword({
    required String email,
    required String password,
  }) = EmailPasswordCredentials;

  /// OAuth credentials
  const factory AuthCredentials.oauth({
    required String provider,
    required String token,
    String? refreshToken,
    Map<String, dynamic>? metadata,
  }) = OAuthCredentials;

  /// Anonymous credentials
  const factory AuthCredentials.anonymous() = AnonymousCredentials;

  factory AuthCredentials.fromJson(Map<String, dynamic> json) => _$AuthCredentialsFromJson(json);

  /// Validate credentials
  bool get isValid {
    return when(
      emailPassword: (email, password) => 
          email.isNotEmpty && password.isNotEmpty && _isValidEmail(email),
      oauth: (provider, token, refreshToken, metadata) => 
          provider.isNotEmpty && token.isNotEmpty,
      anonymous: () => true,
    );
  }

  /// Get credential type as string
  String get type {
    return when(
      emailPassword: (email, password) => 'email_password',
      oauth: (provider, token, refreshToken, metadata) => 'oauth_$provider',
      anonymous: () => 'anonymous',
    );
  }

  /// Validate email format
  static bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }
}

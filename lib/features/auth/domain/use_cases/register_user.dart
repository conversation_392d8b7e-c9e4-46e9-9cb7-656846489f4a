import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_credentials.dart';
import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';

/// Use case for registering a new user
class RegisterUser {
  final AuthRepository repository;

  RegisterUser(this.repository);

  /// Execute registration with email and password
  Future<Either<Failure, AuthResult>> executeWithEmailPassword({
    required String email,
    required String password,
    String? name,
    Map<String, dynamic>? additionalMetadata,
  }) async {
    // Validate input
    if (email.isEmpty || password.isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Email and password are required'),
      );
    }

    if (password.length < 6) {
      return Left(
        Failure.invalidInput(message: 'Password must be at least 6 characters'),
      );
    }

    // Create credentials
    final credentials = AuthCredentials.emailPassword(
      email: email.trim(),
      password: password,
    );

    // Validate credentials
    if (!credentials.isValid) {
      return Left(Failure.invalidInput(message: 'Invalid email format'));
    }

    // Prepare metadata
    final metadata = <String, dynamic>{};
    if (name != null && name.isNotEmpty) {
      metadata['name'] = name.trim();
    }
    if (additionalMetadata != null) {
      metadata.addAll(additionalMetadata);
    }

    // Execute registration
    return await repository.signUp(
      credentials: credentials,
      metadata: metadata.isNotEmpty ? metadata : null,
    );
  }

  /// Execute registration with OAuth
  Future<Either<Failure, AuthResult>> executeWithOAuth({
    required String provider,
    required String token,
    String? refreshToken,
    Map<String, dynamic>? metadata,
  }) async {
    // Validate input
    if (provider.isEmpty || token.isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Provider and token are required'),
      );
    }

    // Create credentials
    final credentials = AuthCredentials.oauth(
      provider: provider,
      token: token,
      refreshToken: refreshToken,
      metadata: metadata,
    );

    // Execute registration
    return await repository.signUp(
      credentials: credentials,
      metadata: metadata,
    );
  }
}

import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for logging out a user
class LogoutUser {
  final AuthRepository repository;

  LogoutUser(this.repository);

  /// Execute logout operation
  Future<Either<Failure, bool>> execute() async {
    try {
      // Check if user is authenticated before attempting logout
      final isAuthenticatedResult = await repository.isAuthenticated();
      
      return isAuthenticatedResult.fold(
        (failure) => Left(failure),
        (isAuthenticated) async {
          if (!isAuthenticated) {
            // User is already logged out
            return const Right(true);
          }

          // Perform logout
          return await repository.signOut();
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Unexpected error during logout: $e'));
    }
  }
}

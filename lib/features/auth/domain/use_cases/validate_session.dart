import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_session.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for validating the current authentication session
class ValidateSession {
  final AuthRepository repository;

  ValidateSession(this.repository);

  /// Execute session validation
  Future<Either<Failure, SessionValidationResult>> execute() async {
    try {
      // Check if user is authenticated
      final isAuthenticatedResult = await repository.isAuthenticated();
      
      return isAuthenticatedResult.fold(
        (failure) => Left(failure),
        (isAuthenticated) async {
          if (!isAuthenticated) {
            return const Right(SessionValidationResult(
              isValid: false,
              reason: 'User is not authenticated',
            ));
          }

          // Get current session
          final currentSessionResult = await repository.getCurrentSession();
          
          return currentSessionResult.fold(
            (failure) => Left(failure),
            (currentSession) async {
              if (currentSession == null) {
                return const Right(SessionValidationResult(
                  isValid: false,
                  reason: 'No active session found',
                ));
              }

              // Check session validity
              if (currentSession.isExpired) {
                return const Right(SessionValidationResult(
                  isValid: false,
                  reason: 'Session has expired',
                  session: null,
                ));
              }

              // Get current user
              final currentUserResult = await repository.getCurrentUser();
              
              return currentUserResult.fold(
                (failure) => Left(failure),
                (currentUser) {
                  return Right(SessionValidationResult(
                    isValid: true,
                    reason: 'Session is valid',
                    session: currentSession,
                    user: currentUser,
                    isAboutToExpire: currentSession.isAboutToExpire,
                  ));
                },
              );
            },
          );
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Unexpected error during session validation: $e'));
    }
  }

  /// Quick validation check (returns boolean)
  Future<Either<Failure, bool>> isValid() async {
    final result = await execute();
    return result.fold(
      (failure) => Left(failure),
      (validationResult) => Right(validationResult.isValid),
    );
  }
}

/// Result of session validation
class SessionValidationResult {
  final bool isValid;
  final String reason;
  final AuthSession? session;
  final AuthUser? user;
  final bool isAboutToExpire;

  const SessionValidationResult({
    required this.isValid,
    required this.reason,
    this.session,
    this.user,
    this.isAboutToExpire = false,
  });
}

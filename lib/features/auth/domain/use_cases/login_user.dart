import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_credentials.dart';
import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';

/// Use case for logging in a user
class LoginUser {
  final AuthRepository repository;

  LoginUser(this.repository);

  /// Execute login with email and password
  Future<Either<Failure, AuthResult>> executeWithEmailPassword({
    required String email,
    required String password,
  }) async {
    // Validate input
    if (email.isEmpty || password.isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Email and password are required'),
      );
    }

    // Create credentials
    final credentials = AuthCredentials.emailPassword(
      email: email.trim(),
      password: password,
    );

    // Validate credentials
    if (!credentials.isValid) {
      return Left(Failure.invalidInput(message: 'Invalid email format'));
    }

    // Execute login
    return await repository.signIn(credentials: credentials);
  }

  /// Execute login with OAuth
  Future<Either<Failure, AuthResult>> executeWithOAuth({
    required String provider,
    required String token,
    String? refreshToken,
    Map<String, dynamic>? metadata,
  }) async {
    // Validate input
    if (provider.isEmpty || token.isEmpty) {
      return Left(
        Failure.invalidInput(message: 'Provider and token are required'),
      );
    }

    // Create credentials
    final credentials = AuthCredentials.oauth(
      provider: provider,
      token: token,
      refreshToken: refreshToken,
      metadata: metadata,
    );

    // Execute login
    return await repository.signIn(credentials: credentials);
  }

  /// Execute anonymous login
  Future<Either<Failure, AuthResult>> executeAnonymous() async {
    final credentials = const AuthCredentials.anonymous();
    return await repository.signIn(credentials: credentials);
  }
}

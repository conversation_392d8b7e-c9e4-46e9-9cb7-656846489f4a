import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_session.dart';
import '../repositories/auth_repository.dart';

/// Use case for refreshing the current authentication session
class RefreshSession {
  final AuthRepository repository;

  RefreshSession(this.repository);

  /// Execute session refresh
  Future<Either<Failure, AuthSession>> execute() async {
    try {
      // Check if user is authenticated
      final isAuthenticatedResult = await repository.isAuthenticated();
      
      return isAuthenticatedResult.fold(
        (failure) => Left(failure),
        (isAuthenticated) async {
          if (!isAuthenticated) {
            return Left(Failure.businessLogic(message: 'User is not authenticated'));
          }

          // Get current session to check if refresh is needed
          final currentSessionResult = await repository.getCurrentSession();
          
          return currentSessionResult.fold(
            (failure) => Left(failure),
            (currentSession) async {
              if (currentSession == null) {
                return Left(Failure.businessLogic(message: 'No active session found'));
              }

              // Check if session is still valid
              if (currentSession.isValid && !currentSession.isAboutToExpire) {
                // Session is still valid, return current session
                return Right(currentSession);
              }

              // Session needs refresh
              return await repository.refreshSession();
            },
          );
        },
      );
    } catch (e) {
      return Left(Failure.unexpected(message: 'Unexpected error during session refresh: $e'));
    }
  }

  /// Check if session needs refresh
  Future<Either<Failure, bool>> needsRefresh() async {
    final currentSessionResult = await repository.getCurrentSession();
    
    return currentSessionResult.fold(
      (failure) => Left(failure),
      (currentSession) {
        if (currentSession == null) {
          return const Right(false); // No session to refresh
        }
        
        return Right(currentSession.isExpired || currentSession.isAboutToExpire);
      },
    );
  }
}

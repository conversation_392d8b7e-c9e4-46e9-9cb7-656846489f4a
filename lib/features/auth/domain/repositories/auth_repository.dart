import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_credentials.dart';
import '../entities/auth_result.dart';
import '../entities/auth_session.dart';
import '../entities/user.dart';

/// Repository interface for authentication operations
abstract class AuthRepository {
  /// Get the current authenticated user
  Future<Either<Failure, AuthUser?>> getCurrentUser();

  /// Get the current session
  Future<Either<Failure, AuthSession?>> getCurrentSession();

  /// Check if user is currently authenticated
  Future<Either<Failure, bool>> isAuthenticated();

  /// Sign up with credentials
  Future<Either<Failure, AuthResult>> signUp({
    required AuthCredentials credentials,
    Map<String, dynamic>? metadata,
  });

  /// Sign in with credentials
  Future<Either<Failure, AuthResult>> signIn({
    required AuthCredentials credentials,
  });

  /// Sign out the current user
  Future<Either<Failure, bool>> signOut();

  /// Refresh the current session
  Future<Either<Failure, AuthSession>> refreshSession();

  /// Reset password for email
  Future<Either<Failure, bool>> resetPassword({
    required String email,
    String? redirectTo,
  });

  /// Update user profile
  Future<Either<Failure, AuthUser>> updateProfile({
    String? name,
    String? avatarUrl,
    Map<String, dynamic>? metadata,
  });

  /// Change user password
  Future<Either<Failure, bool>> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  /// Verify email with token
  Future<Either<Failure, AuthResult>> verifyEmail({
    required String token,
    required String email,
  });

  /// Listen to authentication state changes
  Stream<Either<Failure, AuthResult>> get authStateChanges;

  /// Initialize the authentication repository
  Future<Either<Failure, bool>> initialize();

  /// Check if the repository is initialized
  bool get isInitialized;
}

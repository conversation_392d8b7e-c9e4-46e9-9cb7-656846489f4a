/// Auth feature barrel export file
///
/// This file provides a single import point for all authentication functionality:
/// ```dart
/// import 'package:bidtrakr/features/auth/auth.dart';
/// ```
library;

// Data layer
export 'data/repositories/auth_repository_impl.dart';

// Domain layer
export 'domain/entities/auth_credentials.dart';
export 'domain/entities/auth_result.dart';
export 'domain/entities/auth_session.dart';
export 'domain/entities/user.dart';
export 'domain/repositories/auth_repository.dart';
export 'domain/use_cases/login_user.dart';
export 'domain/use_cases/logout_user.dart';
export 'domain/use_cases/refresh_session.dart';
export 'domain/use_cases/register_user.dart';
export 'domain/use_cases/validate_session.dart';

// Presentation layer - providers
export 'presentation/providers/auth_providers.dart';

// Presentation layer - screens
export 'presentation/screens/forgot_password_screen.dart';
export 'presentation/screens/login_screen.dart';
export 'presentation/screens/signup_screen.dart';

// Presentation layer - widgets
export 'presentation/widgets/auth_button.dart';
export 'presentation/widgets/auth_text_field.dart';
export 'presentation/widgets/auth_wrapper.dart';

import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

import '../../../../core/errors/failures.dart';
import '../../../../core/services/auth/auth_repository.dart' as core_auth;
import '../../domain/entities/auth_credentials.dart';
import '../../domain/entities/auth_result.dart';
import '../../domain/entities/auth_session.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';

/// Implementation of AuthRepository using the existing core auth repository
class AuthRepositoryImpl implements AuthRepository {
  final core_auth.AuthRepository _coreAuthRepository;
  StreamController<Either<Failure, AuthResult>>? _authStateController;

  AuthRepositoryImpl(this._coreAuthRepository);

  @override
  bool get isInitialized => _coreAuthRepository.isSupabaseInitialized();

  @override
  Future<Either<Failure, bool>> initialize() async {
    try {
      await _coreAuthRepository.initialize();
      _setupAuthStateListener();
      return const Right(true);
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to initialize auth repository: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, AuthUser?>> getCurrentUser() async {
    try {
      final user = _coreAuthRepository.currentUser;
      if (user == null) {
        return const Right(null);
      }
      return Right(AuthUser.fromSupabaseUser(user));
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to get current user: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, AuthSession?>> getCurrentSession() async {
    try {
      final session = _coreAuthRepository.getCurrentSession();
      if (session == null) {
        return const Right(null);
      }
      return Right(AuthSession.fromSupabaseSession(session));
    } catch (e) {
      return Left(
        Failure.unexpected(message: 'Failed to get current session: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    try {
      return Right(_coreAuthRepository.isAuthenticated);
    } catch (e) {
      return Left(
        Failure.unexpected(
          message: 'Failed to check authentication status: $e',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, AuthResult>> signUp({
    required AuthCredentials credentials,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      return credentials.when(
        emailPassword: (email, password) async {
          final response = await _coreAuthRepository.signUp(
            email: email,
            password: password,
            data: metadata,
          );
          return Right(AuthResult.fromSupabaseResponse(response));
        },
        oauth: (provider, token, refreshToken, oauthMetadata) async {
          // OAuth signup would need to be implemented in core auth repository
          return Left(
            Failure.unexpected(message: 'OAuth signup not implemented'),
          );
        },
        anonymous: () async {
          return Left(
            Failure.unexpected(message: 'Anonymous signup not implemented'),
          );
        },
      );
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to sign up: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> signIn({
    required AuthCredentials credentials,
  }) async {
    try {
      return credentials.when(
        emailPassword: (email, password) async {
          final response = await _coreAuthRepository.signIn(
            email: email,
            password: password,
          );
          return Right(AuthResult.fromSupabaseResponse(response));
        },
        oauth: (provider, token, refreshToken, metadata) async {
          return Left(
            Failure.unexpected(message: 'OAuth signin not implemented'),
          );
        },
        anonymous: () async {
          return Left(
            Failure.unexpected(message: 'Anonymous signin not implemented'),
          );
        },
      );
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to sign in: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> signOut() async {
    try {
      await _coreAuthRepository.signOut();
      return const Right(true);
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to sign out: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthSession>> refreshSession() async {
    try {
      final session = await _coreAuthRepository.refreshSession();
      if (session == null) {
        return Left(
          Failure.businessLogic(message: 'Failed to refresh session'),
        );
      }
      return Right(AuthSession.fromSupabaseSession(session));
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to refresh session: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> resetPassword({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await _coreAuthRepository.resetPassword(email);
      return const Right(true);
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to reset password: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUser>> updateProfile({
    String? name,
    String? avatarUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      if (name != null) updateData['name'] = name;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;
      if (metadata != null) updateData.addAll(metadata);

      // TODO: Implement updateUser in core AuthRepository
      return Left(
        Failure.unexpected(message: 'Update profile not implemented'),
      );
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to update profile: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // TODO: Implement updateUser in core AuthRepository
      return Left(
        Failure.unexpected(message: 'Change password not implemented'),
      );
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to change password: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> verifyEmail({
    required String token,
    required String email,
  }) async {
    try {
      // TODO: Implement verifyOTP in core AuthRepository
      return Left(Failure.unexpected(message: 'Verify email not implemented'));
    } on supabase.AuthException catch (e) {
      return Left(Failure.businessLogic(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: 'Failed to verify email: $e'));
    }
  }

  @override
  Stream<Either<Failure, AuthResult>> get authStateChanges {
    _authStateController ??=
        StreamController<Either<Failure, AuthResult>>.broadcast();
    return _authStateController!.stream;
  }

  void _setupAuthStateListener() {
    _authStateController ??=
        StreamController<Either<Failure, AuthResult>>.broadcast();

    _coreAuthRepository.onAuthStateChange.listen(
      (authState) {
        try {
          final authResult = AuthResult(
            user: authState.session?.user != null
                ? AuthUser.fromSupabaseUser(authState.session!.user)
                : null,
            session: authState.session != null
                ? AuthSession.fromSupabaseSession(authState.session)
                : null,
            isSuccess: authState.session != null,
          );
          _authStateController?.add(Right(authResult));
        } catch (e) {
          _authStateController?.add(
            Left(Failure.unexpected(message: 'Auth state change error: $e')),
          );
        }
      },
      onError: (error) {
        _authStateController?.add(
          Left(Failure.unexpected(message: 'Auth state stream error: $error')),
        );
      },
    );
  }

  void dispose() {
    _authStateController?.close();
  }
}

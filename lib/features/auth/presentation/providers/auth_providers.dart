import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/providers/provider_templates.dart';
import '../../../../core/services/auth/auth_providers.dart' as core_auth;
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/use_cases/login_user.dart';
import '../../domain/use_cases/logout_user.dart';
import '../../domain/use_cases/refresh_session.dart';
import '../../domain/use_cases/register_user.dart';
import '../../domain/use_cases/validate_session.dart';

/// Provider for the auth repository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final coreAuthRepository = ref.watch(core_auth.authRepositoryProvider);
  return AuthRepositoryImpl(coreAuthRepository);
});

/// Provider for the login user use case
final loginUserProvider = Provider<LoginUser>((ref) {
  return createUseCaseProvider<LoginUser, AuthRepository>(
    ref,
    authRepositoryProvider,
    (repository) => LoginUser(repository),
  );
});

/// Provider for the logout user use case
final logoutUserProvider = Provider<LogoutUser>((ref) {
  return createUseCaseProvider<LogoutUser, AuthRepository>(
    ref,
    authRepositoryProvider,
    (repository) => LogoutUser(repository),
  );
});

/// Provider for the register user use case
final registerUserProvider = Provider<RegisterUser>((ref) {
  return createUseCaseProvider<RegisterUser, AuthRepository>(
    ref,
    authRepositoryProvider,
    (repository) => RegisterUser(repository),
  );
});

/// Provider for the refresh session use case
final refreshSessionProvider = Provider<RefreshSession>((ref) {
  return createUseCaseProvider<RefreshSession, AuthRepository>(
    ref,
    authRepositoryProvider,
    (repository) => RefreshSession(repository),
  );
});

/// Provider for the validate session use case
final validateSessionProvider = Provider<ValidateSession>((ref) {
  return createUseCaseProvider<ValidateSession, AuthRepository>(
    ref,
    authRepositoryProvider,
    (repository) => ValidateSession(repository),
  );
});

/// Provider for current user (async)
final currentUserProvider = FutureProvider((ref) async {
  final repository = ref.watch(authRepositoryProvider);
  final result = await repository.getCurrentUser();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (user) => user,
  );
});

/// Provider for current session (async)
final currentSessionProvider = FutureProvider((ref) async {
  final repository = ref.watch(authRepositoryProvider);
  final result = await repository.getCurrentSession();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (session) => session,
  );
});

/// Provider for authentication status (async)
final isAuthenticatedProvider = FutureProvider<bool>((ref) async {
  final repository = ref.watch(authRepositoryProvider);
  final result = await repository.isAuthenticated();
  return result.fold((failure) => false, (isAuthenticated) => isAuthenticated);
});

/// Provider for session validation (async)
final sessionValidationProvider = FutureProvider((ref) async {
  final validateSession = ref.watch(validateSessionProvider);
  final result = await validateSession.execute();
  return result.fold(
    (failure) => throw Exception(failure.message),
    (validationResult) => validationResult,
  );
});

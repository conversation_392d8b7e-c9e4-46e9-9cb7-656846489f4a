import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/core.dart';
import 'core/initialization/app_initializer.dart';
import 'core/services/deep_link_handler.dart';
import 'core/providers/theme_provider.dart';
import 'core/theme/app_dimensions.dart';
import 'core/widgets/lifecycle_handler.dart';
import 'core/widgets/media_query_wrapper.dart';
import 'features/auth/presentation/widgets/auth_wrapper.dart';

// Global key for ScaffoldMessenger to handle snackbars across the app
final GlobalKey<ScaffoldMessengerState> rootScaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

// Note: The syncEnabledProvider is now defined in lib/core/providers/sync_preference_provider.dart

/// Main entry point for the application
void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the app using AppInitializer
  final dbPath = await AppInitializer.initialize();

  // Initialize deep link handling
  await DeepLinkHandler.initialize();

  // Run the app with ProviderScope
  runApp(
    ProviderScope(
      overrides: [
        // Provide the database path so it can be used to recreate the database
        databasePathProvider.overrideWithValue(dbPath),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Initialize dimensions
    AppDimensions.init(context);

    // Use theme providers
    final lightTheme = ref.watch(lightThemeProvider(context));
    final darkTheme = ref.watch(darkThemeProvider(context));

    return MaterialApp(
      title: 'BidTrakr - Driver Performance Tracker',
      debugShowCheckedModeBanner: false,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: ThemeMode.system,
      // Add the global ScaffoldMessengerKey to fix snackbar issues
      scaffoldMessengerKey: rootScaffoldMessengerKey,
      builder: (context, child) {
        // Use MediaQueryWrapper to handle text scaling
        return MediaQueryWrapper(child: child!);
      },
      home: const LifecycleHandler(child: AuthWrapper()),
    );
  }
}

// Note: The sync service provider is now defined in lib/core/services/sync/sync_service.dart

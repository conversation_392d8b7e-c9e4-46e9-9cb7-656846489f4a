Development Guidelines

These guidelines are intended to standardize and ensure the quality, scalability, and maintainability of AI Agent applications, following Clean Architecture principles.

- **Always use English**  
  Use English for all code (including variable, class, and file names), comments, documentation, and UI text.

- **Folder Structure**
  - **Feature-based Structure**: Organize code by features, not by component types.
  - **Domain Layer**: Contains business rules and logic, independent of any framework.
  - **Data Layer**: Contains repository implementations and data sources.
  - **Presentation Layer**: Contains UI and state management logic.

- **Dependency Rule**
  - **Direction of Dependency**: Must always point inward: `Domain ← Data ← Presentation`.
  - **Domain Layer**: Must not depend on any other layer.
  - **Data Layer**: May only depend on the Domain Layer.
  - **Presentation Layer**: May depend on both Domain and Data Layers.

- **Entity and Model**
  - **Domain Entity**: Pure data representations without framework dependencies.
  - **Presentation Model**: Transform domain entities with UI-specific formatting logic.
  - **DTOs**: Use data transfer objects only at Data Layer boundaries for external communication.

- **Repository Pattern**
  - **Repository Interface**: Defined in the Domain Layer.
  - **Repository Implementation**: Implemented in the Data Layer with caching strategies.
  - **Implementation Rules**: 
    - Never expose data-layer specific types to other layers
    - Define cache invalidation rules per repository interface

- **Use Case Pattern**
  - Each use case must follow the **Single Responsibility Principle**, handling one specific operation.

- **Error Handling**
  - Use a **sealed error hierarchy** for type-safe handling:
    ```dart
    @freezed
    class AppError with _$AppError {
      const factory AppError.network() = NetworkError;
      const factory AppError.server(int code) = ServerError;
      const factory AppError.localStorage() = StorageError;
      const factory AppError.validation(String message) = ValidationError;
    }
    ```
  - Apply **Result pattern** (`Result<Success, AppError>`) for all layer boundaries
  - Handle errors in controllers with user-friendly messages

- **Dependency Injection**
  - Use **Riverpod** with these provider priorities:
    1. `StateNotifierProvider` for complex state
    2. `FutureProvider` for async data initialization
    3. `Provider` for static dependencies

- **UI Components**
  - **Widget Composition**: Build reusable components with `const` constructors
  - **Logic Separation**:
    - Business logic in providers
    - Presentation logic in controllers
    - UI rendering in widgets

- **State Management**
  - Use `AsyncValue.when()` for state-dependent UI:
    ```dart
    state.when(
      data: (value) => ItemList(items: value),
      error: (error, _) => ErrorView(error),
      loading: () => LoadingIndicator(),
    )
    ```
  - Prefer `ref.watch` in builders over direct context access

- **Context Safety**
  - Use `mounted` checks before post-async context access:
    ```dart
    if (mounted) {
      setState(() => isLoading = false);
    }
    ```

- **Context Handling**: Use context7.

- **Performance Considerations**
  - **Image Handling**: Use `cached_network_image` with memory cache
  - **List Optimization**: Implement `ListView.separated` with item extent
  - **Selective Rebuilds**: Use `select()` for granular updates:
    ```dart
    ref.watch(provider.select((value) => value.property))
    ```

- **Consistency**
  - **File Naming**:
    - Domain: `feature_entity.dart`, `feature_usecase.dart`
    - Data: `feature_repository_impl.dart`, `feature_data_source.dart`
    - Presentation: `feature_screen.dart`, `feature_controller.dart`
  - **Code Organization**:
    - Keep widget files under 300 lines
    - Split large components into `/widgets` subdirectory
plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.englr.bidtrakr"
    compileSdk = 35  // Android 15 (API level 35) - required by plugins
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.englr.bidtrakr"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21  // Android 5.0 (minimum supported by Flutter)
        targetSdk = 34  // Android 14 (API level 34)
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: For production release, create a proper keystore and signing config:
            // 1. Generate keystore: keytool -genkey -v -keystore bidtrakr-release-key.keystore -alias bidtrakr -keyalg RSA -keysize 2048 -validity 10000
            // 2. Create key.properties file with keystore details
            // 3. Configure signingConfigs.release with keystore information
            // For now, using debug keys for testing the release build fixes
            signingConfig = signingConfigs.getByName("debug")

            // Enable code shrinking, obfuscation, and optimization
            isMinifyEnabled = true
            isShrinkResources = true

            // Use ProGuard rules
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
}

flutter {
    source = "../.."
}
